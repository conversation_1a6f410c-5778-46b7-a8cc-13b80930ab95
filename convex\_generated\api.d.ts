/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as auth from "../auth.js";
import type * as crudHelpers from "../crudHelpers.js";
import type * as http from "../http.js";
import type * as modalityConfigs from "../modalityConfigs.js";
import type * as patients from "../patients.js";
import type * as projects from "../projects.js";
import type * as seed from "../seed.js";
import type * as streamActions from "../streamActions.js";
import type * as streamSessions from "../streamSessions.js";
import type * as streams from "../streams.js";
import type * as timeline from "../timeline.js";
import type * as users from "../users.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  auth: typeof auth;
  crudHelpers: typeof crudHelpers;
  http: typeof http;
  modalityConfigs: typeof modalityConfigs;
  patients: typeof patients;
  projects: typeof projects;
  seed: typeof seed;
  streamActions: typeof streamActions;
  streamSessions: typeof streamSessions;
  streams: typeof streams;
  timeline: typeof timeline;
  users: typeof users;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
