import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { MonitoringEvents, Patients, Projects, projectStatusValidator, Users } from "./schema";
import { omit } from "convex-helpers";

import { asyncMap } from "convex-helpers";
import { Doc } from "./_generated/dataModel";
// Type-safe field selectors
const projectCreateFields = omit(Projects.withoutSystemFields, ["projectCode"]);

// Query to get all projects
export const getProjects = query({
  args: {
    status: v.optional(projectStatusValidator),
    limit: v.optional(v.number()),
  },
  returns: v.array(Projects.doc),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    let query;
    
    if (args.status) {
      query = ctx.db.query("projects").withIndex("by_status", (q) => q.eq("status", args.status!));
    }else{
      query = ctx.db.query("projects");
    }
    //const projects = await getManyFrom(ctx.db, "projects", "by_status", args.status!);
    const projects = await query
      .order("desc")
      .take(args.limit ?? 50);

    return projects;
  },
});

// Query to get all projects
export const getProjectsWithPatient = query({
  args: {
    status: v.optional(projectStatusValidator),
    limit: v.optional(v.number()),
  },
  returns: v.array(
      v.object({
        ...Projects.doc.fields,
        patient: v.object(Patients.doc.fields), 
      })
    ),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    let query;
    
   if (args.status) {
      query = ctx.db.query("projects").withIndex("by_status", (q) => q.eq("status", args.status!));
    }else{
      query = ctx.db.query("projects");
    }
    const projects = await query
          .order("desc")
          .take(args.limit ?? 50);

    const enrichedProjects = await asyncMap(projects, async (project) => {
      const patient = await ctx.db.get(project.patientId);
      // Ensure patient isnot null, otherwise throw
      if (!patient) throw new Error("Patient not found");

      // For optional fields, convert null to undefined
      return {
        ...project,
        patient,
      };
    });
    return enrichedProjects;
  },
});

// Query to get all projects
export const getProjectswithPatientAndTeam = query({
  args: {
    status: v.optional(projectStatusValidator),
    limit: v.optional(v.number()),
  },
  returns: v.array(
      v.object({
        ...Projects.doc.fields,
        patient: v.object(Patients.doc.fields), // Projects will be enriched separately
        primarySurgeon: v.object(Users.doc.fields),	
        anesthesiologist: v.optional(v.object(Users.doc.fields)),
        neurophysiologist: v.optional(v.object(Users.doc.fields)),
      })
    ),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    let query;
    
    if (args.status) {
      query = ctx.db.query("projects").withIndex("by_status", (q) => q.eq("status", args.status!));
    }else{
      query = ctx.db.query("projects");
    }

    const enrichedProjects = await asyncMap(await query.order("desc").take(args.limit ?? 50), async (project) => {
      const patient = await ctx.db.get(project.patientId);
      const primarySurgeon = await ctx.db.get(project.teamMembers.primarySurgeon);
      const anesthesiologist = project.teamMembers.anesthesiologist ? await ctx.db.get(project.teamMembers.anesthesiologist): undefined;
      const neurophysiologist = project.teamMembers.neurophysiologist? await ctx.db.get(project.teamMembers.neurophysiologist) : undefined;
      // Ensure patient and primarySurgeon are not null, otherwise throw
      if (!patient) throw new Error("Patient not found");
      if (!primarySurgeon) throw new Error("Primary surgeon not found");

      // For optional fields, convert null to undefined
      return {
        ...project,
        patient,
        primarySurgeon,
        anesthesiologist: anesthesiologist ?? undefined,
        neurophysiologist: neurophysiologist ?? undefined,
      };
    });

    /*const enrichedProjects = await Promise.all(
      (await query.collect()).map(async (project) => ({
        ...project,
        patient: await ctx.db.get(project.patientId),
        primarySurgeon: await ctx.db.get(project.teamMembers.primarySurgeon),
        anesthesiologist: project.teamMembers.anesthesiologist ? await ctx.db.get(project.teamMembers.anesthesiologist) : undefined,
        neurophysiologist: project.teamMembers.neurophysiologist ? await ctx.db.get(project.teamMembers.neurophysiologist) : undefined,
      })),
    );*/


    return enrichedProjects;
  },
});


// Query to get a single project with all details
export const getProject = query({
  args: { projectId: v.id("projects") },
  returns: v.union(Projects.doc, v.null()),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      return null;
    }
    return project;
  },
});

// Query to get a single project with all details (patient, team, monitoring events)
export const getProjectwithDetails = query({
  args: { projectId: v.id("projects") },
  returns: v.union(
      v.object({
        ...Projects.doc.fields,
        patient: v.object(Patients.doc.fields), 
        primarySurgeon: v.object(Users.doc.fields),	
        anesthesiologist: v.optional(v.object(Users.doc.fields)),
        neurophysiologist: v.optional(v.object(Users.doc.fields)),
        events: v.array(v.object(MonitoringEvents.doc.fields)),
      }),
      v.null()
    ),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
      return null;
    }

    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Project not found");
      return null;
    }
  
    // Get related data
    const patient = await ctx.db.get(project.patientId);
    const primarySurgeon = await ctx.db.get(project.teamMembers.primarySurgeon) ;
    const anesthesiologist = project.teamMembers.anesthesiologist ? await ctx.db.get(project.teamMembers.anesthesiologist) : undefined;
    const neurophysiologist = project.teamMembers.neurophysiologist ? await ctx.db.get(project.teamMembers.neurophysiologist) : undefined;
    
    // Ensure patient and primarySurgeon are not null, otherwise throw
    if (!patient) throw new Error("Patient not found");
    if (!primarySurgeon) throw new Error("Primary surgeon not found");

    // Get monitoring events for this project
    const events = await ctx.db
      .query("monitoringEvents")
      .filter((q) => q.eq(q.field("projectId"), args.projectId))
      .order("asc")
      .collect();

    return {
      ...project,
      patient,
      primarySurgeon,
      anesthesiologist: anesthesiologist ?? undefined,
      neurophysiologist: neurophysiologist ?? undefined,
      events,
    };
  },
});


// Create a new project
export const createProject = mutation({
  args: projectCreateFields,
  returns: v.id("projects"),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Verify patient exists
    const patient = await ctx.db.get(args.patientId);
    if (!patient) {
      throw new Error("Patient not found");
    }

    // Generate project code
    const date = new Date();
    const projectCode = `P${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}${String(date.getDate()).padStart(2, '0')}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

    const projectId = await ctx.db.insert("projects", {
      ...args,
      projectCode,
      status: "scheduled",
      complications: [],
      createdBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return projectId;
  },
});

// Update project status
export const updateProjectStatus = mutation({
  args: {
    projectId: v.id("projects"),
    status: projectStatusValidator,
    actualStart: v.optional(v.number()),
    actualEnd: v.optional(v.number()),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const project = await ctx.db.get(args.projectId);
    if (!project) {
      throw new Error("Project not found");
    }
    const updateData: Partial<Doc<"projects">> = {
      status: args.status,
      updatedAt: Date.now(),
    };

    if (args.actualStart !== undefined) {
      updateData.actualStart = args.actualStart;
    }

    if (args.actualEnd !== undefined) {
      updateData.actualEnd = args.actualEnd;
    }

    await ctx.db.patch(args.projectId, updateData);

    return { success: true };
  },
});

// Get active projects count for dashboard
export const getActiveProjectsCount = query({
  args: {},
  returns: v.number(),
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const activeProjects = await ctx.db
      .query("projects")
      .withIndex("by_status", (q) => q.eq("status", "in-progress"))
      .collect();

    return activeProjects.length;
  },
});

// Get today's scheduled projects
export const getTodaysProjects = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate()).getTime();
    const endOfDay = startOfDay + (24 * 60 * 60 * 1000) - 1;

    const projects = await ctx.db
      .query("projects")
      .filter((q) => 
        q.and(
          q.gte(q.field("scheduledStart"), startOfDay),
          q.lte(q.field("scheduledStart"), endOfDay)
        )
      )
      .order("asc")
      .collect();

    // Enrich with patient data
    const enrichedProjects = await Promise.all(
      projects.map(async (project) => {
        const patient = await ctx.db.get(project.patientId);
        const primarySurgeon = await ctx.db.get(project.teamMembers.primarySurgeon);
        
        return {
          ...project,
          patient,
          primarySurgeon,
        };
      })
    );

    return enrichedProjects;
  },
});