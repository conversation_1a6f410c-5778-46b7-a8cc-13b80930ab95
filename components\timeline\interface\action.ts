import { Doc, Id } from "@/convex/_generated/dataModel";

/**
 * Unified Timeline Event - the single source of truth for all timeline events
 * Extends NFM MonitoringEvent with timeline-specific properties
 * Maps NFM field names to timeline editor expectations
 * @export
 * @interface TimelineEvent
 */
export interface TimelineEvent extends Omit<Doc<"monitoringEvents">, '_id'> {
  /** Timeline editor expects 'id' instead of '_id' */
  id: Id<"monitoringEvents">;

  /** Timeline editor expects 'start' instead of 'startTime' */
  start: number;

  /** Timeline editor expects 'end' instead of 'endTime' */
  end: number;

  /** Effect ID for timeline rendering (derived from eventType or modalityId) */
  effectId: string;

  /** Timeline-specific interaction properties */
  selected?: boolean;
  flexible?: boolean;
  movable?: boolean;
  disable?: boolean;
  minStart?: number;
  maxEnd?: number;

  /** Display properties (merged from NFMTimelineEvent) */
  modalityName?: string;
  modalityColor?: string;
  color?: string;

  /** Interaction properties (merged from NFMTimelineEvent) */
  editable?: boolean;
  deletable?: boolean;
}

/**
 * Timeline Row - extends NFM ModalityConfig with timeline-specific properties
 * Maps NFM field names to timeline editor expectations
 * @export
 * @interface TimelineRow
 */
export interface TimelineRow extends Omit<Doc<"modalityConfigs">, '_id' | 'displayName'> {
  /** Timeline editor expects 'id' instead of '_id' */
  id: Id<"modalityConfigs">;

  /** Timeline editor requires displayName to be a string */
  displayName: string;

  /** List of events in this modality row */
  actions: TimelineEvent[];

  /** Timeline-specific interaction properties */
  rowHeight?: number;
  selected?: boolean;
  classNames?: string[];

  /** Additional display properties */
  isVisible?: boolean;
}