import { action } from "./_generated/server";
import { api } from "./_generated/api";
import { v } from "convex/values";
import { StreamConfigs } from "./schema";
import { pick } from "convex-helpers";
import { 
  PathConfig,
  PathConfigCreate,
  PathConfigUpdate,
  PathsListResponse, 
  listPaths,
  listConfigPaths,
  addPath,
  updatePath,
  deletePath,
  testStreamConnection as testConnection
} from "../lib/mediamtx";
import { Doc } from "./_generated/dataModel";

// Type-safe field selectors
const streamActionFields = pick(StreamConfigs.withoutSystemFields, [
  "pathName", "sourceUrl", "streamType", "description"
]);

// Test existing stream connection
export const testExistingStream = action({
  args: { pathName: v.string() },
  returns: v.object({
    success: v.boolean(),
    message: v.string(),
    details: v.any(),
  }),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    try {
      const pathsList = await listPaths();
      const streamInfo = pathsList.items?.find(item => item.name === args.pathName);
      const isConnected = streamInfo?.ready === true;

      return { 
        success: isConnected,
        message: isConnected ? "Stream is live" : "Stream is not connected",
        details: streamInfo
      };
    } catch (error) {
      return { 
        success: false,
        message: `Error: ${error instanceof Error ? error.message : error}`,
        details: null
      };
    }
  },
});

// List all stream sources - loads primarily from database
export const listStreamSources = action({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx) => {
    try {
      // Get database configurations (primary source)
      const dbConfigs: Doc<"streamConfigs">[] = await ctx.runQuery(api.streams.getAllStreamConfigs);

      // Get live status from MediaMTX
      let livePathsData: PathsListResponse | null = null;
      try {
        livePathsData = await listPaths();
      } catch (error) {
        console.warn('Failed to get live paths from MediaMTX:', error);
      }

      // Update live status and viewer count for each stream
      const streams: Doc<"streamConfigs">[] = await Promise.all(
        dbConfigs.map(async (dbConfig) => {
          const liveInfo = livePathsData?.items?.find(item => item.name === dbConfig.pathName);
          const isLive = liveInfo?.ready === true;
          const viewers = liveInfo?.readers?.length || 0;

          // Update live status in database if it changed
          if (dbConfig.isLive !== isLive || dbConfig.viewers !== viewers) {
            try {
              await ctx.runMutation(api.streams.updateLiveStatus, {
                pathName: dbConfig.pathName,
                isLive,
                viewers,
              });
            } catch (error) {
              console.warn(`Failed to update live status for ${dbConfig.pathName}:`, error);
            }
          }

          return {
            ...dbConfig,
            isLive,
            viewers,
          };
        })
      );

      return streams;
    } catch (error) {
      console.error('Failed to list streams:', error);
      return [];
    }
  },
});

// Configure stream source using the 'add' API call
export const configureStreamSource = action({
  args: streamActionFields,
  returns: v.object({ success: v.boolean(), pathName: v.string() }),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    if (!/^[a-zA-Z0-9._~\-\/]+$/.test(args.pathName)) {
      throw new Error("Invalid path name");
    }

    try {
      // Check if path already exists
      const configData = await listConfigPaths();
      
      if (configData.items && configData.items.some((item: PathConfig) => item.name === args.pathName)) {
        throw new Error(`Path '${args.pathName}' already exists in MediaMTX configuration`);
      }

      // Check if source URL is already in use
      for (const config of configData.items || []) {
        if (config.source === args.sourceUrl) {
          throw new Error(`Source URL is already configured for path '${config.name}'`);
        }
      }

      // Create path configuration (only send relevant fields)
      const pathConfig: PathConfigCreate = {
        name: args.pathName,
        source: args.sourceUrl,
        rtspTransport: "tcp",
      };

      await addPath(args.pathName, pathConfig);

      await ctx.runMutation(api.streams.saveStreamConfig, {
        pathName: args.pathName,
        sourceUrl: args.sourceUrl,
        streamType: args.streamType,
        description: args.description,
        isEnabled: true,
      });

      return { success: true, pathName: args.pathName };
    } catch (error) {
      throw new Error(`Stream configuration failed: ${error instanceof Error ? error.message : error}`);
    }
  },
});

// Test stream connection with new URL
export const testStreamConnection = action({
  args: {
    sourceUrl: v.string(),
  },
  returns: v.object({
    success: v.boolean(),
    message: v.string()
  }),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    try {
      const result = await testConnection(args.sourceUrl);
      return result;
    } catch (error) {
      return { 
        success: false,
        message: `Connection test failed: ${error instanceof Error ? error.message : error}`
      };
    }
  },
});

// Edit stream configuration (renamed from editStreamSource)
export const editStreamConfig = action({
  args: streamActionFields,
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    try {
      const config = await ctx.runQuery(api.streams.getStreamConfig, { pathName: args.pathName });
      if (!config) throw new Error("Stream configuration not found");

      // If enabled, update MediaMTX using patch API (only send relevant fields)
      if (config.isEnabled) {
        const pathConfig: PathConfigUpdate = {
          name: args.pathName,
          source: args.sourceUrl,
          rtspTransport: "tcp",
        };

        await updatePath(args.pathName, pathConfig);
      }

      // Update database
      await ctx.runMutation(api.streams.updateStreamConfig, args);

      return { success: true };
    } catch (error) {
      throw new Error(`Stream edit failed: ${error instanceof Error ? error.message : error}`);
    }
  },
});

// Toggle stream source (enable/disable)
export const toggleStreamSource = action({
  args: {
    pathName: v.string(),
    isEnabled: v.boolean(),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    try {
      if (args.isEnabled) {
        // Get config from database and add to MediaMTX
        const config = await ctx.runQuery(api.streams.getStreamConfig, { pathName: args.pathName });
        if (!config) throw new Error("Stream configuration not found");

        const pathConfig: PathConfigCreate = {
          name: args.pathName,
          source: config.sourceUrl,
          rtspTransport: "tcp",
        };

        await addPath(args.pathName, pathConfig);
      } else {
        // Remove from MediaMTX
        console.log(`Disabling stream: ${args.pathName}`);
        await deletePath(args.pathName);
      }

      // Update database
      await ctx.runMutation(api.streams.toggleStreamConfig, args);

      return { success: true };
    } catch (error) {
      throw new Error(`Stream toggle failed: ${error instanceof Error ? error.message : error}`);
    }
  },
});

// Import configurations from MediaMTX to database
export const importFromMediaMTX = action({
  args: {},
  returns: v.object({ 
    success: v.boolean(), 
    imported: v.number(),
    updated: v.number(),
    message: v.string()
  }),
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    try {
      // Get configured paths from MediaMTX
      const configPathsData = await listConfigPaths();

      // Get live paths for additional info
      let livePathsData: PathsListResponse | null = null;
      try {
        livePathsData = await listPaths();
      } catch (error) {
        console.warn('Failed to get live paths:', error);
      }

      let importedCount = 0;
      let updatedCount = 0;

      // Import each MediaMTX path configuration
      for (const configInfo of configPathsData.items || []) {
        const pathName = configInfo.name;
        const liveInfo = livePathsData?.items?.find(item => item.name === pathName);
        
        // Check if already exists in database
        const existing = await ctx.runQuery(api.streams.getStreamConfig, { pathName });
        
        if (existing) {
          // Update existing configuration with MediaMTX data
          await ctx.runMutation(api.streams.updateStreamConfigFromMediaMTX, {
            pathName,
            sourceUrl: configInfo.source || '',
            isLive: liveInfo?.ready === true,
            viewers: liveInfo?.readers?.length || 0,
            isInMediaMTX: true,
          });
          updatedCount++;
        } else {
          // Create new configuration from MediaMTX
          await ctx.runMutation(api.streams.saveStreamConfig, {
            pathName,
            sourceUrl: configInfo.source || '',
            streamType: 'external', // Default to external for MediaMTX imports
            description: `Imported from MediaMTX config`,
            isEnabled: true,
          });
          importedCount++;
        }
      }

      return { 
        success: true, 
        imported: importedCount,
        updated: updatedCount,
        message: `Imported ${importedCount} new paths and updated ${updatedCount} existing paths from MediaMTX`
      };
    } catch (error) {
      return { 
        success: false, 
        imported: 0,
        updated: 0,
        message: error instanceof Error ? error.message : String(error)
      };
    }
  },
});

// Reset database to match MediaMTX configurations (WARNING: destructive)
export const resetToMediaMTXConfigs = action({
  args: {
    confirmReset: v.boolean(),
  },
  returns: v.object({
    success: v.boolean(),
    message: v.string(),
    deletedConfigs: v.number(),
    importResult: v.any(),
  }),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    if (!args.confirmReset) {
      throw new Error("Reset confirmation required");
    }

    try {
      // Get all current database configs
      const dbConfigs: Doc<"streamConfigs">[] = await ctx.runQuery(api.streams.getAllStreamConfigs);
      
      // Delete all existing configurations
      for (const config of dbConfigs) {
        await ctx.runMutation(api.streams.deleteStreamConfig, {
          pathName: config.pathName,
        });
      }

      // Import fresh from MediaMTX
      const importResult: {
        success: boolean,
        imported: number,
        updated: number,
        message: string
      } = await ctx.runAction(api.streamActions.importFromMediaMTX);
      
      return {
        success: true,
        message: `Reset complete: ${importResult.message}`,
        deletedConfigs: dbConfigs.length,
        importResult,
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : String(error),
        deletedConfigs: 0,
        importResult: null,
      };
    }
  },
});

// Delete stream configuration
export const deleteStreamConfig = action({
  args: {
    pathName: v.string(),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    try {
      // Get config from database
      const config = await ctx.runQuery(api.streams.getStreamConfig, args);
      if (!config) throw new Error("Stream configuration not found");

      // Remove from MediaMTX if it exists there
      try {
        await deletePath(args.pathName);
      } catch (mediamtxError) {
        console.warn("MediaMTX removal failed:", mediamtxError);
        // Continue with database deletion even if MediaMTX removal fails
      }

      // Remove from database
      await ctx.runMutation(api.streams.deleteStreamConfig, {
        pathName: args.pathName,
      });

      return { success: true };
    } catch (error) {
      throw new Error(`Stream deletion failed: ${error instanceof Error ? error.message : error}`);
    }
  },
});

// Sync MediaMTX paths with database - ensures MediaMTX matches database
export const syncMediaMTXPaths = action({
  args: {},
  returns: v.object({ 
    success: v.boolean(), 
    added: v.number(),
    removed: v.number(),
    updated: v.number(),
    errors: v.array(v.string()),
    message: v.string()
  }),
  handler: async (ctx) => {
    try {
      // Get current paths from MediaMTX
      const configData = await listConfigPaths();
      const mediamtxPaths: Record<string, PathConfig> = {};
      
      // Convert array to Record for easier lookup
      configData.items?.forEach(item => {
        mediamtxPaths[item.name] = item;
      });

      // Get database configurations (source of truth)
      const dbConfigs = await ctx.runQuery(api.streams.getAllStreamConfigs);

      let addedCount = 0;
      let removedCount = 0;
      let updatedCount = 0;
      const errors: string[] = [];

      // Add/Update enabled paths from DB to MediaMTX
      for (const dbConfig of dbConfigs) {
        if (dbConfig.isEnabled) {
          const pathConfig: PathConfigUpdate = {
            name: dbConfig.pathName,
            source: dbConfig.sourceUrl,
            //rtspTransport: "tcp",
          };

          if (!mediamtxPaths[dbConfig.pathName]) {
            // Add missing path to MediaMTX
            try {
              await addPath(dbConfig.pathName, pathConfig);
              addedCount++;
              // Update database to reflect MediaMTX sync
              await ctx.runMutation(api.streams.updateSyncStatus, {
                pathName: dbConfig.pathName,
                isInMediaMTX: true,
                lastSyncStatus: 'synced',
              });
            } catch (error) {
              errors.push(`Failed to add ${dbConfig.pathName}: ${error}`);
            }
          } else {
            // Check if update needed
            const existing = mediamtxPaths[dbConfig.pathName];
            if (existing.source !== dbConfig.sourceUrl) {
              try {
                await updatePath(dbConfig.pathName, pathConfig);
                updatedCount++;
              } catch (error) {
                errors.push(`Failed to update ${dbConfig.pathName}: ${error}`);
              }
            }
          }
        } else {
          // Remove disabled paths from MediaMTX
          if (mediamtxPaths[dbConfig.pathName]) {
            try {
              await deletePath(dbConfig.pathName);
              removedCount++;
              // Update database to reflect removal from MediaMTX
              await ctx.runMutation(api.streams.updateSyncStatus, {
                pathName: dbConfig.pathName,
                isInMediaMTX: false,
                lastSyncStatus: 'synced',
              });
            } catch (error) {
              errors.push(`Failed to remove ${dbConfig.pathName}: ${error}`);
            }
          }
        }
      }

      return { 
        success: errors.length === 0, 
        added: addedCount,
        removed: removedCount,
        updated: updatedCount,
        errors,
        message: `Sync complete: ${addedCount} added, ${updatedCount} updated, ${removedCount} removed${errors.length > 0 ? ` (${errors.length} errors)` : ''}`
      };
    } catch (error) {
      return { 
        success: false, 
        added: 0,
        removed: 0,
        updated: 0,
        errors: [error instanceof Error ? error.message : String(error)],
        message: `Sync failed: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  },
});
