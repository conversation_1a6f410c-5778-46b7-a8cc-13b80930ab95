# NFM Development Docker Compose
# Includes MediaMTX for RTSP → WebRTC video streaming
version: '3.9'

services:
  mediamtx:
    image: bluenviron/mediamtx:latest-ffmpeg
    container_name: nfm-mediamtx
    restart: unless-stopped
    environment:
      - MTX_PROTOCOLS=tcp
      - MTX_RTSPADDRESS=:8554
      - MTX_WEBRTCADDRESS=:8889
      - MTX_HLSADDRESS=:8888
      - MTX_APIADDRESS=:9997
      - MTX_METRICSADDRESS=:9998
      - MTX_LOGFILE=stdout
    ports:
      # RTSP
      - "8554:8554"
      # WebRTC (for ICE/STUN/TURN)
      - "8889:8889/tcp"
      - "8189:8189/udp"
      # HLS (backup streaming)
      - "8888:8888"
      # API (for management)
      - "9997:9997"
      # Metrics
      - "9998:9998"
    volumes:
      - ./docker/mediamtx.yml:/mediamtx.yml
      - ./docker/recordings:/recordings
    networks:
      - nfm-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9997"]