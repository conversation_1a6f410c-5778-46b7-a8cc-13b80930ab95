import { TimelineEngine } from "../engine/engine";
import { TimelineEvent } from "./action";

export interface TimelineEffect {
  /** Effect id */
  id: string;
  /** Effect name */
  name?: string;
  /** Effect code */
  source?: TimeLineEffectSource;
}

export interface EffectSourceParam {
  /** Current time */
  time: number;
  /** Whether to run */
  isPlaying: boolean;
  /** Action */
  action: TimelineEvent;
  /** Action effect */
  effect: TimelineEffect;
  /** Running engine */
  engine: TimelineEngine;
}

/**
 * Effect execution callback
 * @export
 * @interface TimeLineEffectSource
 */
export interface TimeLineEffectSource {
  /** Callback when starting to play in the current action time area */
  start?: (param: EffectSourceParam) => void;
  /** Callback when time enters the action */
  enter?: (param: EffectSourceParam) => void;
  /** Callback when updating the action */
  update?: (param: EffectSourceParam) => void;
  /** Callback when time leaves the action */
  leave?: (param: EffectSourceParam) => void;
  /** Callback when stopping to play in the current action time area */
  stop?: (param: EffectSourceParam) => void;
}