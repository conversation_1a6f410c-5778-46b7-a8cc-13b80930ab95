/**
 * MediaMTX Client for NFM System
 * Handles WebRTC connections to MediaMTX streaming server
 */

import { PathInfo } from './types';
import { getPathInfo, getServerHealth, getServerMetrics } from './api';

export class MediaMTXClient {
  private apiUrl: string;
  private webrtcUrl: string;

  constructor() {
    this.apiUrl = process.env.NEXT_PUBLIC_MEDIAMTX_API_URL || 'http://localhost:9997';
    this.webrtcUrl = process.env.NEXT_PUBLIC_MEDIAMTX_URL || 'http://localhost:8889';
  }

  /**
   * Get information about a specific stream
   */
  async getStreamInfo(streamPath: string): Promise<PathInfo> {
    return await getPathInfo(streamPath);
  }

  /**
   * Get WebRTC URL for a stream
   */
  getWebRTCUrl(streamPath: string): string {
    return `${this.webrtcUrl}/${streamPath}`;
  }

  /**
   * Create WebRTC connection to a stream using WHEP protocol
   */
  async createWebRTCConnection(
    streamPath: string,
    onTrack?: (event: RTCTrackEvent) => void
  ): Promise<RTCPeerConnection> {
    const pc = new RTCPeerConnection({
      iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
    });

    // Set up event handlers
    if (onTrack) {
      pc.ontrack = onTrack;
    }

    pc.oniceconnectionstatechange = () => {
      console.log(`ICE connection state: ${pc.iceConnectionState}`);
    };

    pc.onconnectionstatechange = () => {
      console.log(`Connection state: ${pc.connectionState}`);
    };

    // Add transceivers for receiving audio/video
    pc.addTransceiver('video', { direction: 'recvonly' });
    pc.addTransceiver('audio', { direction: 'recvonly' });

    // Create offer
    const offer = await pc.createOffer();
    await pc.setLocalDescription(offer);

    // Send offer to MediaMTX WHEP endpoint
    const response = await fetch(`${this.webrtcUrl}/${streamPath}/whep`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/sdp',
      },
      body: offer.sdp,
    });

    if (!response.ok) {
      throw new Error(`Failed to connect to stream: ${response.statusText}`);
    }

    const answerSdp = await response.text();
    await pc.setRemoteDescription({
      type: 'answer',
      sdp: answerSdp,
    });

    return pc;
  }

  /**
   * Check if MediaMTX server is running
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await getServerHealth();
      return response;
    } catch {
      return false;
    }
  }

  /**
   * Get server metrics
   */
  async getMetrics(): Promise<string> {
    try {
    const response = await getServerMetrics();
    
    return response;
    } catch (error) {
      throw error;
    }
  }
}

// Singleton instance
export const mediaMTXClient = new MediaMTXClient();