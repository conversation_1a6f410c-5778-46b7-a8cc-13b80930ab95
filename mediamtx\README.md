# MediaMTX Setup for NFM Development

## Quick Start

1. **Download MediaMTX**
   - Go to: https://github.com/bluenviron/mediamtx/releases
   - Download the latest version for your OS
   - Extract the executable to this directory

2. **Start MediaMTX**
   - Windows: Double-click `start-mediamtx.bat`
   - Linux/Mac: Run `./start-mediamtx.sh`

3. **Verify it's running**
   - API: http://localhost:9997
   - Test stream: http://localhost:8889/test_stream

## Available Endpoints

- **RTSP Server**: rtsp://localhost:8554
- **WebRTC**: http://localhost:8889
- **HLS**: http://localhost:8888
- **API**: http://localhost:9997
- **Metrics**: http://localhost:9998

## Test Streams

The configuration includes a test stream that generates a test pattern:
- RTSP: `rtsp://localhost:8554/test_stream`
- WebRTC: http://localhost:8889/test_stream
- HLS: http://localhost:8888/test_stream/index.m3u8

## Monitoring

Run the monitor script to check stream status:
```bash
node monitor.js
```

## Configured Hospital Streams

- **OR1 Main**: `rtsp://*************:8554/main/av`
- **OR1 Sub**: `rtsp://*************:8554/sub/av`
- **OR2 Main**: `rtsp://*************:8554/main/av`

These will be available when connected to the hospital network.

## Authentication

The server is configured with internal authentication:
- **Admin user**: `admin` / `nfm_dev_2025` (for publishing streams)
- **Read access**: Any user can read/playback streams without authentication

To publish a stream with authentication:
```bash
ffmpeg -f your_input -c:v libx264 -f rtsp rtsp://admin:nfm_dev_2025@localhost:8554/your_stream
```

## Troubleshooting

1. **Port already in use**: Kill the process using the port
2. **Stream not working**: Check MediaMTX logs in the console
3. **WebRTC fails**: Ensure no firewall is blocking ports 8889/8189

## Notes

- Recording is disabled in development to save disk space
- All streams are configured for on-demand startup
- Authentication: Admin user `admin` / `nfm_dev_2025` for publishing
- WebRTC ICE server: Google's public STUN server (stun.l.google.com:19302)
