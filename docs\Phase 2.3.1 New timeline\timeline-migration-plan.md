# Timeline Migration Implementation Plan

## Overview
This document outlines the step-by-step migration from the current timeline implementation (`components/timeline_old/`) to the new react-timeline-editor (`components/timeline/`).

## Phase 1: Foundation Setup

### 1.1 Data Transformation Layer
Create utilities to convert NFM data structures to timeline editor format:

**Files to Create:**
- `utils/timelineDataTransform.ts` - Core transformation functions
- `hooks/useTimelineData.ts` - React hook for data management
- `types/timelineEditor.ts` - Type definitions for timeline editor

**Key Integration Approach:**
```typescript
// Direct Integration (No Conversion Layer)
// NFM ModalityConfig -> Timeline Editor TimelineRow (extends ModalityConfig)
// NFM MonitoringEvent -> Timeline Editor TimelineEvent (extends MonitoringEvent)
// NFM EventType/Modality -> Timeline Editor TimelineEffect
```

### 1.2 Effect System Setup
Define effect behaviors for different event types:

**Files to Create:**
- `components/timeline/effects/modalityEffects.ts` - Effect definitions
- `components/timeline/effects/eventRenderers.tsx` - Custom action renderers

### 1.3 Timeline Wrapper Component
Create a wrapper that bridges NFM requirements with timeline editor:

**Files to Create:**
- `components/timeline/NFMTimelineEditor.tsx` - Main wrapper component
- `components/timeline/NFMTimelineControls.tsx` - Control panel component

## Phase 2: Core Integration

### 2.1 Replace Timeline Container
Update the main timeline integration point:

**Files to Modify:**
- `app/dashboard/live-monitoring/page.tsx` - Replace TimelineContainer usage
- `components/contexts/ProjectContext.tsx` - Add timeline editor state management

### 2.2 Video Synchronization
Implement video playback synchronization:

**Features to Implement:**
- Bidirectional time synchronization
- Playback rate matching
- Seek operation handling
- Real-time cursor updates

### 2.3 Event Management
Implement CRUD operations for timeline events:

**Features to Implement:**
- Event creation via timeline clicks
- Event editing through drag/resize
- Event deletion with confirmation
- Batch operations for multiple events

## Phase 3: Feature Enhancement

### 3.1 Advanced Controls
Add enhanced timeline controls:

**Features to Implement:**
- Zoom controls with keyboard shortcuts
- Timeline navigation (prev/next event)
- Modality visibility toggles
- Timeline export/import

### 3.2 Custom Styling
Apply NFM design system:

**Styling Tasks:**
- Theme integration (dark/light mode)
- Color scheme alignment
- Typography consistency
- Responsive design patterns

### 3.3 Performance Optimization
Optimize for large datasets:

**Optimization Tasks:**
- Virtualization for many events
- Efficient re-rendering strategies
- Memory leak prevention
- Smooth animations

## Implementation Steps

### Step 1: Install Dependencies
```bash
npm install lodash @types/lodash
```

### Step 2: Create Data Transformation Utilities
Build the core transformation layer that converts NFM data to timeline editor format.

### Step 3: Create Timeline Wrapper Component
Build the main NFMTimelineEditor component that encapsulates the timeline editor with NFM-specific logic.

### Step 4: Implement Effect System
Define how different event types should behave and render in the timeline.

### Step 5: Update Integration Points
Replace the old timeline usage in the dashboard with the new implementation.

### Step 6: Add Advanced Features
Implement enhanced controls, styling, and performance optimizations.

## File Structure After Migration

```
components/
├── timeline/                    # React timeline editor (cloned library)
│   ├── components/             # Core timeline components
│   ├── engine/                 # Timeline engine
│   ├── interface/              # Type definitions
│   ├── utils/                  # Utility functions
│   ├── effects/                # NFM-specific effects (NEW)
│   ├── NFMTimelineEditor.tsx   # Main wrapper (NEW)
│   └── NFMTimelineControls.tsx # Controls component (NEW)
├── timeline_old/               # Legacy implementation (keep for reference)
└── ui/                         # Existing shadcn/ui components

utils/
├── timelineDataTransform.ts    # Data transformation utilities (NEW)
└── mediaHelpers.ts            # Existing media utilities

hooks/
├── useTimelineData.ts          # Timeline data management hook (NEW)
├── useTimelineScale.ts         # Existing scale hook (may be deprecated)
└── useWebRTC.ts               # Existing WebRTC hook

types/
├── timelineEditor.ts           # Timeline editor types (NEW)
└── timeline.ts                # Existing timeline types
```

## Migration Checklist

### Phase 1 Checklist
- [ ] Install lodash dependency
- [ ] Create data transformation utilities
- [ ] Create timeline wrapper component
- [ ] Define basic effect system
- [ ] Create type definitions

### Phase 2 Checklist
- [ ] Replace timeline usage in dashboard
- [ ] Implement video synchronization
- [ ] Add event CRUD operations
- [ ] Update ProjectContext integration
- [ ] Test basic functionality

### Phase 3 Checklist
- [ ] Add advanced controls
- [ ] Apply NFM styling
- [ ] Implement performance optimizations
- [ ] Add keyboard shortcuts
- [ ] Complete testing suite

## Risk Mitigation

### Backward Compatibility
- Keep `timeline_old/` components as fallback
- Implement feature flags for gradual rollout
- Maintain existing API contracts where possible

### Data Integrity
- Validate all data transformations
- Implement comprehensive error handling
- Add data migration utilities if needed

### Performance Monitoring
- Monitor rendering performance
- Track memory usage patterns
- Implement performance benchmarks

## Success Criteria

### Functional Requirements
- [ ] All existing timeline features work correctly
- [ ] Video synchronization is smooth and accurate
- [ ] Event editing is intuitive and responsive
- [ ] Modality controls function properly

### Performance Requirements
- [ ] Timeline renders smoothly with 1000+ events
- [ ] Memory usage remains stable during long sessions
- [ ] Zoom and scroll operations are fluid
- [ ] Real-time updates don't cause lag

### User Experience Requirements
- [ ] Interface matches NFM design system
- [ ] Keyboard shortcuts work as expected
- [ ] Touch/mobile interactions are responsive
- [ ] Accessibility standards are maintained
