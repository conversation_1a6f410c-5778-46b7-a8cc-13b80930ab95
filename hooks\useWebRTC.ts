"use client";

import { useEffect, useRef, useCallback, useState } from "react";
import { MediaMTXClient } from "@/lib/mediamtx/mediamtx-client";

interface UseWebRTCOptions {
  streamPath: string;
  autoPlay?: boolean;
  onConnectionStateChange?: (state: RTCPeerConnectionState) => void;
  onStreamReady?: (stream: MediaStream) => void;
}

interface WebRTCState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  connectionState: RTCPeerConnectionState | null;
}

export function useWebRTC({
  streamPath,
  autoPlay = true,
  onConnectionStateChange,
  onStreamReady
}: UseWebRTCOptions) {
  const pcRef = useRef<RTCPeerConnection | null>(null);
  const [state, setState] = useState<WebRTCState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    connectionState: null
  });

  const mediaClient = useRef(new MediaMTXClient());

  const connect = useCallback(async () => {
    if (pcRef.current || state.isConnecting) return;

    setState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      const pc = await mediaClient.current.createWebRTCConnection(
        streamPath,
        (event) => {
          if (event.streams && event.streams[0]) {
            onStreamReady?.(event.streams[0]);
          }
        }
      );

      pcRef.current = pc;

      pc.onconnectionstatechange = () => {
        const connectionState = pc.connectionState;
        setState(prev => ({ 
          ...prev, 
          connectionState,
          isConnected: connectionState === "connected",
          isConnecting: connectionState === "connecting"
        }));
        onConnectionStateChange?.(connectionState);
      };

      pc.onicecandidateerror = (event) => {
        console.error("ICE candidate error:", event);
        setState(prev => ({ 
          ...prev, 
          error: "ICE candidate error",
          isConnecting: false 
        }));
      };

    } catch (error) {
      console.error("WebRTC connection failed:", error);
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : "Connection failed",
        isConnecting: false 
      }));
    }
  }, [streamPath, onStreamReady, onConnectionStateChange, state.isConnecting]);

  const disconnect = useCallback(() => {
    if (pcRef.current) {
      pcRef.current.close();
      pcRef.current = null;
    }
    setState({
      isConnected: false,
      isConnecting: false,
      error: null,
      connectionState: null
    });
  }, []);

  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(() => connect(), 1000);
  }, [disconnect, connect]);

  useEffect(() => {
    if (autoPlay) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [streamPath, autoPlay, connect, disconnect]);

  return {
    ...state,
    connect,
    disconnect,
    reconnect
  };
}
