# LLM Instructions for Convex Type-Safe Development

## Core Principles for LLM

When working with this Convex codebase, ALWAYS follow these principles:

### 1. **NEVER manually define validators in function arguments**

❌ **WRONG:**
```typescript
export const createUser = mutation({
  args: {
    email: v.string(),
    name: v.string(),
    role: v.union(v.literal("admin"), v.literal("user")),  // Manual duplication
  },
  handler: async (ctx, args) => { /* */ }
});
```

✅ **CORRECT:**
```typescript
import { Users } from "./schema";
import { omit } from "convex-helpers";

const userCreateFields = Users.withoutSystemFields.fields;

export const createUser = mutation({
  args: userCreateFields,  // Automatically synced with schema
  returns: v.id("users"),
  handler: async (ctx, args) => { /* */ }
});
```

### 2. **Always use Table pattern for schema definitions**

❌ **WRONG:**
```typescript
export default defineSchema({
  users: defineTable({
    email: v.string(),  // Direct field definition
    name: v.string(),
  })
});
```

✅ **CORRECT:**
```typescript
export const Users = Table("users", {
  email: v.string(),
  name: v.string(),
});

export default defineSchema({
  users: Users.table.index("by_email", ["email"]),
});
```

### 3. **Use field selectors for partial field sets**

For different operations, create specific field selectors:

```typescript
import { Users } from "./schema";
import { pick, omit } from "convex-helpers";

// For creation (exclude system fields and auto-generated)
const userCreateFields = omit(Users.withoutSystemFields.fields, ["lastLogin"]);

// For updates (exclude immutable fields)
const userUpdateFields = omit(Users.withoutSystemFields.fields, ["email", "createdAt"]);

// For search (only searchable fields)
const userSearchFields = pick(Users.withoutSystemFields.fields, ["name", "email", "role"]);
```

### 4. **Export and reuse individual validators**

```typescript
// In schema.ts
export const { role: userRoleValidator } = Users.withoutSystemFields.fields;
export const { status: projectStatusValidator } = Projects.withoutSystemFields.fields;

// In functions
export const getUsersByRole = query({
  args: { role: userRoleValidator },  // Reuse the exact validator
  handler: async (ctx, args) => { /* */ }
});
```

### 5. **Always add proper return type validators**

```typescript
export const getUser = query({
  args: { userId: v.id("users") },
  returns: v.union(Users.doc, v.null()),  // Use Table.doc for complete documents
  handler: async (ctx, args) => {
    return await ctx.db.get(args.userId);
  }
});
```

## Mandatory Patterns

### When creating new functions:

1. **Import required Table and helpers:**
```typescript
import { TableName } from "./schema";
import { pick, omit } from "convex-helpers";
```

2. **Define field selectors at the top:**
```typescript
const createFields = TableName.withoutSystemFields.fields;
const updateFields = omit(createFields, ["immutableField"]);
```

3. **Use selectors in function args:**
```typescript
export const createItem = mutation({
  args: createFields,
  returns: v.id("tableName"),
  handler: async (ctx, args) => { /* */ }
});
```

### When modifying existing functions:

1. **Identify manual validator definitions**
2. **Replace with Table field selectors**
3. **Test that types still work correctly**

### When adding new table fields:

1. **Add field to Table definition in schema.ts**
2. **Functions automatically get the new field**
3. **No manual updates needed in function files**

## Common Patterns Library

### Basic CRUD Operations:
```typescript
// Create
const createFields = TableName.withoutSystemFields.fields;
export const create = mutation({
  args: createFields,
  returns: v.id("tableName"),
  handler: async (ctx, args) => {
    return await ctx.db.insert("tableName", {
      ...args,
      createdAt: Date.now(),
    });
  }
});

// Read
export const get = query({
  args: { id: v.id("tableName") },
  returns: v.union(TableName.doc, v.null()),
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  }
});

// Update
const updateFields = omit(TableName.withoutSystemFields.fields, ["createdAt", "createdBy"]);
export const update = mutation({
  args: { id: v.id("tableName"), ...updateFields },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    await ctx.db.patch(id, { ...updates, updatedAt: Date.now() });
    return { success: true };
  }
});
```

### Search/Filter Operations:
```typescript
const searchFields = pick(TableName.withoutSystemFields.fields, ["searchableField1", "searchableField2"]);

export const search = query({
  args: {
    ...searchFields,
    limit: v.optional(v.number()),
  },
  returns: v.array(TableName.doc),
  handler: async (ctx, args) => {
    // Search logic
  }
});
```

## Error Prevention Checklist

Before submitting code, verify:

- [ ] No manual validator definitions in function args
- [ ] All Tables use the Table() pattern
- [ ] Field selectors are used for partial fields
- [ ] Return types use Table.doc or proper validators
- [ ] Individual validators are exported and reused
- [ ] Functions will automatically adapt to schema changes

## Benefits Verification

After implementing, confirm:

- [ ] Schema changes don't require function updates
- [ ] TypeScript provides full autocomplete
- [ ] Compile errors catch field name changes
- [ ] No duplicate validator code exists
- [ ] Functions are type-safe end-to-end

This approach ensures maximum maintainability and type safety while minimizing manual maintenance when schemas evolve.
