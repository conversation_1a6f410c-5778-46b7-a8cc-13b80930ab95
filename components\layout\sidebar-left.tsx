"use client"

import type * as React from "react"
import {
  Activity,
  Brain,
  Calendar,
  FileText,
  LayoutDashboard,
  Settings,
  Users,
  Zap,
  Monitor,
  Circle,
  ChevronDown,
  LogOut,
  User,
  ChevronRight
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState } from "react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
  SidebarSeparator,
  useSidebar
} from "@/components/ui/sidebar"
import { useProjectContext, useCurrentUser } from "@/components/contexts/ProjectContext"
import { cn } from "@/lib/utils"
import ThemeToggler from "../theme/theme-toggler"

// Navigation data
const navigationItems = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Live Monitoring",
    url: "/dashboard/live-monitoring",
    icon: Monitor,
  },
  {
    title: "Projects",
    url: "/dashboard/projects",
    icon: Brain,
  },
  {
    title: "Patients",
    url: "/dashboard/patients",
    icon: Users,
  },
  {
    title: "Recordings",
    url: "/dashboard/recordings",
    icon: Zap,
  },
  {
    title: "Reports",
    url: "/dashboard/reports",
    icon: FileText,
  },
  {
    title: "Schedule",
    url: "/dashboard/schedule",
    icon: Calendar,
  },
]

const settingsItems = [
  {
    title: "General",
    url: "/dashboard/settings",
    icon: Settings,
  },
  {
    title: "Users",
    url: "/dashboard/settings/users",
    icon: Users,
  },
  {
    title: "System",
    url: "/dashboard/settings/system",
    icon: Brain,
  },
]

interface SidebarLeftProps extends React.ComponentProps<typeof Sidebar> {
  onNavClick?: (item: { title: string; url: string; icon: any }) => void
  activeSessionsCount?: number
}

export function SidebarLeft({ onNavClick, activeSessionsCount = 0, ...props }: SidebarLeftProps) {
  const pathname = usePathname()
  const { state } = useSidebar()
  const { currentProject } = useProjectContext()
  const { user } = useCurrentUser()
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [isSessionsVisible, setIsSessionsVisible] = useState(true)

  const userName = user?.name || 'User'
  const userInitials = userName
    ? userName.split(' ').map((n: string) => n[0]).join('').toUpperCase()
    : 'U'

  const handleNavClick = (item: any) => {
    if (onNavClick) {
      onNavClick(item)
    }
  }

  return (
    <Sidebar collapsible="icon" className="border-r-0" {...props}>
      <SidebarHeader>
        {/* NFM System Header */}
        <div className="flex items-center gap-2 py-1">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
            <Activity className="size-4" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-semibold">NFM System</span>
            <span className="truncate text-xs">Neural Function Monitor</span>
          </div>
          {state === "expanded" && 
          
            <ThemeToggler />
          }
        </div>

        <SidebarSeparator />

        {/* Live Session Status */}
        {isSessionsVisible && (
          <div className="px-2">
            <div className="rounded-lg border bg-sidebar-accent p-2">
              {state === "expanded" ? (
                <>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Circle className={cn(
                        "h-2 w-2 fill-current",
                        activeSessionsCount > 0 ? "text-green-500" : "text-gray-400"
                      )} />
                      <span className="text-sm font-medium">
                        {activeSessionsCount > 0 ? "Live Sessions" : "No Active Sessions"}
                      </span>
                    </div>
                    {activeSessionsCount > 0 && (
                      <Badge variant="default" className="ml-2 h-5 text-xs">
                        {activeSessionsCount}
                      </Badge>
                    )}
                  </div>
                  {activeSessionsCount > 0 && (
                    <p className="mt-1 text-xs text-muted-foreground">
                      {activeSessionsCount} patient{activeSessionsCount !== 1 ? 's' : ''} monitored
                    </p>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsSessionsVisible(false)}
                    className="mt-2 h-6 w-full text-xs"
                  >
                    Hide
                  </Button>
                </>
              ) : (
                <div className="flex justify-center">
                  <Circle className={cn(
                    "h-4 w-4 fill-current",
                    activeSessionsCount > 0 ? "text-green-500" : "text-gray-400"
                  )} />
                  {activeSessionsCount > 0 && (
                    <Badge variant="default" className="ml-1 h-4 text-xs">
                      {activeSessionsCount}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {!isSessionsVisible && state === "expanded" && (
          <div className="px-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsSessionsVisible(true)}
              className="w-full text-xs"
            >
              Show Live Sessions
            </Button>
          </div>
        )}

        {/* Main Navigation */}
        <SidebarMenu>
          {navigationItems.map((item) => {
            const isActive = pathname === item.url
            return (
              <SidebarMenuItem key={item.title}>
                <SidebarMenuButton
                  asChild
                  isActive={isActive}
                  onClick={() => handleNavClick(item)}
                >
                  <Link href={item.url}>
                    <item.icon />
                    <span>{item.title}</span>
                    {item.title === "Live Monitoring" && activeSessionsCount > 0 && (
                      <Badge variant="outline" className="ml-auto h-5 text-xs">
                        {activeSessionsCount}
                      </Badge>
                    )}
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            )
          })}
          <Collapsible key={"Settings"} open={isSettingsOpen} onOpenChange={setIsSettingsOpen} className="group/collapsible">
            <SidebarMenuItem>
              <CollapsibleTrigger asChild>
                <SidebarMenuButton >
                  <Settings />
                  <span>Settings</span>
                  <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                </SidebarMenuButton>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <SidebarMenuSub>
                  {settingsItems.map((item) => {
                    const isActive = pathname === item.url
                    return (
                      <SidebarMenuSubItem key={item.title}>
                        <SidebarMenuSubButton asChild isActive={isActive}>
                          <Link href={item.url}>
                            <item.icon />
                            <span>{item.title}</span>
                          </Link>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    )
                  })}
                </SidebarMenuSub>
              </CollapsibleContent>
            </SidebarMenuItem>
          </Collapsible>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>

        {/* User Section */}
        <div className="mt-auto px-2">
          <SidebarSeparator className="mb-2" />

          {/* User Profile */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className={cn(
                  "w-full justify-start h-auto p-2",
                  state === "collapsed" && "justify-center px-2"
                )}
              >
                <Avatar className="h-6 w-6">
                  <AvatarImage src={user?.image} alt={userName} />
                  <AvatarFallback className="text-xs">{userInitials}</AvatarFallback>
                </Avatar>
                {state === "expanded" && (
                  <div className="ml-2 flex flex-col items-start text-left">
                    <span className="text-sm font-medium">{userName}</span>
                    <span className="text-xs text-muted-foreground">{user?.email}</span>
                  </div>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600">
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* System Info */}
          {state === "expanded" && (
            <div className="mt-2 text-xs text-muted-foreground">
              <p>NFM System v1.0</p>
              {currentProject && (
                <p className="mt-1 font-medium text-primary">
                  Project: {currentProject.projectCode}
                </p>
              )}
            </div>
          )}
        </div>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  )
}
