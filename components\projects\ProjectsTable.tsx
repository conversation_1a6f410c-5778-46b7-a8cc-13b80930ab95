"use client"

import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  Search,
  MoreVertical,
  Play,
  Eye,
  Edit,
  FileText,
  Brain
} from "lucide-react"

interface Project {
  _id: string
  projectCode: string
  patient?: {
    firstName: string
    lastName: string
    medicalRecordNumber: string
  }
  surgeryType: string
  operatingRoom: string
  scheduledStart: number
  scheduledDuration: number  
  status: "scheduled" | "pre-op" | "in-progress" | 
           "post-op" | "completed" | "cancelled"
  primarySurgeon?: { name: string }
}

interface ProjectsTableProps {
  projects: Project[]
  onProjectSelect: (project: Project) => void
  onProjectAction: (projectId: string, action: string) => void
}

export function ProjectsTable({ projects, onProjectSelect, onProjectAction }: ProjectsTableProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [roomFilter, setRoomFilter] = useState<string>("all")

  const filteredProjects = projects.filter((project) => {
    const matchesSearch = 
      project.patient?.firstName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.patient?.lastName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.patient?.medicalRecordNumber?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.primarySurgeon?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.surgeryType.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesStatus = statusFilter === "all" || project.status === statusFilter
    const matchesRoom = roomFilter === "all" || project.operatingRoom === roomFilter

    return matchesSearch && matchesStatus && matchesRoom
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled": return "secondary"
      case "pre-op": return "default"
      case "in-progress": return "destructive"
      case "post-op": return "outline"
      case "completed": return "secondary"
      case "cancelled": return "secondary"
      default: return "secondary"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "scheduled": return "🕒"
      case "pre-op": return "🟡"
      case "in-progress": return "🔴"
      case "post-op": return "🟢"
      case "completed": return "✅"
      case "cancelled": return "❌"
      default: return "🕒"
    }
  }

  const formatDateTime = (timestamp: number) => {
    const date = new Date(timestamp)
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
  }
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours}h ${mins}m`
  }

  // Get unique rooms for filter
  const uniqueRooms = Array.from(new Set(projects.map(p => p.operatingRoom))).sort()

  return (
    <div className="space-y-4">
      {/* Search and Filter Bar */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search patients, surgeons, surgery types..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="scheduled">Scheduled</SelectItem>
            <SelectItem value="pre-op">Pre-op</SelectItem>
            <SelectItem value="in-progress">In Progress</SelectItem>
            <SelectItem value="post-op">Post-op</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>

        <Select value={roomFilter} onValueChange={setRoomFilter}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Room" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Rooms</SelectItem>
            {uniqueRooms.map((room) => (
              <SelectItem key={room} value={room}>
                OR {room}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Projects Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[80px]">Status</TableHead>
              <TableHead className="w-[200px]">Patient</TableHead>
              <TableHead className="w-[180px]">Surgeon</TableHead>
              <TableHead className="w-[160px]">Surgery Type</TableHead>
              <TableHead className="w-[60px]">OR</TableHead>
              <TableHead className="w-[120px]">Scheduled</TableHead>
              <TableHead className="w-[100px] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>          <TableBody>
            {filteredProjects.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  No projects found matching your filters
                </TableCell>
              </TableRow>
            ) : (
              filteredProjects.map((project) => {
                const { date, time } = formatDateTime(project.scheduledStart)
                
                return (
                  <TableRow 
                    key={project._id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => onProjectSelect(project)}
                  >
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{getStatusIcon(project.status)}</span>
                        <Badge variant={getStatusColor(project.status)} className="text-xs">
                          {project.status.replace('-', ' ')}
                        </Badge>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">
                          {project.patient?.lastName}, {project.patient?.firstName}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          MRN: {project.patient?.medicalRecordNumber}
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">
                          {project.primarySurgeon?.name || "Not assigned"}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Neurosurgery
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{project.surgeryType}</div>
                        <Badge variant="outline" className="text-xs">
                          {formatDuration(project.scheduledDuration)}
                        </Badge>
                      </div>
                    </TableCell>                    
                    <TableCell>
                      <div className="text-center">
                        <div className="text-lg font-bold">{project.operatingRoom}</div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{time}</div>
                        <div className="text-sm text-muted-foreground">{date}</div>
                      </div>
                    </TableCell>
                    
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => onProjectAction(project._id, "view")}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                          
                          {project.status === "scheduled" && (
                            <DropdownMenuItem onClick={() => onProjectAction(project._id, "start")}>
                              <Play className="mr-2 h-4 w-4" />
                              Start Session
                            </DropdownMenuItem>
                          )}
                          
                          {project.status === "in-progress" && (
                            <DropdownMenuItem onClick={() => onProjectAction(project._id, "monitor")}>
                              <Brain className="mr-2 h-4 w-4" />
                              Open Monitor
                            </DropdownMenuItem>
                          )}
                          
                          <DropdownMenuItem onClick={() => onProjectAction(project._id, "edit")}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Project
                          </DropdownMenuItem>
                          
                          <DropdownMenuItem onClick={() => onProjectAction(project._id, "report")}>
                            <FileText className="mr-2 h-4 w-4" />
                            Generate Report
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                )
              })
            )}
          </TableBody>
        </Table>
      </div>
      
      <div className="text-sm text-muted-foreground">
        Showing {filteredProjects.length} of {projects.length} projects
      </div>
    </div>
  )
}