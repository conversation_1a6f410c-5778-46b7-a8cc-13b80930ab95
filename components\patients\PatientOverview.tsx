"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  User,
  Calendar,
  Hash,
  Phone,
  Home,
  AlertTriangle,
  Pill,
  Stethoscope
} from "lucide-react"

interface Patient {
  _id: string
  firstName: string
  lastName: string
  address?: string
  contactPhone?: string
  medicalRecordNumber: string
  dateOfBirth: number
  gender: string
  emergencyContact?: {
    name: string
    relationship: string
    phone: string
  }
  allergies?: string
  medications: Array<{
    name: string
    dosage: string
    frequency: string
  }>
  medicalHistory: string[]
  diagnosis?: string
  notes?: string
}

interface PatientOverviewProps {
  patient: Patient
}

export function PatientOverview({ patient }: PatientOverviewProps) {
  const calculateAge = (dateOfBirth: number) => {
    const today = new Date()
    const birthDate = new Date(dateOfBirth)
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }
    return age
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <User className="h-5 w-5" />
          <span>Patient Overview</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Basic Information */}
        <div>
          <h3 className="text-xl font-semibold mb-2">
            {patient.firstName} {patient.lastName}
          </h3>
          <div className="grid gap-2 text-sm">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>DOB: {formatDate(patient.dateOfBirth)} ({calculateAge(patient.dateOfBirth)} years)</span>
            </div>
            <div className="flex items-center space-x-2">
              <Hash className="h-4 w-4 text-muted-foreground" />
              <span>MRN: {patient.medicalRecordNumber}</span>
            </div>
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span>{patient.gender}</span>
            </div>
            {patient.contactPhone && (
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span>{patient.contactPhone}</span>
              </div>
            )}
            {patient.address && (
              <div className="flex items-center space-x-2">
                <Home className="h-4 w-4 text-muted-foreground" />
                <span>{patient.address}</span>
              </div>
            )}
            {patient.emergencyContact && (
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                <span>Emergency: {patient.emergencyContact.name} ({patient.emergencyContact.relationship}) - {patient.emergencyContact.phone}</span>
              </div>
            )}
          </div>
        </div>

        {/* Diagnosis */}
        {patient.diagnosis && (
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Stethoscope className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Diagnosis:</span>
            </div>
            <p className="text-sm bg-muted p-3 rounded-lg">
              {patient.diagnosis}
            </p>
          </div>
        )}

        {/* Allergies */}
        {patient.allergies && (
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-destructive" />
              <span className="font-medium text-destructive">Allergies:</span>
            </div>
            <Badge variant="destructive" className="text-sm">
              {patient.allergies}
            </Badge>
          </div>
        )}

        {/* Medications */}
        {patient.medications && patient.medications.length > 0 && (
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Pill className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Medications:</span>
            </div>
            <div className="space-y-2">
              {patient.medications.map((medication, index) => (
                <div key={index} className="text-sm bg-muted p-3 rounded-lg">
                  <div className="font-medium">{medication.name}</div>
                  <div className="text-muted-foreground">
                    {medication.dosage} - {medication.frequency}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Medical History */}
        {patient.medicalHistory && patient.medicalHistory.length > 0 && (
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Medical History:</span>
            </div>
            <div className="space-y-1">
              {patient.medicalHistory.map((history, index) => (
                <div key={index} className="text-sm bg-muted p-2 rounded">
                  {history}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Notes */}
        {patient.notes && (
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Notes:</span>
            </div>
            <p className="text-sm bg-muted p-3 rounded-lg">
              {patient.notes}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
