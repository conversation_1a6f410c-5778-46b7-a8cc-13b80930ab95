"use client"

import React, { createContext, useContext, useReducer, useEffect, useRef, useMemo } from 'react'
import { VideoTimelineState, VideoTimelineActions, VideoTimelineContextType } from '@/types/timelineEditor'

// Initial state
const initialState: VideoTimelineState = {
  currentTime: 0,
  duration: 0,
  isPlaying: false,
  playbackRate: 1,
  isSeeking: false,
  seekingTime: null,
  seekingSource: null,
  isVideoLoading: false,
  isTimelineLoading: false,
  videoError: null,
  timelineError: null,
}

// Action types for reducer
type VideoTimelineAction =
  | { type: 'PLAY' }
  | { type: 'PAUSE' }
  | { type: 'SET_CURRENT_TIME'; payload: { time: number; source: 'video' | 'timeline' } }
  | { type: 'SET_DURATION'; payload: number }
  | { type: 'SET_PLAYBACK_RATE'; payload: number }
  | { type: 'START_SEEKING'; payload: { time: number; source: 'video' | 'timeline' } }
  | { type: 'UPDATE_SEEKING'; payload: number }
  | { type: 'END_SEEKING' }
  | { type: 'SEEK_TO'; payload: { time: number; source: 'video' | 'timeline' } }
  | { type: 'SET_VIDEO_LOADING'; payload: boolean }
  | { type: 'SET_TIMELINE_LOADING'; payload: boolean }
  | { type: 'SET_VIDEO_ERROR'; payload: string | null }
  | { type: 'SET_TIMELINE_ERROR'; payload: string | null }

// Reducer function
function videoTimelineReducer(state: VideoTimelineState, action: VideoTimelineAction): VideoTimelineState {
  switch (action.type) {
    case 'PLAY':
      return { ...state, isPlaying: true }
    
    case 'PAUSE':
      return { ...state, isPlaying: false }
    
    case 'SET_CURRENT_TIME':
      // Only update if not seeking or if the update is from the seeking source
      if (!state.isSeeking || action.payload.source === state.seekingSource) {
        return { ...state, currentTime: action.payload.time }
      }
      return state
    
    case 'SET_DURATION':
      return { ...state, duration: (action.payload === Infinity) ? Number.MAX_SAFE_INTEGER : action.payload }
    
    case 'SET_PLAYBACK_RATE':
      return { ...state, playbackRate: action.payload }
    
    case 'START_SEEKING':
      return {
        ...state,
        isSeeking: true,
        seekingTime: action.payload.time,
        seekingSource: action.payload.source,
      }
    
    case 'UPDATE_SEEKING':
      return {
        ...state,
        seekingTime: action.payload,
        currentTime: action.payload, // Update current time during seeking
      }
    
    case 'END_SEEKING':
      return {
        ...state,
        isSeeking: false,
        seekingTime: null,
        seekingSource: null,
      }
    
    case 'SEEK_TO':
      return {
        ...state,
        currentTime: action.payload.time,
        isSeeking: false,
        seekingTime: null,
        seekingSource: null,
      }
    
    case 'SET_VIDEO_LOADING':
      return { ...state, isVideoLoading: action.payload }
    
    case 'SET_TIMELINE_LOADING':
      return { ...state, isTimelineLoading: action.payload }
    
    case 'SET_VIDEO_ERROR':
      return { ...state, videoError: action.payload }
    
    case 'SET_TIMELINE_ERROR':
      return { ...state, timelineError: action.payload }
    
    default:
      return state
  }
}

// Create context
const VideoTimelineContext = createContext<VideoTimelineContextType | null>(null)

// Provider props
interface VideoTimelineProviderProps {
  children: React.ReactNode
  initialDuration?: number
  onVideoSeek?: (time: number) => void
  onTimelineSeek?: (time: number) => void
  onPlayStateChange?: (isPlaying: boolean) => void
  onPlaybackRateChange?: (rate: number) => void
}

export function VideoTimelineProvider({
  children,
  initialDuration = 0,
  onVideoSeek,
  onTimelineSeek,
  onPlayStateChange,
  onPlaybackRateChange,
}: VideoTimelineProviderProps) {
  // Add stack trace to see what's causing the double render
  console.log('[VideoTimelineProvider] RENDER - Props received:', {
    initialDuration,
    onVideoSeek: !!onVideoSeek,
    onTimelineSeek: !!onTimelineSeek,
    onPlayStateChange: !!onPlayStateChange,
    onPlaybackRateChange: !!onPlaybackRateChange,
  })
  console.trace('[VideoTimelineProvider] RENDER STACK TRACE')
  const [state, dispatch] = useReducer(videoTimelineReducer, {
    ...initialState,
    duration: initialDuration,
  })

  // Use ref to access current state in actions without causing recreations
  const stateRef = useRef(state)
  stateRef.current = state

  // Remove the band-aid solution

  console.log('[VideoTimelineProvider] RENDER - State:', {
    currentTime: state.currentTime,
    isPlaying: state.isPlaying,
    isSeeking: state.isSeeking,
    playbackRate: state.playbackRate
  })
  // Refs for external callbacks to prevent stale closures
  const onVideoSeekRef = useRef(onVideoSeek)
  const onTimelineSeekRef = useRef(onTimelineSeek)
  const onPlayStateChangeRef = useRef(onPlayStateChange)
  const onPlaybackRateChangeRef = useRef(onPlaybackRateChange)

  // Update refs when callbacks change
  useEffect(() => {
    console.log('[VideoTimelineProvider] CALLBACK REFS UPDATE - Callbacks changed:', {
      onVideoSeek: !!onVideoSeek,
      onTimelineSeek: !!onTimelineSeek,
      onPlayStateChange: !!onPlayStateChange,
      onPlaybackRateChange: !!onPlaybackRateChange
    })
    onVideoSeekRef.current = onVideoSeek
    onTimelineSeekRef.current = onTimelineSeek
    onPlayStateChangeRef.current = onPlayStateChange
    onPlaybackRateChangeRef.current = onPlaybackRateChange
  }, [onVideoSeek, onTimelineSeek, onPlayStateChange, onPlaybackRateChange])

  // Memoize actions to prevent unnecessary re-renders
  const actions = useMemo<VideoTimelineActions>(() => {
    console.log('[VideoTimelineProvider] ACTIONS RECREATED - Dependencies changed')
    return {
    play: () => {
      dispatch({ type: 'PLAY' })
      onPlayStateChangeRef.current?.(true)
    },

    pause: () => {
      dispatch({ type: 'PAUSE' })
      onPlayStateChangeRef.current?.(false)
    },

    togglePlayPause: () => {
      if (stateRef.current.isPlaying) {
        dispatch({ type: 'PAUSE' })
        onPlayStateChangeRef.current?.(false)
      } else {
        dispatch({ type: 'PLAY' })
        onPlayStateChangeRef.current?.(true)
      }
    },

    seekTo: (time: number, source: 'video' | 'timeline') => {
      dispatch({ type: 'SEEK_TO', payload: { time, source } })

      // Notify appropriate external handlers
      if (source === 'timeline') {
        onVideoSeekRef.current?.(time)
      } else {
        onTimelineSeekRef.current?.(time)
      }
    },

    startSeeking: (time: number, source: 'video' | 'timeline') => {
      dispatch({ type: 'START_SEEKING', payload: { time, source } })
    },

    updateSeeking: (time: number) => {
      dispatch({ type: 'UPDATE_SEEKING', payload: time })
    },

    endSeeking: () => {
      const seekingTime = stateRef.current.seekingTime
      const seekingSource = stateRef.current.seekingSource

      dispatch({ type: 'END_SEEKING' })

      // Perform final seek when seeking ends
      if (seekingTime !== null && seekingSource) {
        if (seekingSource === 'timeline') {
          onVideoSeekRef.current?.(seekingTime)
        } else {
          onTimelineSeekRef.current?.(seekingTime)
        }
      }
    },

    setPlaybackRate: (rate: number) => {
      dispatch({ type: 'SET_PLAYBACK_RATE', payload: rate })
      onPlaybackRateChangeRef.current?.(rate)
    },

    setDuration: (duration: number) => {
      dispatch({ type: 'SET_DURATION', payload: duration })
    },

    setVideoLoading: (loading: boolean) => {
      dispatch({ type: 'SET_VIDEO_LOADING', payload: loading })
    },

    setTimelineLoading: (loading: boolean) => {
      dispatch({ type: 'SET_TIMELINE_LOADING', payload: loading })
    },

    setVideoError: (error: string | null) => {
      dispatch({ type: 'SET_VIDEO_ERROR', payload: error })
    },

    setTimelineError: (error: string | null) => {
      dispatch({ type: 'SET_TIMELINE_ERROR', payload: error })
    },

    updateCurrentTime: (time: number, source: 'video' | 'timeline') => {
      dispatch({ type: 'SET_CURRENT_TIME', payload: { time, source } })
    },
    }
  }, []) // No dependencies since we use stateRef for current state access

  // Memoize context value to prevent unnecessary re-renders of child components
  const contextValue = useMemo<VideoTimelineContextType>(() => {
    console.log('[VideoTimelineProvider] CONTEXT VALUE RECREATED - Dependencies changed')
    return {
      ...state,
      ...actions,
      isInitialized: true,
    }
  }, [state, actions]);

  return (
    <VideoTimelineContext.Provider value={contextValue}>
      {children}
    </VideoTimelineContext.Provider>
  )
}

// Hook to use the context
export function useVideoTimeline(): VideoTimelineContextType {
  const context = useContext(VideoTimelineContext)
  if (!context) {
    throw new Error('useVideoTimeline must be used within a VideoTimelineProvider')
  }
  return context
}

// Export context for advanced usage
export { VideoTimelineContext }
