import React, { use, useCallback, useEffect, useImperativeHandle, useLayoutEffect, useRef, useState } from 'react';
import { ScrollSync } from 'react-virtualized';
import { ITimelineEngine, TimelineEngine } from '../engine/engine';
import { MIN_SCALE_COUNT, PREFIX, START_CURSOR_TIME } from '../interface/const';
import { TimelineEditor, TimelineRow, TimelineState } from '../interface/timeline';
import { checkProps } from '../utils/check_props';
import { getScaleCountByRows, parserPixelToTime, parserTimeToPixel } from '../utils/deal_data';
import { Cursor } from './cursor/cursor';
import { EditArea } from './edit_area/edit_area';
import './timeline.css';
import { TimeArea } from './time_area/time_area';

export interface TimelineProps extends TimelineEditor {
  ref?: React.Ref<TimelineState>;
}

export const Timeline: React.FC<TimelineProps> = (props) => {
  const { ref } = props;
  const checkedProps = checkProps(props);
  const { style } = props;
  const {
    effects,
    editorData: data,
    //scrollTop,
    autoScroll,
    hideCursor,
    disableDrag,
    scale,
    scaleWidth,
    startLeft,
    minScaleCount,
    maxScaleCount,
    onChange,
    engine,
    autoReRender = true,
    //onScroll: onScrollVertical,
    duration,
  } = checkedProps;

  const engineRef = useRef<ITimelineEngine>(engine || new TimelineEngine());
  const domRef = useRef<HTMLDivElement>(null);
  const areaRef = useRef<HTMLDivElement>(null);
  const scrollSync = useRef<ScrollSync>(null);

  // Editor data state
  const [editorData, setEditorData] = useState(data);
  // Scale count state
  const [scaleCount, setScaleCount] = useState(MIN_SCALE_COUNT);
  // Cursor position state
  const [cursorTime, setCursorTime] = useState(START_CURSOR_TIME);
  // Playing state
  const [isPlaying, setIsPlaying] = useState(false);
  // Current timeline width
  const [width, setWidth] = useState(Number.MAX_SAFE_INTEGER);

  /** Dynamically set scale count */
  const handleSetScaleCount = useCallback((value: number) => {
    const safeMaxScale = maxScaleCount ?? Number.MAX_SAFE_INTEGER;
    const safeMinScale = minScaleCount ?? MIN_SCALE_COUNT;
    const data = Math.min(safeMaxScale, Math.max(safeMinScale, value));
    setScaleCount(data);
  }, [maxScaleCount, minScaleCount]);

  /** Listen for data changes */
  useLayoutEffect(() => {
    if (scale !== undefined) {
      handleSetScaleCount(getScaleCountByRows(data, { scale, duration }));
    }
    setEditorData(data);
  }, [data, scale, duration, handleSetScaleCount]);

  useEffect(() => {
    engineRef.current.effects = effects;
  }, [effects]);

  useEffect(() => {
    engineRef.current.data = editorData;
  }, [editorData]);

  useEffect(() => {
    if (autoReRender) {
      engineRef.current.reRender();
    }
  }, [editorData, autoReRender]);

  // deprecated
  /*useEffect(() => {
    if (scrollSync.current && scrollTop !== undefined) {
      scrollSync.current.setState({ scrollTop });
    }
  }, [scrollTop]);

  /** Handle active data changes */
  const handleEditorDataChange = (editorData: TimelineRow[]) => {
    if (!onChange) return;
    const result = onChange(editorData);
    if (result !== false) {
      engineRef.current.data = editorData;
      if (autoReRender) {
        engineRef.current.reRender();
      }
    }
  };

  /** Handle cursor positioning */
  const handleSetCursor = useCallback((param: { left?: number; time?: number; updateTime?: boolean }): boolean => {
    let { left, time, updateTime = true } = param;
    if (typeof left === 'undefined' && typeof time === 'undefined') return false;
    
    /*
      const safeStartLeft = startLeft ?? 0;
    const safeScale = scale ?? 1;
    const safeScaleWidth = scaleWidth ?? 1;
    */
    if (typeof left === 'undefined') 
      left = parserTimeToPixel(time!, { startLeft: startLeft!, scale: scale!, scaleWidth: scaleWidth! });

    if (typeof time === 'undefined') {
      time = parserPixelToTime(left, {  startLeft: startLeft!, scale: scale!, scaleWidth: scaleWidth! });
    }

    let result = true;
    if (updateTime) {
      result = engineRef.current.setTime(time);
      if(autoReRender)
        engineRef.current.reRender();
    }
    if (result) {
      setCursorTime(time);
    }
    return result;
  }, [startLeft, scale, scaleWidth, autoReRender]);

  /** Set scroll left position */
  const handleDeltaScrollLeft = (delta: number) => {
    if (!scrollSync.current) return;

    // Prevent auto-scroll when exceeding maximum distance
    const safeScaleWidth = scaleWidth ?? 1;
    const safeStartLeft = startLeft ?? 0;
    const data = scrollSync.current.state.scrollLeft + delta;
    if (data > scaleCount * (safeScaleWidth - 1) + safeStartLeft - width) return;

    scrollSync.current.setState({
      scrollLeft: Math.max(scrollSync.current.state.scrollLeft + delta, 0)
    });
  };

  // Handle engine-related data
  useEffect(() => {
    const handleTime = ({ time }: { time: number }) => {
      handleSetCursor({ time, updateTime: false });
    };
    const handlePlay = () => setIsPlaying(true);
    const handlePaused = () => setIsPlaying(false);
    engineRef.current.on('setTimeByTick', handleTime);
    engineRef.current.on('play', handlePlay);
    engineRef.current.on('paused', handlePaused);
  }, [handleSetCursor]);

  // Ref data
  useImperativeHandle(ref, () => ({
    get target() {
      return domRef.current!;
    },
    get listener() {
      return engineRef.current;
    },
    get isPlaying() {
      return engineRef.current.isPlaying;
    },
    get isPaused() {
      return engineRef.current.isPaused;
    },
    setPlayRate: engineRef.current.setPlayRate.bind(engineRef.current),
    getPlayRate: engineRef.current.getPlayRate.bind(engineRef.current),
    setTime: (time: number) => handleSetCursor({ time }),
    getTime: engineRef.current.getTime.bind(engineRef.current),
    reRender: engineRef.current.reRender.bind(engineRef.current),
    play: (param: Parameters<TimelineState['play']>[0]) => engineRef.current.play({ ...param }),
    pause: engineRef.current.pause.bind(engineRef.current),
    scrollLeftBy: (val: number) => {
      handleDeltaScrollLeft(val);
    },
    setScrollLeft: (val) => {
      if (scrollSync.current) {
        scrollSync.current.setState({ scrollLeft: Math.max(val, 0) });
      }
    },
    setScrollTop: (val) => {
      if (scrollSync.current) {
        scrollSync.current.setState({ scrollTop: Math.max(val, 0) });
      }
    },
  }));

  // Listen for timeline area width changes
  useEffect(() => {
    if (areaRef.current) {
      const resizeObserver = new ResizeObserver(() => {
        if (!areaRef.current) return;
        setWidth(areaRef.current.getBoundingClientRect().width);
      });
      resizeObserver.observe(areaRef.current);
      return () => {
        resizeObserver.disconnect();
      };
    }
  }, []);

  return (
    <div ref={domRef} style={style} className={`${PREFIX} ${disableDrag ? PREFIX + '-disable' : ''}`}>
      <ScrollSync ref={scrollSync}>
        {({ scrollLeft, scrollTop, onScroll }) => (
          <>
            <TimeArea
              {...checkedProps}
              timelineWidth={width}
              disableDrag={disableDrag || isPlaying}
              setCursor={handleSetCursor}
              cursorTime={cursorTime}
              editorData={editorData}
              scaleCount={scaleCount}
              setScaleCount={handleSetScaleCount}
              onScroll={onScroll}
              scrollLeft={scrollLeft}
            />
            <EditArea
              {...checkedProps}
              timelineWidth={width}
              disableDrag={disableDrag || isPlaying}
              editorData={editorData}
              cursorTime={cursorTime}
              scaleCount={scaleCount}
              setScaleCount={handleSetScaleCount}
              scrollTop={scrollTop}
              scrollLeft={scrollLeft}
              setEditorData={handleEditorDataChange}
              deltaScrollLeft={autoScroll ? handleDeltaScrollLeft : undefined}
              onScroll={(params) => {
                onScroll(params);
                //onScrollVertical?.(params);
              }}
            />
            {!hideCursor && (
              <Cursor
                {...checkedProps}
                timelineWidth={width}
                disableDrag={isPlaying}
                scrollLeft={scrollLeft}
                scaleCount={scaleCount}
                setScaleCount={handleSetScaleCount}
                setCursor={handleSetCursor}
                cursorTime={cursorTime}
                editorData={editorData}
                areaRef={areaRef as React.RefObject<HTMLDivElement>}
                scrollSync={scrollSync as React.RefObject<ScrollSync>}
                deltaScrollLeft={autoScroll ? handleDeltaScrollLeft : (() => {})}
              />
            )}
          </>
        )}
      </ScrollSync>
    </div>
  );
};
