# MediaMTX Configuration Changes

## Fixed Issues

### 1. Deprecated Parameters
All deprecated parameters have been updated to their new versions:
- `protocols` → `rtspTransports`
- `encryption` → `rtspEncryption`
- `authMethods` → `rtspAuthMethods`
- `sourceProtocol` → `rtspTransport`
- Recording settings moved to `pathDefaults` section
- Authentication now uses `authInternalUsers` structure

### 2. Invalid Path Name
The regex path pattern `stream~^(.+)$` was invalid. MediaMTX paths can only contain:
- Alphanumeric characters
- Underscore (_)
- Dot (.)
- Tilde (~)
- Minus (-)
- Slash (/)

Changed to `stream_dynamic` as a simple path prefix.

### 3. WebRTC ICE Servers
Fixed the ICE server configuration format. Note: `webrtcICEServers2` is used temporarily as the exact parameter name may vary by MediaMTX version.

## Current Configuration

### Authentication
Using internal authentication with:
- **Admin user**: Can publish, read, playback, and access API
- **Anonymous users**: Can read and playback streams

### RTSP Settings
- Transport: TCP only (more reliable for medical streams)
- Encryption: Disabled for development
- Auth methods: Basic authentication

### Paths
- `test_stream`: Auto-generated test pattern
- `or1_main`, `or1_sub`, `or2_main`: Hospital RTSP sources
- `stream_dynamic`: Placeholder for runtime configuration

## Testing

After starting MediaMTX, test authentication:
```bash
# Publish with auth
ffmpeg -re -f lavfi -i testsrc -c:v libx264 -f rtsp rtsp://admin:nfm_dev_2025@localhost:8554/test_auth

# Read without auth (should work)
ffplay rtsp://localhost:8554/test_stream
```