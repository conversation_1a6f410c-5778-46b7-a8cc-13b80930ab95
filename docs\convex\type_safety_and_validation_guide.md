# Convex TypeSafety and Argument Validation Manual for LLMs

## Overview

This manual provides best practices for implementing type safety and argument validation in Convex applications without code repetition. It covers schema design, validator reuse, and advanced TypeScript patterns to maintain consistency across your full-stack application.

## Core Principles

### 1. Single Source of Truth
- Define your data model once in `convex/schema.ts`
- Export and reuse validators throughout your application
- Let TypeScript types flow from your schema definitions

### 2. DRY (Don't Repeat Yourself)
- Avoid duplicating validator definitions
- Use schema-derived types instead of manual type definitions
- Leverage TypeScript utility types for field subsets

### 3. End-to-End Type Safety
- Use Convex-generated types (`Doc<"table">`, `Id<"table">`)
- Validate arguments using schema-defined validators
- Maintain consistency between backend and frontend types

## Schema Organization Best Practices

### Basic Schema Structure

```typescript
// convex/schema.ts
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

// Export individual field validators
export const courseValidator = v.union(
  v.literal("appetizer"),
  v.literal("main"),
  v.literal("dessert")
);

// Export complete field objects for reuse
export const recipeFields = {
  name: v.string(),
  course: courseValidator,
  ingredients: v.array(v.string()),
  steps: v.array(v.string()),
};

export default defineSchema({
  recipes: defineTable(recipeFields)
    .index("by_course", ["course"]),
});
```

### Advanced Schema with Table Helper

```typescript
// convex/schema.ts
import { defineSchema } from "convex/server";
import { v } from "convex/values";
import { Table } from "convex-helpers/server";

export const Recipes = Table("recipes", {
  name: v.string(),
  course: v.union(
    v.literal("appetizer"),
    v.literal("main"),
    v.literal("dessert")
  ),
  ingredients: v.array(v.string()),
  steps: v.array(v.string()),
});

export default defineSchema({
  recipes: Recipes.table.index("by_course", ["course"]),
});
```

## Argument Validation Patterns

### 1. Reusing Schema Field Validators

```typescript
// convex/recipes.ts
import { mutation, query } from "./_generated/server";
import { recipeFields, courseValidator } from "./schema";

// Use complete field object for insert operations
export const addRecipe = mutation({
  args: recipeFields,
  handler: async (ctx, args) => {
    return await ctx.db.insert("recipes", args);
  },
});

// Use individual validators for specific arguments
export const listByCourse = query({
  args: { course: courseValidator },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("recipes")
      .withIndex("by_course", (q) => q.eq("course", args.course))
      .collect();
  },
});
```

### 2. Using Table Helper Validators

```typescript
// convex/recipes.ts
import { mutation, action } from "./_generated/server";
import { Recipes } from "./schema";

// For new document creation (without system fields)
export const addRecipe = mutation({
  args: Recipes.withoutSystemFields,
  handler: async (ctx, args) => {
    return await ctx.db.insert("recipes", args);
  },
});

// For operations with complete documents (including system fields)
export const processRecipe = action({
  args: {
    recipe: Recipes.doc, // v.object() validator for complete document
  },
  handler: async (ctx, args) => {
    // Process the complete recipe document
    console.log(`Processing ${args.recipe.name} (ID: ${args.recipe._id})`);
  },
});
```

### 3. Validator Subsets with Destructuring

```typescript
// convex/recipes.ts
import { mutation, query } from "./_generated/server";
import { recipeFields } from "./schema";

// Extract specific validators
const { course, name } = recipeFields;

export const searchByNameAndCourse = query({
  args: { course, name },
  handler: async (ctx, args) => {
    // Query logic here
  },
});

// Use rest operator to exclude fields
const { course, ...recipeWithoutCourse } = recipeFields;

export const addDessert = mutation({
  args: recipeWithoutCourse,
  handler: async (ctx, args) => {
    return await ctx.db.insert("recipes", { 
      ...args, 
      course: "dessert" 
    });
  },
});
```

## TypeScript Type Patterns

### 1. Using Generated Types

```typescript
// src/components/Recipe.tsx
import type { Doc, Id } from "../convex/_generated/dataModel";
import type { WithoutSystemFields } from "convex/server";

// Use Doc<"table"> for complete documents
export function RecipeDisplay({ recipe }: { recipe: Doc<"recipes"> }) {
  return (
    <div>
      <h1>{recipe.name}</h1>
      <p>Course: {recipe.course}</p>
      <p>Created: {new Date(recipe._creationTime).toLocaleDateString()}</p>
    </div>
  );
}

// Use Id<"table"> for document references
export function RecipeLink({ id }: { id: Id<"recipes"> }) {
  const recipe = useQuery(api.recipes.getById, { id });
  return recipe ? <a href={`/recipe/${id}`}>{recipe.name}</a> : null;
}

// Use WithoutSystemFields for new document creation
export function RecipeForm({ 
  onSave 
}: { 
  onSave: (data: WithoutSystemFields<Doc<"recipes">>) => void 
}) {
  // Form implementation
}
```

### 2. Extracting Types from Validators

```typescript
// convex/schema.ts
import { v, Infer } from "convex/values";

export const courseValidator = v.union(
  v.literal("appetizer"),
  v.literal("main"),
  v.literal("dessert")
);

// Extract TypeScript type from validator
export type Course = Infer<typeof courseValidator>;

// Use in client code
// src/components/CourseSelector.tsx
import type { Course } from "../convex/schema";

export function CourseSelector({ 
  selectedCourse, 
  onSelect 
}: { 
  selectedCourse: Course;
  onSelect: (course: Course) => void;
}) {
  // Component implementation
}
```

### 3. TypeScript Utility Types for Subsets

```typescript
// src/types/recipe.ts
import type { Doc } from "../convex/_generated/dataModel";

// Pick specific fields
export type RecipeSummary = Pick<Doc<"recipes">, "name" | "course">;

// Omit specific fields
export type RecipeWithoutSteps = Omit<Doc<"recipes">, "steps">;

// Make all fields optional (useful for updates)
export type RecipeUpdate = Partial<WithoutSystemFields<Doc<"recipes">>>;

// Use in components
export function RecipeHeader({ recipe }: { recipe: RecipeSummary }) {
  return <h1>{recipe.name} ({recipe.course})</h1>;
}

export function updateRecipe(
  id: Id<"recipes">, 
  updates: RecipeUpdate
) {
  // Update logic
}
```

## Common Patterns and Anti-Patterns

### ✅ DO: Centralize Validator Definitions

```typescript
// convex/schema.ts - GOOD
export const userFields = {
  email: v.string(),
  name: v.string(),
  role: v.union(v.literal("admin"), v.literal("user")),
};

export default defineSchema({
  users: defineTable(userFields),
});
```

### ❌ DON'T: Duplicate Validators

```typescript
// convex/users.ts - BAD
export const createUser = mutation({
  args: {
    email: v.string(), // Duplicated from schema
    name: v.string(),   // Duplicated from schema
    role: v.union(v.literal("admin"), v.literal("user")), // Duplicated
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("users", args);
  },
});
```

### ✅ DO: Use Generated Types

```typescript
// src/components/User.tsx - GOOD
import type { Doc } from "../convex/_generated/dataModel";

export function UserProfile({ user }: { user: Doc<"users"> }) {
  return <div>{user.name} ({user.email})</div>;
}
```

### ❌ DON'T: Define Manual Types

```typescript
// src/types/user.ts - BAD
export interface User {
  _id: string;           // Manual type definition
  _creationTime: number; // that duplicates generated types
  email: string;
  name: string;
  role: "admin" | "user";
}
```

## Advanced Techniques

### 1. Conditional Validation

```typescript
// convex/schema.ts
export const createPostFields = {
  title: v.string(),
  content: v.string(),
  isDraft: v.boolean(),
};

export const publishPostFields = {
  ...createPostFields,
  publishedAt: v.number(),
  isDraft: v.literal(false), // Override for published posts
};
```

### 2. Nested Object Validation

```typescript
// convex/schema.ts
export const addressFields = {
  street: v.string(),
  city: v.string(),
  country: v.string(),
};

export const userFields = {
  name: v.string(),
  email: v.string(),
  address: v.object(addressFields), // Nested validation
};

// Extract nested types
export type Address = Infer<typeof v.object(addressFields)>;
```

### 3. Dynamic Validator Selection

```typescript
// convex/validation.ts
import { recipeFields } from "./schema";

export function getValidatorSubset<K extends keyof typeof recipeFields>(
  fields: K[]
): Pick<typeof recipeFields, K> {
  return Object.fromEntries(
    fields.map(field => [field, recipeFields[field]])
  ) as Pick<typeof recipeFields, K>;
}

// Usage
const nameAndCourseValidator = getValidatorSubset(["name", "course"]);
```

## Testing Type Safety

### 1. Runtime Validation Tests

```typescript
// convex/tests/validation.test.ts
import { convexTest } from "convex-test";
import { api } from "./_generated/api";

test("recipe validation", async () => {
  const t = convexTest(schema);
  
  // Valid data should pass
  await t.mutation(api.recipes.addRecipe, {
    name: "Test Recipe",
    course: "main",
    ingredients: ["ingredient1"],
    steps: ["step1"],
  });
  
  // Invalid data should fail
  await expect(
    t.mutation(api.recipes.addRecipe, {
      name: "Test Recipe",
      course: "invalid_course", // Should fail validation
      ingredients: ["ingredient1"],
      steps: ["step1"],
    })
  ).rejects.toThrow();
});
```

### 2. TypeScript Compilation Tests

```typescript
// src/types/type-tests.ts
import type { Doc, Id } from "../convex/_generated/dataModel";
import type { WithoutSystemFields } from "convex/server";

// These should compile without errors
const validId: Id<"recipes"> = "recipe_id" as Id<"recipes">;
const validDoc: Doc<"recipes"> = {} as Doc<"recipes">;
const validNewDoc: WithoutSystemFields<Doc<"recipes">> = {} as WithoutSystemFields<Doc<"recipes">>;

// These should cause TypeScript errors (uncomment to test)
// const invalidId: Id<"recipes"> = "invalid_id"; // TS Error
// const invalidDoc: Doc<"nonexistent"> = {}; // TS Error
```

## Migration and Refactoring

### 1. Migrating Existing Code

```typescript
// Step 1: Extract validators from existing code
// OLD: Inline validators
export const oldFunction = mutation({
  args: {
    name: v.string(),
    course: v.union(v.literal("main"), v.literal("dessert")),
  },
  handler: async (ctx, args) => { /* ... */ },
});

// NEW: Schema-based validators
import { recipeFields } from "./schema";
const { name, course } = recipeFields;

export const newFunction = mutation({
  args: { name, course },
  handler: async (ctx, args) => { /* ... */ },
});
```

### 2. Gradual Type Safety Adoption

```typescript
// Start with basic generated types
import type { Doc } from "../convex/_generated/dataModel";

// Gradually add more specific typing
type RecipePreview = Pick<Doc<"recipes">, "name" | "course">;

// Eventually use full type safety with validators
import { Recipes } from "../convex/schema";
const createRecipeArgs = Recipes.withoutSystemFields;
```

## Troubleshooting Common Issues

### 1. Type Mismatches

**Problem**: TypeScript errors when using generated types
**Solution**: Ensure your schema is properly exported and types are regenerated

```bash
npm run convex dev  # Regenerates types
```

### 2. Validator Import Errors

**Problem**: Cannot import validators from schema
**Solution**: Check export syntax and file paths

```typescript
// Correct export
export const recipeFields = { /* ... */ };

// Correct import
import { recipeFields } from "./schema";
```

### 3. System Fields Confusion

**Problem**: Including `_id` and `_creationTime` in creation functions
**Solution**: Use `WithoutSystemFields` for new document creation

```typescript
// Correct
args: WithoutSystemFields<Doc<"recipes">>

// Instead of including system fields manually
args: {
  name: v.string(),
  // Don't include _id or _creationTime for new documents
}
```

## Conclusion

By following these patterns and best practices, you can maintain type safety across your entire Convex application while minimizing code repetition. The key is to establish your schema as the single source of truth and derive all other types and validators from it.

Remember to:
- Define validators once in your schema
- Export and reuse them throughout your application
- Use TypeScript utility types for field subsets
- Leverage Convex-generated types for end-to-end safety
- Test both runtime validation and TypeScript compilation

This approach will result in more maintainable, type-safe, and less error-prone Convex applications.