"use client"

import { useState } from "react"
import { usePathname } from "next/navigation"
import { Header } from "./header"
import { SidebarLeft } from "./sidebar-left"
import { SidebarRight } from "./sidebar-right"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { useIsMobile } from "@/hooks/use-mobile"
import { ScrollArea } from "../ui/scroll-area"

interface MainLayoutProps {
  children: React.ReactNode
  activeSessionsCount?: number
  pageTitle?: string
  pageSubtitle?: string
  isLive?: boolean
}

const pagesWithRightSidebar = ["/live-monitoring"]

export function MainLayout({
  children,
  activeSessionsCount = 0,
  pageTitle,
  pageSubtitle,
  isLive = false
}: MainLayoutProps) {
  const pathname = usePathname()
  const [isRightSidebarOpen, setRightSidebarOpen] = useState(false)

  // Determine current page for right sidebar content
  const getCurrentPage = () => {
    const matchedPage = pagesWithRightSidebar.find(page => pathname.includes(page))
    return matchedPage ? matchedPage.replace('/', '') : 'default'
  }

  // Determine if right sidebar should be shown
  const showRightSidebarTrigger = pathname.includes('/live-monitoring')

  const handleToggleRightSidebar = () => {
    setRightSidebarOpen(!isRightSidebarOpen)
  }

  return (
    <SidebarProvider>
      {/* Left Sidebar */}
      <SidebarLeft activeSessionsCount={activeSessionsCount} />

      {/* Main Content Area */}
      <div className="flex flex-1 flex-col">
        {/* Header */}
        <Header
          activeSessionsCount={activeSessionsCount}
          pageTitle={pageTitle}
          pageSubtitle={pageSubtitle}
          isLive={isLive}
          onToggleRightSidebar={handleToggleRightSidebar}
          isRightSidebarOpen={isRightSidebarOpen}
          showRightSidebarTrigger={showRightSidebarTrigger}
        />

        {/* Content */}
        
          {/* Main Page Content */}
          <ScrollArea className="gap-1 p-4 flex-1 z-0 overflow-auto">
            <div className="flex-1 bg-muted/10 ">
              {children}
            </div>
          </ScrollArea>
      </div>
      {/* Right Sidebar */}
      <SidebarRight
        isOpen={isRightSidebarOpen}
        currentPage={getCurrentPage()}
        onOpenChange={setRightSidebarOpen}
      />
    </SidebarProvider>
  )
}