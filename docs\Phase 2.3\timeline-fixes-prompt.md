# Phase 2.3 Timeline Fixes - Critical Issues to Resolve

**Date**: May 31, 2025  
**Status**: 🔧 FIXES REQUIRED  
**Phase**: 2.3 Basic Timeline Foundation  
**Priority**: HIGH - Core functionality issues affecting usability

## 🚨 Current Issues Identified

### 1. Event Positioning Bug
**Problem**: When clicking an event on the timeline, a dot appears offset to the right instead of at the correct position.
**Impact**: Events appear at wrong timestamps, making timeline unreliable for medical monitoring.

### 2. Missing Event Duration Support  
**Problem**: Events are currently represented as single dots instead of time ranges.
**Impact**: Cannot show event duration, which is critical for medical analysis (e.g., MEP loss lasting 30 seconds).
**Database Change Required**: Add `startTime` and `endTime` fields to events table.

### 3. Timeline Scrolling Issues
**Problem**: Multiple scrolling-related bugs:
- Timeline doesn't scroll when using "Next Event" while zoomed in
- CurrentTimeIndicator goes off-screen instead of auto-scrolling timeline
- No manual scrolling capability (mouse wheel, scroll buttons)

### 4. CurrentTimeIndicator Interaction Issues
**Problem**: When dragging the time indicator, it doesn't follow mouse movement smoothly - only jumps to position on mouse release.
**Impact**: Poor user experience for seeking to specific times.

### 5. Missing Zoom Controls
**Problem**: No keyboard shortcuts for zooming (Ctrl+scroll should zoom in/out).

### 6. Missing Modality Navigation  
**Problem**: No way to jump to next/previous event for specific modalities.
**Requested**: Hover arrows on modality labels to navigate between events of that type.

### 7. Missing Event Context Menu
**Problem**: No right-click context menu for events.
**Required**: Right-click should show "View", "Edit", "Delete" options.

## 📋 Current Phase 2.3 Status

### ✅ What's Working
- Timeline zoom levels and scale calculations
- Basic event marker display
- Video-timeline synchronization (when not scrolled)
- Modality color coding
- Expandable view toggle
- Time ruler with grid lines

### 🔧 What Needs Fixing
- Event positioning accuracy
- Event duration representation  
- Timeline auto-scrolling
- Manual scrolling controls
- Smooth time indicator dragging
- Keyboard zoom shortcuts
- Modality-specific navigation
- Event context menus

## 🎯 Implementation Plan

### Phase 1: Fix Event Positioning & Duration
1. **Update Events Schema** (`convex/schema.ts`):
   - Add `startTime: v.number()` and `endTime: v.number()` to events table
   - Keep `time` field for backward compatibility (can be calculated as `startTime`)
   
2. **Fix Event Positioning** (`components/timeline/ModalityTrack.tsx`):
   - Debug position calculation in `getPositionAtTime()` function
   - Ensure event markers align correctly with time ruler
   
3. **Implement Event Duration Rendering**:
   - Add duration bars for events when zoomed in (> 30s per 100px)
   - Keep dot representation when zoomed out for overview
   - Color-code duration bars with modality colors

### Phase 2: Fix Timeline Scrolling
1. **Auto-scroll for Next Event** (`components/timeline/TimelineContainer.tsx`):
   - Modify `handleNextEvent()` to call `timeline.scrollToTime()`
   - Ensure CurrentTimeIndicator stays visible
   
2. **Add Manual Scrolling**:
   - Add scroll buttons at timeline edges
   - Implement mouse wheel scrolling
   - Add keyboard arrow key scrolling
   
3. **Fix CurrentTimeIndicator Scrolling**:
   - Auto-scroll timeline when indicator would go off-screen
   - Implement smooth following during video playback

### Phase 3: Improve Time Indicator Interaction
1. **Smooth Dragging** (`components/timeline/CurrentTimeIndicator.tsx`):
   - Add mouse move handlers for real-time position updates
   - Remove animation during dragging
   - Re-enable animation on mouse release

### Phase 4: Add Advanced Controls
1. **Keyboard Shortcuts** (`components/timeline/TimelineContainer.tsx`):
   - Add `onKeyDown` handler for Ctrl+scroll zoom
   - Implement space bar for play/pause
   - Add arrow keys for seek

2. **Modality Navigation**:
   - Add hover state to modality labels
   - Show next/previous arrows
   - Implement modality-specific event jumping

3. **Event Context Menu**:
   - Add right-click handler to event markers
   - Create context menu component
   - Implement View/Edit/Delete actions (stubs for Phase 3.1)

## 📁 Key Files to Examine

### Primary Components to Modify:
1. **`convex/schema.ts`** - Add event duration fields
2. **`hooks/useTimelineScale.ts`** - Fix position calculations, add auto-scroll
3. **`components/timeline/ModalityTrack.tsx`** - Fix event positioning, add duration rendering
4. **`components/timeline/TimelineContainer.tsx`** - Add scrolling, keyboard shortcuts
5. **`components/timeline/CurrentTimeIndicator.tsx`** - Fix dragging interaction
6. **`app/live-monitoring/page.tsx`** - Update sample events with duration

### Supporting Files:
- **`docs/frontend-specifications.md`** - Update event duration requirements
- **`components/timeline/TimelineRuler.tsx`** - May need position calculation fixes
- **Sample events data** - Add startTime/endTime to test events

### Documentation Files to Review:
- **`docs/Phase 2.3/implementation-summary.md`** - Complete Phase 2.3 context and what was implemented
- **`docs/frontend-specifications.md`** - Timeline specifications (lines ~350-450) with updated requirements
- **`docs/progression-log.md`** - Current project status and implementation decisions
- **`docs/implementation-checklist.md`** - What checkboxes are complete vs pending
- **`backend-specifications.md`** - Database schema context for events table modifications
- **`components/timeline/index.ts`** - Available timeline exports and component structure

## 🧪 Testing Requirements

### Manual Testing Checklist:
- [ ] Click event marker - dot appears at correct position
- [ ] Zoom in - events show as duration bars instead of dots
- [ ] Click "Next Event" while zoomed in - timeline scrolls to show event
- [ ] Drag time indicator - follows mouse smoothly
- [ ] Mouse wheel on timeline - scrolls horizontally
- [ ] Ctrl+mouse wheel - zooms in/out
- [ ] Right-click event - context menu appears
- [ ] Hover modality label - navigation arrows appear
- [ ] Timeline auto-scrolls when time indicator would go off-screen

### Data Validation:
- [ ] Events with duration display correctly
- [ ] Event positioning matches time ruler
- [ ] Scroll position preserved during zoom changes
- [ ] Event creation maintains backward compatibility

## 🔄 Implementation Order

### Step 1: Database & Positioning Fixes (Critical)
```
1. Update events schema with startTime/endTime
2. Fix event positioning calculations
3. Test basic timeline accuracy
```

### Step 2: Scrolling & Navigation (High Priority)
```
1. Implement timeline auto-scrolling
2. Add manual scroll controls  
3. Fix next event navigation
```

### Step 3: Interaction Improvements (Medium Priority)
```
1. Smooth time indicator dragging
2. Keyboard shortcuts
3. Event duration visualization
```

### Step 4: Advanced Features (Low Priority)
```
1. Modality navigation arrows
2. Event context menus
3. Polish and optimization
```

## 💻 LLM Implementation Prompt

```
I need you to fix critical timeline issues in Phase 2.3 of the NFM medical monitoring system.

CURRENT STATUS:
- Basic timeline foundation is complete but has positioning and scrolling bugs
- Events appear offset from correct positions
- Timeline doesn't scroll properly during navigation
- Missing event duration support for medical analysis

DOCUMENTATION TO REVIEW FIRST:
- docs/Phase 2.3/implementation-summary.md (complete Phase 2.3 context)
- docs/frontend-specifications.md (timeline specs ~lines 350-450, updated requirements)
- docs/progression-log.md (current project status and decisions)
- backend-specifications.md (database schema context for events table)

CRITICAL ISSUES TO FIX:
1. Event positioning bug - events appear offset to the right
2. Add event duration fields to schema (startTime, endTime)
3. Timeline auto-scrolling when using Next Event or time indicator goes off-screen
4. Smooth time indicator dragging (currently jumps on release)
5. Manual scrolling with mouse wheel and scroll buttons
6. Keyboard zoom shortcuts (Ctrl+scroll)
7. Event context menu on right-click

FILES TO EXAMINE:
- convex/schema.ts (add duration fields)
- hooks/useTimelineScale.ts (fix calculations)  
- components/timeline/ModalityTrack.tsx (fix positioning)
- components/timeline/TimelineContainer.tsx (add scrolling)
- components/timeline/CurrentTimeIndicator.tsx (fix dragging)

IMPLEMENTATION ORDER:
1. Fix event positioning and add duration support
2. Implement timeline scrolling and navigation
3. Improve time indicator interaction
4. Add keyboard shortcuts and context menus

TEST THOROUGHLY: Events must appear at correct positions and timeline must scroll properly for medical precision.

Start with the most critical positioning fixes first, then work through scrolling issues.
```

---

**Status**: Ready for implementation in new chat session  
**Expected Duration**: 2-3 hours for all fixes  
**Validation**: Manual testing with sample events required