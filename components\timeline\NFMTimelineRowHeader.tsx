/**
 * Timeline row header component for modality information and navigation
 */

import React, { useState, useMemo, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { MultiSelect } from '@/components/ui_custom/multi-select';
import { Doc } from '@/convex/_generated/dataModel';
import { TimelineEvent } from './interface/timeline';
import { useIsMobile } from '@/hooks/use-mobile';

export interface NFMTimelineRowHeaderProps {
  modality: Doc<"modalityConfigs">;
  events: TimelineEvent[];
  currentTime: number;
  isVisible?: boolean;
  onToggleVisibility?: (modalityId: string, visible: boolean) => void;
  onNavigateToEvent?: (time: number) => void;
  className?: string;
}

const NFMTimelineRowHeaderComponent = ({
  modality,
  events,
  currentTime,
  isVisible = true,
  onToggleVisibility,
  onNavigateToEvent,
  className
}: NFMTimelineRowHeaderProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const isMobile = useIsMobile();


  // Memoize expensive event calculations
  const modalityEvents = useMemo(() => {
    return events
      .filter(event => event.modalityId === modality._id)
      .sort((a, b) => a.start - b.start);
  }, [events, modality._id]);

  // Memoize navigation event calculations
  const { prevEvent, nextEvent } = useMemo(() => {
    const pastEvents = modalityEvents.filter(event => event.end < currentTime);
    const futureEvents = modalityEvents.filter(event => event.start > currentTime);

    return {
      prevEvent: pastEvents.length > 0 ? pastEvents[pastEvents.length - 1] : null,
      nextEvent: futureEvents.length > 0 ? futureEvents[0] : null
    };
  }, [modalityEvents, currentTime]);

  // Memoize event handlers to prevent unnecessary re-renders
  const handlePrevEvent = useCallback(() => {
    if (prevEvent && onNavigateToEvent) {
      onNavigateToEvent(prevEvent.start);
    }
  }, [prevEvent, onNavigateToEvent]);

  const handleNextEvent = useCallback(() => {
    if (nextEvent && onNavigateToEvent) {
      onNavigateToEvent(nextEvent.start);
    }
  }, [nextEvent, onNavigateToEvent]);



  return (
    <div
      className={cn(
        'timeline-row-header',
        'flex items-center justify-between px-3 py-2 border-r border-gray-200 dark:border-gray-700',
        'bg-gray-50 dark:bg-gray-800 transition-all duration-200',
         isMobile? 'min-w-[100px] max-w-[100px]' :'min-w-[150px] max-w-[150px]', // Fixed width
        'select-none', // Prevent text selection
        {
          'bg-gray-100 dark:bg-gray-700': isHovered,
          'opacity-50': !isVisible
        },
        className
      )}
      style={{
        height: '40px', // Match timeline row height
        borderLeftColor: modality.colorCode,
        borderLeftWidth: '4px',
        borderLeftStyle: 'solid'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Left side - Modality info */}
      <div className="flex items-center gap-2 flex-1 min-w-0">
        {/* Modality color indicator */}
        <div
          className="w-3 h-3 rounded-full flex-shrink-0"
          style={{ backgroundColor: modality.colorCode }}
        />
        
        {/* Modality name */}
        <div className="flex flex-col min-w-0 flex-1">
          <span className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
            {modality.name}
          </span>
          {modalityEvents.length > 0 && (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {modalityEvents.length} event{modalityEvents.length !== 1 ? 's' : ''}
            </span>
          )}
        </div>
      </div>

      {/* Right side - Navigation and visibility controls */}
      <div className="flex items-center gap-1 flex-shrink-0">
        {/* Navigation buttons (shown on hover) */}
        {isHovered && modalityEvents.length > 0 && (
          <>
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePrevEvent}
              disabled={!prevEvent}
              title={prevEvent ? `Previous event at ${prevEvent.start.toFixed(1)}s` : 'No previous events'}
              className="h-6 w-6 p-0"
            >
              <ChevronLeft className="h-3 w-3" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNextEvent}
              disabled={!nextEvent}
              title={nextEvent ? `Next event at ${nextEvent.start.toFixed(1)}s` : 'No upcoming events'}
              className="h-6 w-6 p-0"
            >
              <ChevronRight className="h-3 w-3" />
            </Button>
          </>
        )}

        {/* Visibility toggle 
        <Button
          variant="ghost"
          size="sm"
          onClick={handleToggleVisibility}
          title={isVisible ? 'Hide modality' : 'Show modality'}
          className="h-6 w-6 p-0"
        >
          {isVisible ? (
            <Eye className="h-3 w-3" />
          ) : (
            <EyeOff className="h-3 w-3" />
          )}
        </Button>*/}
      </div>
    </div>
  );
};

// Custom comparison function for more precise memoization
const arePropsEqual = (
  prevProps: NFMTimelineRowHeaderProps,
  nextProps: NFMTimelineRowHeaderProps
): boolean => {
  // Check if modality changed
  if (prevProps.modality._id !== nextProps.modality._id ||
      prevProps.modality.name !== nextProps.modality.name ||
      prevProps.modality.colorCode !== nextProps.modality.colorCode) {
    return false;
  }

  // Check if current time changed significantly (avoid micro-changes)
  if (Math.abs(prevProps.currentTime - nextProps.currentTime) > 0.01) {
    return false;
  }

  // Check if visibility changed
  if (prevProps.isVisible !== nextProps.isVisible) {
    return false;
  }

  // Check if events array changed (shallow comparison for performance)
  if (prevProps.events.length !== nextProps.events.length) {
    return false;
  }

  // Check if any events for this modality changed
  const prevModalityEvents = prevProps.events.filter(e => e.modalityId === prevProps.modality._id);
  const nextModalityEvents = nextProps.events.filter(e => e.modalityId === nextProps.modality._id);

  if (prevModalityEvents.length !== nextModalityEvents.length) {
    return false;
  }

  // Check if event IDs changed (most common case)
  for (let i = 0; i < prevModalityEvents.length; i++) {
    if (prevModalityEvents[i].id !== nextModalityEvents[i].id ||
        prevModalityEvents[i].start !== nextModalityEvents[i].start ||
        prevModalityEvents[i].end !== nextModalityEvents[i].end) {
      return false;
    }
  }

  return true;
};

// Memoize the component to prevent unnecessary re-renders when props haven't changed
export const NFMTimelineRowHeader = React.memo(NFMTimelineRowHeaderComponent, arePropsEqual);

/**
 * Container component for all timeline row headers
 */
export interface NFMTimelineRowHeadersProps {
  modalities: Doc<"modalityConfigs">[];
  events: Doc<"monitoringEvents">[];
  currentTime: number;
  visibleModalities?: Doc<"modalityConfigs">[];
  onToggleVisibility?: (modalityId: string, visible: boolean) => void;
  onNavigateToEvent?: (time: number) => void;
  onModalityVisibilityChange?: (modalityIds: string[]) => void;
  className?: string;
}

const NFMTimelineRowHeadersComponent = ({
  modalities,
  events,
  currentTime,
  visibleModalities = [],
  onToggleVisibility,
  onNavigateToEvent,
  onModalityVisibilityChange,
  className
}: NFMTimelineRowHeadersProps) => {

  // Memoize modality lookup map for better performance
  const modalityMap = useMemo(() => {
    return new Map(modalities.map(m => [m._id, { name: m.name, colorCode: m.colorCode }]));
  }, [modalities]);

  // Memoize expensive event transformation
  const timelineEvents = useMemo((): TimelineEvent[] => {
    return events.map(event => {
      const modalityInfo = modalityMap.get(event.modalityId);
      return {
        ...event,
        id: event._id,
        start: event.startTime,
        end: event.endTime || event.startTime + 1,
        effectId: `modality-${event.modalityId}`,
        selected: false,
        flexible: true,
        movable: true,
        disable: false,
        minStart: 0,
        maxEnd: Number.MAX_VALUE,
        modalityName: modalityInfo?.name || '',
        modalityColor: modalityInfo?.colorCode || '#6b7280'
      };
    });
  }, [events, modalityMap]);

  // Memoize MultiSelect options to prevent recreation on every render
  const multiSelectOptions = useMemo(() => {
    return modalities.map(modality => {
      // Create a stable icon component for each modality
      const ModalityIcon = () => (
        <div
          className="w-3 h-3 rounded-full"
          style={{ backgroundColor: modality.colorCode }}
        />
      );

      return {
        label: modality.name,
        value: modality._id,
        icon: ModalityIcon
      };
    });
  }, [modalities]);

  // Memoize visible modality IDs
  const visibleModalityIds = useMemo(() => {
    return visibleModalities.map(m => m._id);
  }, [visibleModalities]);

  // Memoize the onValueChange handler to prevent unnecessary re-renders
  const handleModalityVisibilityChange = useCallback((value: string[]) => {
    onModalityVisibilityChange?.(value);
  }, [onModalityVisibilityChange]);

  return (
    <div className={cn('timeline-row-headers', 'flex flex-col', className)}>
      {/* MultiSelect for modality visibility - positioned above row headers */}
      <div className="p-2 bg-gray-50 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
        <MultiSelect
          minimized
          options={multiSelectOptions}
          onValueChange={handleModalityVisibilityChange}
          defaultValue={visibleModalityIds}
          placeholder="Select modalities..."
          variant="default"
          animation={0}
          maxCount={0}
          className="w-full text-xs min-h-7 h-7 "
        />

      </div>

      {/* Row headers */}
      {visibleModalities
        .map(modality => (
          <NFMTimelineRowHeader
            key={modality._id}
            modality={modality}
            events={timelineEvents}
            currentTime={currentTime}
            isVisible={true} //{visibleModalities.length === 0 || visibleModalities.includes(modality._id)}
            onToggleVisibility={onToggleVisibility}
            onNavigateToEvent={onNavigateToEvent}
          />
        ))}
    </div>
  );
};

// Custom comparison function for NFMTimelineRowHeaders
const areRowHeadersPropsEqual = (
  prevProps: NFMTimelineRowHeadersProps,
  nextProps: NFMTimelineRowHeadersProps
): boolean => {

  // Check if modalities array changed (reference or content)
  if (prevProps.modalities !== nextProps.modalities) {
    if (prevProps.modalities.length !== nextProps.modalities.length) {
      return false;
    }
    // Check if any modality changed
    for (let i = 0; i < prevProps.modalities.length; i++) {
      if (prevProps.modalities[i]._id !== nextProps.modalities[i]._id ||
          prevProps.modalities[i].name !== nextProps.modalities[i].name ||
          prevProps.modalities[i].colorCode !== nextProps.modalities[i].colorCode) {
        return false;
      }
    }
  }

  // Check if events array changed (reference or content)
  if (prevProps.events !== nextProps.events) {
    if (prevProps.events.length !== nextProps.events.length) {
      return false;
    }
    // For performance, only check if the array reference changed
    // The individual row headers will handle event-specific changes
  }

  // Check if current time changed significantly
  if (Math.abs(prevProps.currentTime - nextProps.currentTime) > 0.01) {
    return false;
  }

  // Check if visible modalities changed
  if (prevProps.visibleModalities !== nextProps.visibleModalities) {
    if (prevProps.visibleModalities?.length !== nextProps.visibleModalities?.length) {
      return false;
    }
    // Check if any visible modality changed
    if (prevProps.visibleModalities && nextProps.visibleModalities) {
      for (let i = 0; i < prevProps.visibleModalities.length; i++) {
        if (prevProps.visibleModalities[i]._id !== nextProps.visibleModalities[i]._id) {
          return false;
        }
      }
    }
  }

  // Check if callback functions changed (this is likely the culprit)
  if (prevProps.onNavigateToEvent !== nextProps.onNavigateToEvent ||
      prevProps.onToggleVisibility !== nextProps.onToggleVisibility ||
      prevProps.onModalityVisibilityChange !== nextProps.onModalityVisibilityChange) {
    return false;
  }

  return true;
};

// Export memoized version of NFMTimelineRowHeaders with custom comparison
export const NFMTimelineRowHeaders = React.memo(NFMTimelineRowHeadersComponent, areRowHeadersPropsEqual);
