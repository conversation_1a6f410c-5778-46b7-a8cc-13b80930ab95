# Database Schema & API Design
## NFM System Data Architecture

### 🗃️ Core Database Schema

**Phase 2.1 Update**: Database schema now implements the Table pattern with convex-helpers for maximum type safety, automatic validation, and CRUD operations.

**Primary Entities Overview:**
- **Users**: Medical staff with role-based access
- **Projects**: Surgery sessions with team assignments
- **Patients**: Patient information and medical history
- **ModalityConfigs**: Configurable monitoring modalities
- **MonitoringEvents**: Timeline events and annotations
- **ClinicalExams**: Pre/post-operative assessments
- **StreamSessions**: Video streaming session metadata
- **StreamConfigs**: MediaMTX integration configuration
- **Reports**: Generated surgical reports

---

### 🔧 Phase 2.1: Type-Safe Schema Implementation

The entire schema has been refactored using the Table pattern from convex-helpers:

```typescript
// SEE convex/schema.ts for complete implementation

// Table pattern provides:
// - Type-safe field definitions
// - Automatic validator export
// - Schema-to-function synchronization
// - CRUD operation generation

export const Users = Table("users", {
  email: v.optional(v.string()),
  name: v.optional(v.string()),
  role: v.optional(userRoleValidator),
  // ... complete field definitions
});

// Exported validators for reuse
export const { role: userRoleValidator } = Users.withoutSystemFields.fields;
export const { status: projectStatusValidator } = Projects.withoutSystemFields.fields;
export const { gender: patientGenderValidator } = Patients.withoutSystemFields.fields;
```

### 📋 CRUD Operations Available

All tables now have auto-generated CRUD operations:

```typescript
// SEE convex/crudHelpers.ts for implementation

import { crud } from "convex-helpers/server/crud";

// Available CRUD operations for each table:
export const usersCrud = crud(schema, "users");           // User management
export const patientsCrud = crud(schema, "patients");     // Patient records
export const projectsCrud = crud(schema, "projects");     // Surgery sessions
export const streamConfigsCrud = crud(schema, "streamConfigs"); // Stream configuration
export const monitoringEventsCrud = crud(schema, "monitoringEvents"); // Event logging

// Usage pattern:
// CREATE: await ctx.runMutation(usersCrud.create, userData);
// READ:   await ctx.runQuery(usersCrud.read, { id: userId });
// UPDATE: await ctx.runMutation(usersCrud.update, { id: userId, patch: updates });
// DELETE: await ctx.runMutation(usersCrud.destroy, { id: userId });
```

**Recommendation**: Use CRUD operations for simple data operations to reduce boilerplate code and ensure consistency.

---

### 👥 Users & Authentication

```typescript
// SEE convex/users.ts for complete type-safe implementation

// Schema definition (convex/schema.ts)
export const Users = Table("users", {
    email: v.optional(v.string()),
    name: v.optional(v.string()),
    role: v.optional(v.union(
      v.literal("default"),
      v.literal("surgeon"),
      v.literal("anesthesiologist"), 
      v.literal("neurophysiologist"),
      v.literal("admin"),
      v.literal("technician")
    )),
    specialization: v.optional(v.string()),
    license: v.optional(v.string()),
    credentials: v.optional(v.array(v.string())),
    department: v.optional(v.string()),
    phone: v.optional(v.string()),
    preferences: v.optional(v.object({
      defaultModalities: v.array(v.string()),
      notificationSettings: v.object({
        email: v.boolean(),
        inApp: v.boolean(),
        criticalEvents: v.boolean(),
      }),
      uiPreferences: v.object({
        theme: v.union(v.literal("light"), v.literal("dark")),
        timelineView: v.union(v.literal("compact"), v.literal("expanded")),
        defaultTimeScale: v.number(),
      }),
    })),
    isActive: v.optional(v.boolean()),
    lastLogin: v.optional(v.number()),
    createdAt: v.optional(v.number()),
});

// Type-safe API functions now available:
// - getUsers: Query with role and status filtering
// - getUser: Single user retrieval
// - getUsersByRole: Team assignment support
// - updateUser: Admin OR self-update with field restrictions
// - updateUserPreferences: User preference management
// - createUser: Admin-only user creation
// - deactivateUser/reactivateUser: User lifecycle management
```

**Key Improvements in Phase 2.1**:
- ✅ Auto-updating function arguments when schema changes
- ✅ Type-safe field access with IDE autocomplete
- ✅ Proper authorization logic (admin OR self for updates)
- ✅ Field-level update restrictions for non-admin users

---

### 🏥 Projects & Surgery Sessions

```typescript
// SEE convex/projects.ts for complete type-safe implementation

export const Projects = Table("projects", {
  // Basic Project Information
  projectCode: v.string(),
  patientId: v.id("patients"),
  surgeryType: v.string(),
  surgerySubtype: v.optional(v.string()),
  operatingRoom: v.string(),
  
  // Project Status with exported validator
  status: v.union(
    v.literal("scheduled"),
    v.literal("pre-op"),
    v.literal("in-progress"),
    v.literal("post-op"),
    v.literal("completed"),
    v.literal("cancelled")
  ),
  
  // Team Assignments
  teamMembers: v.object({
    primarySurgeon: v.id("users"),
    assistingSurgeons: v.array(v.id("users")),
    anesthesiologist: v.id("users"),
    neurophysiologist: v.id("users"),
    nurses: v.array(v.id("users")),
    technicians: v.array(v.id("users")),
  }),
  
  // Scheduling & Documentation fields...
  // (See schema.ts for complete definition)
});

// Type-safe API functions now available:
// - getProjects: Enhanced filtering with patient/user data
// - getProject: Single project with related data
// - createProject: Auto-generated project codes
// - updateProjectStatus: Status transitions with timestamps
// - getActiveProjectsCount: Dashboard metrics
// - getTodaysProjects: Schedule management
```

---

### 🩺 Patients & Medical Records

```typescript
// SEE convex/patients.ts for complete type-safe implementation

export const Patients = Table("patients", {
  firstName: v.string(),
  lastName: v.string(),
  address: v.optional(v.string()),
  contactPhone: v.optional(v.string()),
  medicalRecordNumber: v.string(),
  dateOfBirth: v.number(),
  gender: v.union(v.literal("male"), v.literal("female"), v.literal("other")),
  emergencyContact: v.optional(v.object({
    name: v.string(),
    relationship: v.string(),
    phone: v.string(),
  })),
  allergies: v.optional(v.string()),
  medications: v.array(v.object({
    name: v.string(),
    dosage: v.string(),
    frequency: v.string(),
  })),
  medicalHistory: v.array(v.string()),
  diagnosis: v.optional(v.string()),
  notes: v.optional(v.string()),
  createdBy: v.id("users"),
  createdAt: v.optional(v.number()),
  updatedAt: v.optional(v.number()),
});

// Type-safe API functions:
// - getPatients: Search and pagination
// - getPatient: Single patient with related projects
// - createPatient: MRN uniqueness validation
// - updatePatient: Comprehensive patient updates
```

---

### 📹 Stream Management & MediaMTX Integration

```typescript
// SEE convex/streams.ts and convex/streamActions.ts

export const StreamConfigs = Table("streamConfigs", {
  pathName: v.string(),
  sourceUrl: v.string(),
  streamType: v.optional(v.union(
    v.literal("inomed"),
    v.literal("microscope"),
    v.literal("camera"),
    v.literal("screen"),
    v.literal("external"),
    v.literal("local")
  )),
  description: v.optional(v.string()),
  isActive: v.boolean(),
  isEnabled: v.boolean(),
  isLive: v.optional(v.boolean()),
  isInMediaMTX: v.optional(v.boolean()),
  viewers: v.optional(v.number()),
  originatedFromMediaMTX: v.optional(v.boolean()),
  lastSyncStatus: v.optional(v.union(
    v.literal("synced"),
    v.literal("needs_sync"),
    v.literal("conflict")
  )),
  createdAt: v.number(),
  updatedAt: v.number(),
});

// Complete MediaMTX integration with type safety:
// - saveStreamConfig: Stream configuration storage
// - updateLiveStatus: Real-time status updates
// - updateSyncStatus: MediaMTX synchronization tracking
// - updateStreamConfigFromMediaMTX: Import from MediaMTX
// - getLiveSession: Live session management with authorization
// - startLiveSession/stopLiveSession: Session lifecycle
// - joinSession: User participation tracking

// MediaMTX Actions (streamActions.ts):
// - listStreamSources: Database + MediaMTX status combination
// - configureStreamSource: Stream setup with validation
// - testStreamConnection: Connection testing
// - editStreamConfig: Configuration updates
// - toggleStreamSource: Enable/disable streams
// - importFromMediaMTX: Bulk import operations
// - resetToMediaMTXConfigs: Full reset functionality
// - deleteStreamConfig: Stream removal
// - syncMediaMTXPaths: Bidirectional synchronization
```

---

### 📊 Monitoring Events & Clinical Data

```typescript
// Event management with type-safe validators
export const MonitoringEvents = Table("monitoringEvents", {
  projectId: v.id("projects"),
  sessionId: v.id("sessionConfigs"),
  timestamp: v.number(),
  modalityId: v.id("modalityConfigs"),
  eventType: v.string(),
  severity: v.union(
    v.literal("normal"),
    v.literal("warning"),
    v.literal("critical")
  ),
  title: v.string(),
  description: v.string(),
  location: v.optional(v.string()),
  screenshots: v.array(v.id("_storage")),
  videoClip: v.optional(v.object({
    startTime: v.number(),
    endTime: v.number(),
    storageId: v.id("_storage"),
  })),
  reviewerId: v.optional(v.id("users")),
  reviewStatus: v.union(
    v.literal("unreviewed"),
    v.literal("under-review"),
    v.literal("reviewed"),
    v.literal("flagged")
  ),
  reviewNotes: v.optional(v.string()),
  createdBy: v.id("users"),
  createdAt: v.number(),
  updatedAt: v.number(),
});

// Clinical examinations
export const ClinicalExams = Table("clinicalExams", {
  projectId: v.id("projects"),
  type: v.union(v.literal("pre-op"), v.literal("post-op")),
  // ... detailed clinical assessment fields
  // (See schema.ts for complete definition)
});
```

---

## 🚀 Phase 2.1 Benefits Summary

### 1. **Automatic Schema Synchronization**
- Schema changes automatically propagate to all functions
- No manual validator maintenance required
- Compile-time errors prevent schema mismatches

### 2. **Type Safety Enhancements**
- Full TypeScript type inference
- IDE autocomplete for all database operations
- Runtime validation consistency

### 3. **CRUD Operations Integration**
- Pre-built CRUD functions for all tables
- Consistent API patterns across the application
- Reduced development time for simple operations

### 4. **Enhanced Developer Experience**
- Single source of truth for all field definitions
- DRY principle enforced throughout codebase
- Maintainable and scalable architecture

### 5. **Production-Ready Features**
- Complete MediaMTX integration with type safety
- Robust user management with proper authorization
- Comprehensive session and event management

All database operations now benefit from maximum type safety while maintaining full functionality and performance.
