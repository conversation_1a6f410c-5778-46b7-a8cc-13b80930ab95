# Video-Timeline Integration Architecture

## Overview

This document describes the unified video-timeline synchronization architecture implemented in the NFM project. The new architecture replaces the previous complex state management system with a single, robust state management solution that provides seamless synchronization between video playback and timeline interactions.

## Architecture Goals

### Problems Solved
- **Multiple State Sources**: Eliminated 4+ different state management layers that caused conflicts
- **Circular Dependencies**: Removed complex event listener chains that created infinite loops
- **Performance Issues**: Reduced excessive re-renders and memory leaks
- **Missing Seeking States**: Added proper seeking state management with visual feedback
- **Debugging Complexity**: Simplified state flow for easier maintenance and debugging

### Key Benefits
- ✅ Single source of truth for all video-timeline state
- ✅ Predictable state updates with useReducer pattern
- ✅ Intelligent synchronization with configurable thresholds
- ✅ Performance optimizations with debouncing and batching
- ✅ Comprehensive error handling and loading states
- ✅ Backward compatibility with legacy components

## Core Components

### 1. VideoTimelineProvider Context

**Location**: `components/contexts/VideoTimelineContext.tsx`

The central state management component that provides:

```typescript
interface VideoTimelineState {
  // Core playback state
  currentTime: number;
  duration: number;
  isPlaying: boolean;
  playbackRate: number;
  
  // Seeking state management
  isSeeking: boolean;
  seekingTime: number | null;
  seekingSource: 'video' | 'timeline' | null;
  
  // Loading and error states
  isVideoLoading: boolean;
  isTimelineLoading: boolean;
  videoError: string | null;
  timelineError: string | null;
}
```

**Key Features**:
- useReducer-based state management for predictable updates
- Seeking state tracking to prevent feedback loops
- External callback refs to prevent stale closures
- Comprehensive action interface for all state mutations

### 2. VideoTimelineSync Hook

**Location**: `hooks/useVideoTimelineSync.ts`

Enhanced synchronization logic with configurable behavior:

```typescript
interface VideoTimelineSyncConfig {
  timeSyncThreshold?: number;     // Minimum time difference to trigger sync
  seekDebounceMs?: number;        // Debounce time for seek operations
  enableBidirectionalSync?: boolean;
  pauseOnSeek?: boolean;
  resumeAfterSeek?: boolean;
  syncUpdateInterval?: number;
  enableSmoothing?: boolean;
}
```

**Key Features**:
- Intelligent sync logic with configurable thresholds
- Debounced seek operations to prevent excessive calls
- Bidirectional synchronization between video and timeline
- Performance optimizations with update intervals
- Smooth transition support

### 3. ReactPlayerWrapper Component

**Location**: `components/video/ReactPlayerWrapper.tsx`

Lazy-loaded video player with unified state integration:

**Key Features**:
- Dynamic import with Suspense for performance
- Seamless integration with VideoTimelineProvider
- Enhanced seeking capabilities compared to WebRTCPlayer
- Comprehensive error handling and loading states
- Support for multiple video formats and streaming protocols

### 4. Updated Timeline Components

**NFMTimelineComplete**: Integrated with VideoTimelineProvider for unified state
**NFMTimelineEditor**: Enhanced with synchronization hooks
**NFMTimelineControls**: Direct integration with unified state actions

## State Flow Architecture

```mermaid
graph TD
    A[VideoTimelineProvider] --> B[VideoTimelineState]
    B --> C[currentTime]
    B --> D[isPlaying]
    B --> E[isSeeking]
    B --> F[seekingTime]
    B --> G[duration]
    B --> H[playbackRate]
    
    I[ReactPlayerWrapper] --> J[Video Events]
    J --> K[handleVideoTimeUpdate]
    K --> L[updateCurrentTime]
    L --> A
    
    M[NFMTimelineEditor] --> N[Timeline Events]
    N --> O[handleTimelineTimeUpdate]
    O --> P[updateCurrentTime]
    P --> A
    
    Q[Timeline Cursor Drag] --> R[startSeeking]
    R --> A
    S[Timeline Cursor Release] --> T[endSeeking + seekTo]
    T --> A
    
    A --> U[Video Seek Command]
    A --> V[Timeline Sync Command]
    
    style A fill:#ccffcc
    style B fill:#ccffcc
    style L fill:#e6f3ff
    style P fill:#e6f3ff
    
    classDef state fill:#ccffcc,stroke:#00aa00,stroke-width:2px
    classDef action fill:#e6f3ff,stroke:#0066cc,stroke-width:2px
```

## Implementation Details

### Synchronization Behaviors

#### 1. Video-to-Timeline Sync
- Video progress updates trigger `handleVideoTimeUpdate`
- Updates are throttled based on `timeSyncThreshold` and `syncUpdateInterval`
- Timeline cursor position updates to match video progress
- No sync during seeking to prevent interference

#### 2. Timeline-to-Video Sync
- Timeline interactions trigger `handleTimelineTimeUpdate`
- Bidirectional sync can be enabled/disabled via configuration
- Video seeks to match timeline position changes
- Debounced to prevent excessive seek operations

#### 3. Seeking State Management
- `startSeeking`: Initiated when timeline cursor drag begins
- `updateSeeking`: Continuous updates during drag operation
- `endSeeking`: Finalizes seek operation and triggers video sync
- Visual feedback provided during seeking state

### Performance Optimizations

#### 1. Debouncing and Throttling
```typescript
// Configurable thresholds prevent excessive updates
const defaultConfig = {
  timeSyncThreshold: 0.1,      // 100ms threshold
  seekDebounceMs: 100,         // 100ms debounce
  syncUpdateInterval: 100,     // 100ms update interval
};
```

#### 2. Lazy Loading
- ReactPlayer is dynamically imported with Suspense
- Reduces initial bundle size
- Improves page load performance

#### 3. Intelligent State Updates
- useReducer prevents unnecessary re-renders
- State updates only occur when values actually change
- Seeking state prevents feedback loops during user interactions

## Usage Examples

### Basic Implementation

```tsx
import { VideoTimelineProvider } from '@/components/contexts/VideoTimelineContext';
import { LiveStreamCard } from '@/components/video/LiveStreamCard';
import { NFMTimelineComplete } from '@/components/timeline/NFMTimelineComplete';

function LiveMonitoringPage() {
  return (
    <VideoTimelineProvider
      initialDuration={3600}
      onVideoSeek={(time) => console.log('Video seek:', time)}
      onTimelineSeek={(time) => console.log('Timeline seek:', time)}
    >
      <LiveStreamCard streamPath="rtmp://example.com/stream" />
      <NFMTimelineComplete />
    </VideoTimelineProvider>
  );
}
```

### Advanced Configuration

```tsx
import { useVideoTimelineSync } from '@/hooks/useVideoTimelineSync';

function CustomTimelineComponent() {
  const sync = useVideoTimelineSync({
    timeSyncThreshold: 0.2,
    seekDebounceMs: 150,
    enableBidirectionalSync: true,
    pauseOnSeek: false,
  });

  return (
    <div>
      <button onClick={sync.togglePlayPause}>
        {sync.isPlaying ? 'Pause' : 'Play'}
      </button>
      <div>Current Time: {sync.currentTime.toFixed(2)}s</div>
      <div>Seeking: {sync.isSeeking ? 'Yes' : 'No'}</div>
    </div>
  );
}
```

## Migration Guide

### From Legacy Implementation

The new architecture maintains backward compatibility but legacy props are deprecated:

```tsx
// ❌ Legacy (deprecated)
<NFMTimelineComplete
  currentTime={currentTime}
  isPlaying={isPlaying}
  onTimeChange={handleTimeChange}
  onPlayStateChange={handlePlayStateChange}
/>

// ✅ New (recommended)
<VideoTimelineProvider>
  <NFMTimelineComplete />
</VideoTimelineProvider>
```

### Breaking Changes

1. **WebRTCPlayer Replacement**: LiveStreamCard now uses ReactPlayerWrapper by default
2. **State Management**: Direct state props are deprecated in favor of context
3. **Event Handlers**: Component-level event handlers replaced with context actions

## Testing and Validation

### Test Scenarios

1. **Basic Synchronization**
   - Play/pause from timeline controls
   - Video progress updates timeline cursor
   - Timeline clicks seek video

2. **Seeking Behavior**
   - Timeline cursor dragging
   - Click-to-seek functionality
   - Seeking state visual feedback

3. **Performance**
   - No excessive re-renders
   - Smooth seeking without lag
   - Memory usage stability

4. **Error Handling**
   - Invalid video URLs
   - Network interruptions
   - Graceful degradation

### Debug Tools

Enable development mode logging:
```typescript
// Development warnings for legacy props
if (process.env.NODE_ENV === 'development') {
  console.warn('[Component] Legacy props detected...');
}
```

## Future Enhancements

### Planned Features
- Enhanced timeline cursor dragging with snap-to-grid
- Multi-video synchronization support
- Advanced seeking animations
- Timeline zoom synchronization with video quality
- Keyboard shortcut integration

### Performance Improvements
- Virtual scrolling for large timelines
- Web Workers for heavy synchronization calculations
- Canvas-based timeline rendering for better performance

## Troubleshooting

### Common Issues

1. **Sync Lag**: Adjust `timeSyncThreshold` and `syncUpdateInterval`
2. **Excessive Seeking**: Increase `seekDebounceMs` value
3. **Memory Leaks**: Ensure proper cleanup in useEffect hooks
4. **State Conflicts**: Verify single VideoTimelineProvider wrapper

### Debug Commands

```typescript
// Access sync status
const syncStatus = videoTimelineSync.getSyncStatus();
console.log('Sync Status:', syncStatus);

// Force synchronization
videoTimelineSync.forceSync('video'); // or 'timeline'
```

## Conclusion

The unified video-timeline integration provides a robust, performant, and maintainable solution for synchronizing video playback with timeline interactions. The architecture eliminates previous complexity while providing enhanced functionality and better user experience.

For implementation questions or issues, refer to the component documentation or create an issue in the project repository.
