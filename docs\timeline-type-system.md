# Timeline Type System Documentation

## Overview
This document maps out all timeline-related types, their purposes, origins, and relationships to identify opportunities for simplification.

## Current Type Complexity Issues

### 1. Multiple Event Types (MAJOR ISSUE)
We currently have **4 different event types** that represent the same data:

#### `Doc<"monitoringEvents">` (Convex Schema)
- **Origin**: `convex/_generated/dataModel.ts` (auto-generated)
- **Purpose**: Database schema type
- **Fields**: `_id`, `startTime`, `endTime`, `modalityId`, `eventType`, `severity`, `title`, `description`
- **Used in**: Database queries, mutations

#### `TimelineEvent` (types/timeline.ts)
- **Origin**: `types/timeline.ts:11`
- **Purpose**: Simple extension of MonitoringEvent
- **Extends**: `MonitoringEvent & { modalityName: string; modalityColor: string; }`
- **Used in**: Live monitoring page imports

#### `TimelineEvent` (components/timeline/interface/action.ts)
- **Origin**: `components/timeline/interface/action.ts:9`
- **Purpose**: Timeline editor compatibility
- **Extends**: `Omit<Doc<"monitoringEvents">, '_id'>`
- **Fields**: `id` (instead of `_id`), `start` (instead of `startTime`), `end` (instead of `endTime`), `effectId`
- **Used in**: Timeline components, row headers

#### `NFMTimelineEvent` (types/timelineEditor.ts)
- **Origin**: `types/timelineEditor.ts:12`
- **Purpose**: Extended timeline event with NFM-specific properties
- **Extends**: `TimelineEvent` (from interface/action.ts)
- **Additional Fields**: `originalEvent`, `eventType`, `severity`, `color`, `editable`, `deletable`
- **Used in**: Event handlers, action renderers

### 2. Type Import Confusion
Multiple files import different `TimelineEvent` types:
```typescript
// app/dashboard/live-monitoring/page.tsx
import { TimelineEvent as TimelineEditorEvent } from "@/components/timeline";
import { TimelineEvent } from "@/types/timeline";

// This creates confusion and requires type assertions
```

### 3. Redundant Type Conversions
Components constantly convert between types:
```typescript
// NFMTimelineRowHeader.tsx:177
const timelineEvents: TimelineEvent[] = events.map(event => ({
  ...event,
  id: event._id,
  start: event.startTime,
  end: event.endTime || event.startTime + 1,
  // ... more conversions
}));
```

## Recommended Simplification

### Phase 1: Consolidate to Single Event Type
**Recommendation**: Use `TimelineEvent` from `components/timeline/interface/action.ts` as the single source of truth.

**Rationale**:
1. Already extends MonitoringEvent with timeline-specific fields
2. Compatible with react-timeline-editor requirements
3. Most comprehensive and well-defined

### Phase 2: Update All Imports
Replace all other TimelineEvent imports with the consolidated type:
```typescript
// Single import across all files
import { TimelineEvent } from '@/components/timeline/interface/timeline';
```

### Phase 3: Remove Redundant Types
- Delete `TimelineEvent` from `types/timeline.ts`
- Merge useful properties from `NFMTimelineEvent` into the main `TimelineEvent`
- Remove type conversion utilities

## Type Mapping Strategy

### Current State (Complex)
```
Database → Doc<"monitoringEvents">
    ↓ (conversion)
Timeline → TimelineEvent (types/timeline.ts)
    ↓ (conversion)  
Editor → TimelineEvent (interface/action.ts)
    ↓ (conversion)
NFM → NFMTimelineEvent (types/timelineEditor.ts)
```

### Target State (Simple)
```
Database → Doc<"monitoringEvents">
    ↓ (single conversion)
Timeline → TimelineEvent (interface/action.ts)
```

## Implementation Plan

1. **Consolidate Event Types**: Merge all event types into single `TimelineEvent`
2. **Update Imports**: Replace all timeline event imports
3. **Remove Conversions**: Eliminate type conversion functions
4. **Update Components**: Ensure all components use unified type
5. **Clean Up**: Remove redundant type definitions

## Benefits of Simplification

1. **Reduced Complexity**: Single event type instead of 4
2. **Better Type Safety**: No more type assertions or conversions
3. **Improved DX**: Clear, consistent imports
4. **Easier Maintenance**: Single source of truth for event structure
5. **Performance**: Eliminate runtime type conversions

## Files Requiring Updates

### High Priority (Type Definitions)
- `types/timeline.ts` - Remove redundant TimelineEvent
- `types/timelineEditor.ts` - Merge NFMTimelineEvent properties
- `components/timeline/interface/action.ts` - Enhance main TimelineEvent

### Medium Priority (Components)
- `app/dashboard/live-monitoring/page.tsx` - Update imports and handlers
- `components/timeline/NFMTimelineRowHeader.tsx` - Remove conversions
- `components/timeline/NFMTimelineEditor.tsx` - Update type usage

### Low Priority (Utilities)
- `components/timeline/effects/eventRenderers.tsx` - Update prop types
- `components/timeline/NFMTimelineContextMenu.tsx` - Update interfaces
