.timeline-editor-action {
  position: absolute;
  left: 0;
  top: 0;
  background-color: #2f3134;
}

.timeline-editor-action .timeline-editor-action-left-stretch,
.timeline-editor-action .timeline-editor-action-right-stretch {
  position: absolute;
  top: 0;
  width: 10px;
  border-radius: 4px;
  height: 100%;
  overflow: hidden;
}

.timeline-editor-action .timeline-editor-action-left-stretch::after,
.timeline-editor-action .timeline-editor-action-right-stretch::after {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  border-radius: 4px;
  border-top: 28px solid transparent;
  border-bottom: 28px solid transparent;
}

.timeline-editor-action .timeline-editor-action-left-stretch {
  left: 0;
}

.timeline-editor-action .timeline-editor-action-left-stretch::after {
  left: 0;
  content: "";
  border-left: 7px solid rgba(255, 255, 255, .1);
  border-right: 7px solid transparent;
}

.timeline-editor-action .timeline-editor-action-right-stretch {
  right: 0;
}

.timeline-editor-action .timeline-editor-action-right-stretch::after {
  right: 0;
  content: "";
  border-right: 7px solid rgba(255, 255, 255, .1);
  border-left: 7px solid transparent;
}
