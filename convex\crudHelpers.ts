// CRUD utilities using convex-helpers for type-safe operations
import { crud } from "convex-helpers/server/crud";
import schema from "./schema";

// Generate CRUD operations for each table using the Table pattern
// These are type-safe and automatically handle validation

// Users CRUD operations
export const usersCrud = crud(schema, "users");

// Patients CRUD operations  
export const patientsCrud = crud(schema, "patients");

// Projects CRUD operations
export const projectsCrud = crud(schema, "projects");

// Stream configurations CRUD operations
export const streamConfigsCrud = crud(schema, "streamConfigs");

// Monitoring events CRUD operations
export const monitoringEventsCrud = crud(schema, "monitoringEvents");

// Example usage in other files:
// 
// import { usersCrud } from "./crudHelpers";
// 
// // Create a user
// const userId = await ctx.runMutation(usersCrud.create, {
//   email: "<EMAIL>",
//   name: "<PERSON>",
//   role: "surgeon"
// });
//
// // Read a user
// const user = await ctx.runQuery(usersCrud.read, { id: userId });
//
// // Update a user
// await ctx.runMutation(usersCrud.update, { 
//   id: userId, 
//   patch: { name: "Jane Doe" } 
// });
