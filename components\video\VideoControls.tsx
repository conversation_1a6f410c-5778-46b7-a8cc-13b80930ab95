"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>lider } from "@/components/ui/slider";
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Camera,
  Maximize2,
  PictureInPicture2
} from "lucide-react";
import { cn } from "@/lib/utils";

interface VideoControlsProps {
  isPlaying: boolean;
  isMuted: boolean;
  volume: number[];
  showControls: boolean;
  isRecording?: boolean;
  onPlayPause: () => void;
  onVolumeToggle: () => void;
  onVolumeChange: (value: number[]) => void;
  onScreenshot: () => void;
  onFullscreen: () => void;
  onPictureInPicture: () => void;
  className?: string;
}

export function VideoControls({
  isPlaying,
  isMuted,
  volume,
  showControls,
  isRecording = false,
  onPlayPause,
  onVolumeToggle,
  onVolumeChange,
  onScreenshot,
  onFullscreen,
  onPictureInPicture,
  className
}: VideoControlsProps) {
  return (
    <div 
      className={cn(
        "absolute bottom-4 left-4 right-4 transition-opacity duration-300",
        showControls ? "opacity-100" : "opacity-0",
        className
      )}
    >
      <div className="flex items-center gap-2 mb-4">
        {/* Play/Pause (only for evaluation mode) */}
        {!isRecording && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onPlayPause}
            className="text-white hover:bg-white/20"
          >
            {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
          </Button>
        )}

        {/* Volume Controls */}
        <Button
          size="sm"
          variant="ghost"
          onClick={onVolumeToggle}
          className="text-white hover:bg-white/20"
        >
          {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
        </Button>

        <div className="w-20">
          <Slider
            value={volume}
            onValueChange={onVolumeChange}
            max={100}
            step={1}
            className="w-full"
          />
        </div>

        <div className="flex-1" />

        {/* Action Buttons */}
        <Button
          size="sm"
          variant="ghost"
          onClick={onScreenshot}
          className="text-white hover:bg-white/20"
          title="Take Screenshot"
        >
          <Camera className="h-4 w-4" />
        </Button>

        <Button
          size="sm"
          variant="ghost"
          onClick={onPictureInPicture}
          className="text-white hover:bg-white/20"
          title="Picture in Picture"
        >
          <PictureInPicture2 className="h-4 w-4" />
        </Button>

        <Button
          size="sm"
          variant="ghost"
          onClick={onFullscreen}
          className="text-white hover:bg-white/20"
          title="Fullscreen"
        >
          <Maximize2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
