import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { MonitoringEvents, Projects, StreamConfigs, StreamSessions } from "./schema";
import { pick } from "convex-helpers";

// Type-safe field selectors
const streamConfigFields = StreamConfigs.withoutSystemFields;
const streamConfigCreateFields = pick(streamConfigFields, [
  "pathName", "sourceUrl", "streamType", "description", "isEnabled"
]);
// Add fields when allowing more edits
const streamConfigUpdateFields = pick(streamConfigFields, [
  "sourceUrl", "streamType", "description" 
]);

// Store stream configuration
export const saveStreamConfig = mutation({
  args: streamConfigCreateFields,
  returns: v.id("streamConfigs"),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const existing = await ctx.db
      .query("streamConfigs")
      .withIndex("by_path", (q) => q.eq("pathName", args.pathName))
      .first();

    if (existing) {
      throw new Error(`Stream configuration already exists`);
    }

    return await ctx.db.insert("streamConfigs", {
      pathName: args.pathName,
      sourceUrl: args.sourceUrl,
      streamType: args.streamType,
      description: args.description,
      isActive: true,
      isEnabled: args.isEnabled ?? true,
      originatedFromMediaMTX: false,
      isLive: false,
      viewers: 0,
      isInMediaMTX: false,
      lastSyncStatus: 'synced',
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  },
});

// Update live status for stream
export const updateLiveStatus = mutation({
  args: {
    pathName: v.string(),
    isLive: v.boolean(),
    viewers: v.number(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const config = await ctx.db
      .query("streamConfigs")
      .withIndex("by_path", (q) => q.eq("pathName", args.pathName))
      .first();

    if (config) {
      await ctx.db.patch(config._id, {
        isLive: args.isLive,
        viewers: args.viewers,
        updatedAt: Date.now(),
      });
    }
    return null;
  },
});

// Update sync status for stream
export const updateSyncStatus = mutation({
  args: {
    pathName: v.string(),
    isInMediaMTX: v.boolean(),
    lastSyncStatus: v.union(
      v.literal("synced"),
      v.literal("needs_sync"),
      v.literal("conflict")
    ),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const config = await ctx.db
      .query("streamConfigs")
      .withIndex("by_path", (q) => q.eq("pathName", args.pathName))
      .first();

    if (config) {
      await ctx.db.patch(config._id, {
        isInMediaMTX: args.isInMediaMTX,
        lastSyncStatus: args.lastSyncStatus,
        updatedAt: Date.now(),
      });
    }
    return null;
  },
});

// Update stream config from MediaMTX data
export const updateStreamConfigFromMediaMTX = mutation({
  args: {
    pathName: v.string(),
    sourceUrl: v.string(),
    isLive: v.boolean(),
    viewers: v.number(),
    isInMediaMTX: v.boolean(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const config = await ctx.db
      .query("streamConfigs")
      .withIndex("by_path", (q) => q.eq("pathName", args.pathName))
      .first();

    if (config) {
      await ctx.db.patch(config._id, {
        sourceUrl: args.sourceUrl,
        isLive: args.isLive,
        viewers: args.viewers,
        isInMediaMTX: args.isInMediaMTX,
        lastSyncStatus: 'synced',
        updatedAt: Date.now(),
      });
    }
    return null;
  },
});

// Update stream configuration
export const updateStreamConfig = mutation({
  args: {
    pathName: v.string(),
    ...streamConfigUpdateFields,
  },
  returns: v.id("streamConfigs"),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const { pathName, ...updateData } = args;

    const config = await ctx.db
      .query("streamConfigs")
      .withIndex("by_path", (q) => q.eq("pathName", pathName))
      .first();

    if (!config) {
      throw new Error("Stream configuration not found");
    }

    await ctx.db.patch(config._id, {
      ...updateData,
      lastSyncStatus: 'needs_sync',
      updatedAt: Date.now(),
    });

    return config._id;
  },
});

// Delete stream configuration
export const deleteStreamConfig = mutation({
  args: { pathName: v.string() },
  returns: v.id("streamConfigs"),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const config = await ctx.db
      .query("streamConfigs")
      .withIndex("by_path", (q) => q.eq("pathName", args.pathName))
      .first();

    if (!config) {
      throw new Error("Stream configuration not found");
    }

    await ctx.db.delete(config._id);
    return config._id;
  },
});

// Toggle stream enabled status
export const toggleStreamConfig = mutation({
  args: {
    pathName: v.string(),
    isEnabled: v.boolean(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const config = await ctx.db
      .query("streamConfigs")
      .withIndex("by_path", (q) => q.eq("pathName", args.pathName))
      .first();

    if (config) {
      await ctx.db.patch(config._id, {
        isEnabled: args.isEnabled,
        updatedAt: Date.now(),
      });
    }
    return null;
  },
});

// Get single stream configuration
export const getStreamConfig = query({
  args: { pathName: v.string() },
  returns: v.union(StreamConfigs.doc, v.null()),
  handler: async (ctx, args) => {
    return await ctx.db
      .query("streamConfigs")
      .withIndex("by_path", (q) => q.eq("pathName", args.pathName))
      .first();
  },
});

// Get all stream configurations
export const getAllStreamConfigs = query({
  args: {},
  returns: v.array(StreamConfigs.doc),
  handler: async (ctx) => {
    return await ctx.db
      .query("streamConfigs")
      .collect();
  },
});

// Get active stream configurations
export const getActivetreamConfigs = query({
  args: {},
  returns: v.array(StreamConfigs.doc),
  handler: async (ctx) => {
    return await ctx.db
      .query("streamConfigs")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .collect();
  },
});

// Real-time query for live session status
export const getLiveSession = query({
  args: { projectId: v.id("projects") },
  returns: v.union(
    v.object({
      session: v.object(StreamSessions.doc.fields),
      project: v.object(Projects.doc.fields),
      recentEvents: v.array(v.object(MonitoringEvents.doc.fields)),
      duration: v.number(),
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    // Get current user for authorization
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email || "nulll"))
      .unique();
    
    if (!user) throw new Error("User not found");

    // Get project with team validation
    const project = await ctx.db.get(args.projectId);
    if (!project) throw new Error("Project not found");
    
    // Check if user is part of project team
    const teamMemberIds = [
      project.teamMembers.primarySurgeon,
      project.teamMembers.anesthesiologist,
      project.teamMembers.neurophysiologist,
      ...project.teamMembers.assistingSurgeons,
      ...project.teamMembers.nurses,
      ...project.teamMembers.technicians,
    ];
    
    if (!teamMemberIds.includes(user._id) && user.role !== "admin") {
      throw new Error("Not authorized for this project");
    }

    // Get active stream session
    const streamSession = await ctx.db
      .query("streamSessions")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .filter((q) => q.neq(q.field("status"), "stopped"))
      .first();

    if (!streamSession) return null;

    // Get recent events for session
    const recentEvents = await ctx.db
      .query("monitoringEvents")
      .withIndex("by_session", (q) => q.eq("sessionId", streamSession._id))
      .order("desc")
      .take(10);

    return {
      session: streamSession,
      project,
      recentEvents,
      duration: streamSession.startTime ? Date.now() - streamSession.startTime : 0,
    };
  },
});

// Start a new live session
export const startLiveSession = mutation({
  args: {
    projectId: v.id("projects"),
    streamSources: v.array(v.object({
      name: v.string(),
      url: v.string(),
      quality: v.string(),
    })),
  },
  returns: v.id("streamSessions"),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email || "null"))
      .unique();
    
    if (!user) throw new Error("User not found");

    // Check for existing active session
    const existingSession = await ctx.db
      .query("streamSessions")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .filter((q) => q.neq(q.field("status"), "stopped"))
      .first();
    
    if (existingSession) {
      throw new Error("A session is already active for this project");
    }

    // Generate unique session ID
    //const sessionId = `session_${args.projectId}_${Date.now()}`;
    
    // Create stream session
    const streamSessionId = await ctx.db.insert("streamSessions", {
      projectId: args.projectId,
    //  sessionId,
      status: "starting",
      streamSources: args.streamSources.map(source => ({
        ...source,
        isActive: true,
      })),
      startTime: Date.now(),
      viewers: [user._id],
      eventCount: 0,
    });

    // Update project status
    await ctx.db.patch(args.projectId, {
      status: "in-progress",
      actualStart: Date.now(),
      updatedAt: Date.now(),
    });

    return streamSessionId;
  },
});

// Stop live session
export const stopLiveSession = mutation({
  args: {
    projectId: v.id("projects"),
    sessionId: v.id("streamSessions"),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const streamSession = await ctx.db.get(args.sessionId);
      /*.query("streamSessions")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .filter((q) => q.eq(q.field("_id"), args.sessionId))
      .unique();*/

    if (!streamSession) throw new Error("Session not found");

    // Update session status
    await ctx.db.patch(streamSession._id, {
      status: "stopped",
      endTime: Date.now(),
    });

    // Update project status
    await ctx.db.patch(args.projectId, {
      status: "post-op",
      actualEnd: Date.now(),
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Add viewer to session
export const joinSession = mutation({
  args: {
    sessionId: v.id("streamSessions"),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email || "null"))
      .unique();
    
    if (!user) throw new Error("User not found");

    const session = await ctx.db.get(args.sessionId);

    if (!session) throw new Error("Session not found");

    // Add user to viewers if not already present
    if (!session.viewers.includes(user._id)) {
      await ctx.db.patch(session._id, {
        viewers: [...session.viewers, user._id],
      });
    }

    return { success: true };
  },
});
