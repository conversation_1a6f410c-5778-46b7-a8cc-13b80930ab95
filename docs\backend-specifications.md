# Backend Implementation Specifications
## NFM System - Convex Backend Guide

### 🎯 Overview

This document provides detailed backend implementation specifications for the NFM system using Convex. It includes database operations, real-time synchronization patterns, file handling, and API design specifically tailored for medical neuromonitoring workflows.

**Phase 2.1 Update**: The backend now implements the Table pattern with convex-helpers for type-safe operations, automatic schema synchronization, and CRUD utilities to eliminate manual validator maintenance.

---

## 📊 Convex Schema Implementation

### Phase 2.1: Type-Safe Schema with Table Pattern

The schema has been completely refactored to use the convex-helpers Table pattern for maximum type safety and automatic synchronization.

```typescript
// SEE convex/schema.ts for complete implementation

// Key improvements in Phase 2.1:
// - Table pattern using convex-helpers
// - Exported field validators for reuse
// - Type-safe field selectors
// - Automatic schema-to-function synchronization

export const Users = Table("users", {
  email: v.optional(v.string()),
  name: v.optional(v.string()),
  role: v.optional(v.union(
      v.literal("default"),
      v.literal("surgeon"),
      v.literal("anesthesiologist"), 
      v.literal("neurophysiologist"),
      v.literal("admin"),
      v.literal("technician")
    )),
  // ... other fields
});

// Also tables for  patients, projects, streamConfigs, monitoringEvents, clinicalExams, streamSessions, systemConfig

// Export individual validators for reuse
export const { role: userRoleValidator } = Users.withoutSystemFields.fields;
export const { status: projectStatusValidator } = Projects.withoutSystemFields.fields;

// Schema definition with proper indexes e.g. for users
export default defineSchema({
  users: Users.table
    .index("by_email", ["email"])
    .index("by_role", ["role"])
    .index("by_active", ["isActive"]),
  // ... other tables
});
```

### Type-Safe Function Implementation

All functions now use type-safe field selectors instead of manual validator definitions:

```typescript
// SEE convex/patients.ts, convex/projects.ts, convex/users.ts, convex/streams.ts ...

// Example: Type-safe patient creation
import { Patients } from "./schema";
import { pick, omit } from "convex-helpers";

const patientCreateFields = Patients.withoutSystemFields.fields;
const patientUpdateFields = omit(patientCreateFields, ["createdBy"]);

export const createPatient = mutation({
  args: patientCreateFields,  // Automatically synced with schema
  returns: v.id("patients"),
  handler: async (ctx, args) => { /* ... */ }
});
```

### CRUD Operations Available

Phase 2.1 introduces comprehensive CRUD utilities for all tables:

```typescript
// SEE convex/crudHelpers.ts

import { crud } from "convex-helpers/server/crud";

// Auto-generated CRUD operations for each table
export const usersCrud = crud(schema, "users");
export const patientsCrud = crud(schema, "patients");
export const projectsCrud = crud(schema, "projects");
export const streamConfigsCrud = crud(schema, "streamConfigs");
export const monitoringEventsCrud = crud(schema, "monitoringEvents");

// Usage examples:
// const userId = await ctx.runMutation(usersCrud.create, userData);
// const user = await ctx.runQuery(usersCrud.read, { id: userId });
// await ctx.runMutation(usersCrud.update, { id: userId, patch: updates });
// await ctx.runMutation(usersCrud.destroy, { id: userId });
```

**Recommendation**: Use CRUD operations where possible for simple data operations to reduce boilerplate and ensure consistency.

---

## 🔄 Real-time Data Patterns

### Live Session Management

```typescript
// SEE convex/streams.ts - All functions now type-safe

// Key functions with type-safe implementations:
// - getLiveSession: Real-time session status with team authorization
// - startLiveSession: Session initiation with proper validation
// - stopLiveSession: Session termination with status updates
// - joinSession: User participation management
```

### MediaMTX Integration

```typescript
// SEE convex/streamActions.ts - Complete functionality restored

// All MediaMTX integration functions now available with type safety:
// - listStreamSources: Database + live status synchronization  
// - configureStreamSource: Stream setup with validation
// - testStreamConnection: Connection testing
// - editStreamConfig: Configuration updates
// - toggleStreamSource: Enable/disable functionality
// - importFromMediaMTX: MediaMTX to database sync
// - resetToMediaMTXConfigs: Full reset operations
// - deleteStreamConfig: Stream removal
// - syncMediaMTXPaths: Bidirectional synchronization
```

### Event Management System
 NOT UPDATED AFTER 2.1 UPDATE IMPLEMENTING CONVEX HELPERS AND TABLE PATTERN FOR CONVEX 
```typescript
// Event management with type-safe field selectors
import { MonitoringEvents, eventSeverityValidator } from "./schema";

export const getEventsBySession = query({
  args: { 
    sessionId: v.string(),
    modalityFilter: v.optional(v.array(v.string())),
    severityFilter: v.optional(v.array(eventSeverityValidator)),
  },
  returns: v.array(v.any()), // Enhanced with modality data, define better typing
  handler: async (ctx, args) => {

     // Real-time events query for timeline with filtering
    // TODO: Uses proper indexes for performance
    let query = ctx.db
      .query("monitoringEvents")
      .withIndex("by_session", (q) => q.eq("sessionId", args.sessionId));

    // Apply filters if provided
    if (args.modalityFilter && args.modalityFilter.length > 0) {
      query = query.filter((q) => 
        args.modalityFilter!.some(modalityId => 
          q.eq(q.field("modalityId"), modalityId)
        )
      );
    }

    if (args.severityFilter && args.severityFilter.length > 0) {
      query = query.filter((q) => 
        args.severityFilter!.includes(q.field("severity"))
      );
    }

    const events = await query.order("asc").collect();

    // Get modality information for each event
    const eventsWithModalities = await Promise.all(
      events.map(async (event) => {
        const modality = await ctx.db.get(event.modalityId);
        return {
          ...event,
          modality,
        };
      })
    );

    return eventsWithModalities;
  },
});

// Create new monitoring event
export const createEvent = mutation({
  args: {
    projectId: v.id("projects"),
    sessionId: v.string(),
    timestamp: v.number(),
    modalityId: v.id("modalityConfigs"),
    eventType: v.string(),
    severity: v.union(v.literal("normal"), v.literal("warning"), v.literal("critical")),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    location: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email))
      .unique();
    
    if (!user) throw new Error("User not found");

    // Get modality for default title if not provided
    const modality = await ctx.db.get(args.modalityId);
    if (!modality) throw new Error("Modality not found");

    const title = args.title || `${modality.displayName} ${args.eventType}`;

    // Create the event
    const eventId = await ctx.db.insert("monitoringEvents", {
      projectId: args.projectId,
      sessionId: args.sessionId,
      timestamp: args.timestamp,
      modalityId: args.modalityId,
      eventType: args.eventType,
      severity: args.severity,
      title,
      description: args.description || "",
      location: args.location,
      screenshots: [],
      reviewStatus: "unreviewed",
      createdBy: user._id,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Update stream session event count
    const streamSession = await ctx.db
      .query("streamSessions")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .filter((q) => q.eq(q.field("sessionId"), args.sessionId))
      .unique();

    if (streamSession) {
      await ctx.db.patch(streamSession._id, {
        eventCount: streamSession.eventCount + 1,
        lastEventTime: Date.now(),
      });
    }

    return eventId;
  },
});

// Update event (for review process)
export const updateEvent = mutation({
  args: {
    eventId: v.id("monitoringEvents"),
    updates: v.object({
      title: v.optional(v.string()),
      description: v.optional(v.string()),
      severity: v.optional(v.union(v.literal("normal"), v.literal("warning"), v.literal("critical"))),
      location: v.optional(v.string()),
      reviewStatus: v.optional(v.union(
        v.literal("unreviewed"),
        v.literal("under-review"),
        v.literal("reviewed"),
        v.literal("flagged")
      )),
      reviewNotes: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email))
      .unique();
    
    if (!user) throw new Error("User not found");

    const event = await ctx.db.get(args.eventId);
    if (!event) throw new Error("Event not found");

    // Add reviewer if changing review status
    const updateData: any = {
      ...args.updates,
      updatedAt: Date.now(),
    };

    if (args.updates.reviewStatus && args.updates.reviewStatus !== "unreviewed") {
      updateData.reviewerId = user._id;
    }

    await ctx.db.patch(args.eventId, updateData);
    return { success: true };
  },
});
```

---

## 📁 File Upload & Screenshot Handling
NOT UPDATED AFTER 2.1 UPDATE IMPLEMENTING CONVEX HELPERS AND TABLE PATTERN FOR CONVEX 
```typescript
// convex/files.ts
// 
// TODO after 2.1 We can use CRUD operations
// SEE convex/files.ts for implementation patterns
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Generate upload URL for screenshots/videos
export const generateUploadUrl = mutation({
  args: {
    eventId: v.optional(v.id("monitoringEvents")),
    fileType: v.union(v.literal("screenshot"), v.literal("video"), v.literal("document")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    return await ctx.storage.generateUploadUrl();
  },
});

// Add screenshot to event
export const addScreenshotToEvent = mutation({
  args: {
    eventId: v.id("monitoringEvents"),
    storageId: v.id("_storage"),
    timestamp: v.number(),
    description: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");

    const event = await ctx.db.get(args.eventId);
    if (!event) throw new Error("Event not found");

    // Add screenshot to event's screenshots array
    const updatedScreenshots = [...event.screenshots, args.storageId];
    
    await ctx.db.patch(args.eventId, {
      screenshots: updatedScreenshots,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

// Get file URL for display
export const getFileUrl = query({
  args: { storageId: v.id("_storage") },
  handler: async (ctx, args) => {
    return await ctx.storage.getUrl(args.storageId);
  },
});
```

---

## 🏥 Clinical Data Management
HANDLER NOT UPDATED AFTER 2.1 UPDATE IMPLEMENTING CONVEX HELPERS AND TABLE PATTERN FOR CONVEX
```typescript
// Clinical examination management with type-safe approach
import { ClinicalExams } from "./schema";

const clinicalExamFields = ClinicalExams.withoutSystemFields.fields;

export const saveExamination = mutation({
  args: pick(clinicalExamFields, [
    "projectId", "type", "cranialNerves", "motorFunction", 
    "sensoryFunction", "reflexes", "overallStatus", "summaryNotes", "recommendations"
  ]),
  returns: v.id("clinicalExams"),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email))
      .unique();
    
    if (!user) throw new Error("User not found");

    // Check if examination already exists
    const existingExam = await ctx.db
      .query("clinicalExams")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .filter((q) => q.eq(q.field("type"), args.type))
      .first();

    const examData = {
      projectId: args.projectId,
      type: args.type,
      cranialNerves: args.cranialNerves,
      motorFunction: args.motorFunction,
      sensoryFunction: args.sensoryFunction,
      reflexes: args.reflexes,
      overallStatus: args.overallStatus,
      summaryNotes: args.summaryNotes,
      recommendations: args.recommendations,
      examiner: user._id,
      timestamp: Date.now(),
    };

    if (existingExam) {
      // Update existing examination
      await ctx.db.patch(existingExam._id, examData);
      return existingExam._id;
    } else {
      // Create new examination
      return await ctx.db.insert("clinicalExams", examData);
    }
  },
});

// Get examinations for project
export const getExaminationsByProject = query({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    const exams = await ctx.db
      .query("clinicalExams")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .collect();

    // Get examiner details for each exam
    const examsWithExaminers = await Promise.all(
      exams.map(async (exam) => {
        const examiner = await ctx.db.get(exam.examiner);
        return {
          ...exam,
          examinerName: examiner?.name || "Unknown",
        };
      })
    );

    return examsWithExaminers;
  },
});
```

---

## 🎯 Phase 2.3: Project Context System

### Enhanced Frontend-Backend Integration

Phase 2.3 introduces a comprehensive context system for better state management and reduced API calls:

```typescript
// components/contexts/ProjectContext.tsx

// Centralized project, session, and user management
export interface ProjectContextType {
  // Current project data
  currentProject: Doc<"projects"> | null;
  currentProjectId: Id<"projects"> | null;
  currentSession: Doc<"streamSessions"> | null;
  currentSessionId: Id<"streamSessions"> | null;
  currentUser: Doc<"users"> | null;
  currentUserId: Id<"users"> | null;

  // Project-related data (auto-fetched)
  modalities: TimelineModality[];
  events: TimelineEvent[];

  // Application state management
  applicationState: 'live-monitoring' | 'historical-review' | 'editing' | 'reporting';
}

// Usage in components:
function LiveMonitoringContent() {
  const { currentProject, modalities, events, currentUserId } = useProjectContext();
  const { modalities, events } = useProjectData(); // Convenience hook

  // No need for separate queries - data comes from context
  // Automatic user detection from project
  // Centralized loading states
}
```

### Database-Driven Special Modalities

The "ALL" modality is now stored in the database instead of being hardcoded:

```typescript
// convex/modalityConfigs.ts - Enhanced seed data
const defaultModalities = [
  // Special "ALL" modality for compact timeline view
  {
    name: "ALL",
    displayName: "All Modalities",
    colorCode: "#6b7280", // gray-500
    eventTypes: [
      {
        name: "Manual Event",
        severity: "normal" as const,
        defaultDuration: 15,
        description: "Manually created event"
      }
    ],
    isActive: true,
    createdAt: Date.now(),
  },
  // ... other modalities
];
```

### Schema-Based Type System

Types are now derived directly from the Convex schema using Pick/Omit:

```typescript
// types/timeline.ts - Schema-based types
export type TimelineEvent = Omit<Pick<MonitoringEvent,
  | "_id"
  | "startTime"
  | "endTime"
  | "modalityId"
  | "eventType"
  | "severity"
  | "title"
  | "description"
>, "_id"> & {
  id: Id<"monitoringEvents">; // Frontend convenience alias
  modalityName: string;
  modalityColor: string;
};

export type TimelineModality = Omit<Pick<ModalityConfig,
  | "_id"
  | "name"
  | "displayName"
  | "colorCode"
>, "_id"> & {
  id: Id<"modalityConfigs">; // Frontend convenience alias
  isVisible: boolean;
};
```

### Session Management API

Enhanced session management with proper lifecycle handling:

```typescript
// convex/streamSessions.ts - Complete session management
export const getOrCreateActiveSession = mutation({
  args: { projectId: v.id("projects") },
  handler: async (ctx, args) => {
    // Smart session detection and creation
    const activeSession = await ctx.db
      .query("streamSessions")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId))
      .filter((q) => q.or(
        q.eq(q.field("status"), "live"),
        q.eq(q.field("status"), "starting"),
        q.eq(q.field("status"), "paused")
      ))
      .first();

    if (activeSession) return activeSession._id;

    // Create new session if none exists
    return await ctx.db.insert("streamSessions", {
      projectId: args.projectId,
      status: "starting",
      streamSources: [],
      startTime: Date.now(),
      viewers: [],
      eventCount: 0,
    });
  },
});
```

### Benefits of Phase 2.3 Architecture

1. **Reduced API Calls**: Context fetches data once, components use cached data
2. **Type Safety**: Schema-driven types prevent drift between database and frontend
3. **Centralized State**: Single source of truth for project/session/user data
4. **Auto-Detection**: Smart detection of test data for development
5. **Cleaner Components**: Components focus on UI, not data fetching
6. **Better Performance**: Fewer queries, better caching, optimized re-renders

After Phase 2.3 implementation, the system provides maximum type safety, automatic schema synchronization, comprehensive CRUD operations, and efficient state management while maintaining all existing functionality.
