"use client";

import { useState, useEffect } from "react";
import { useAction } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { 
  Plus, 
  Edit2, 
  Video, 
  VideoOff, 
  Users, 
  RefreshCw, 
  AlertCircle,
  Check,
  X,
  Loader2,
  Settings2,
  Trash2,
  Download,
  RotateCcw,
  AlertTriangle
} from "lucide-react";
import { Doc } from "@/convex/_generated/dataModel";

export function StreamSourceManager() {
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);

  const [editingStream, setEditingStream] = useState<Doc<"streamConfigs"> | null>(null);
  const [deletingStream, setDeletingStream] = useState<Doc<"streamConfigs"> | null>(null);
  const [isResetDialogOpen, setIsResetDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    pathName: "",
    sourceUrl: "",
    streamType: "",
    description: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<{success: boolean; message: string} | null>(null);
  const [streams, setStreams] = useState<Doc<"streamConfigs">[]>([]);

  const configureStream = useAction(api.streamActions.configureStreamSource);
  const editStream = useAction(api.streamActions.editStreamConfig);
  const deleteStream = useAction(api.streamActions.deleteStreamConfig);
  const toggleStream = useAction(api.streamActions.toggleStreamSource);
  const listStreams = useAction(api.streamActions.listStreamSources);
  const testConnection = useAction(api.streamActions.testStreamConnection);
  const syncPaths = useAction(api.streamActions.syncMediaMTXPaths);
  const importFromMediaMTX = useAction(api.streamActions.importFromMediaMTX);
  const resetToMediaMTX = useAction(api.streamActions.resetToMediaMTXConfigs);

  // Load streams on mount and periodically
  useEffect(() => {
    loadStreams();
    const interval = setInterval(loadStreams, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const loadStreams = async () => {
    try {
      const streamList = await listStreams();
      setStreams(streamList);
    } catch (error) {
      console.error("Failed to load streams:", error);
    }
  };

  const handleSync = async () => {
    setIsLoading(true);
    try {
      const result = await syncPaths();
      if (result.success) {
        await loadStreams();
        // Show success message or handle errors
        if (result.errors && result.errors.length > 0) {
          console.warn('Sync completed with errors:', result.errors);
        }
      }
    } catch (error) {
      console.error("Failed to sync paths:", error);
    }
    setIsLoading(false);
  };

  const handleImportFromMediaMTX = async () => {
    setIsLoading(true);
    try {
      const result = await importFromMediaMTX();
      if (result.success) {
        await loadStreams();
        alert(result.message);
      } else {
        alert(`Import failed: ${result.message}`);
      }
    } catch (error) {
      console.error("Failed to import from MediaMTX:", error);
      alert("Failed to import configurations from MediaMTX");
    }
    setIsLoading(false);
  };

  const handleResetToMediaMTX = async () => {
    setIsLoading(true);
    try {
      const result = await resetToMediaMTX({ confirmReset: true });
      if (result.success) {
        await loadStreams();
        alert(result.message);
      } else {
        alert(`Reset failed: ${result.message}`);
      }
    } catch (error) {
      console.error("Failed to reset to MediaMTX:", error);
      alert("Failed to reset to MediaMTX configurations");
    }
    setIsResetDialogOpen(false);
    setIsLoading(false);
  };

  const handleTestConnection = async () => {
    setIsTesting(true);
    setTestResult(null);
    try {
      const result = await testConnection({ sourceUrl: formData.sourceUrl });
      setTestResult(result);
    } catch (error) {
      setTestResult({ success: false, message: error instanceof Error ? error.message : String(error) });
    }
    setIsTesting(false);
  };

  const handleAdd = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await configureStream({
        pathName: formData.pathName,
        sourceUrl: formData.sourceUrl,
        streamType: formData.streamType as Doc<"streamConfigs">["streamType"],//"screen" | "camera" | "inomed" | "microscope" | undefined,
        description: formData.description,
      });
      setIsAddOpen(false);
      resetForm();
      await loadStreams();
    } catch (error) {
      alert(error instanceof Error ? error.message : String(error));
    }
    setIsLoading(false);
  };

  const handleEdit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingStream) return;
    
    setIsLoading(true);
    try {
      await editStream({
        pathName: editingStream.pathName,
        sourceUrl: formData.sourceUrl,
        streamType: formData.streamType as Doc<"streamConfigs">["streamType"],
        description: formData.description,
      });
      setIsEditOpen(false);
      setEditingStream(null);
      resetForm();
      await loadStreams();
    } catch (error) {
      alert(error instanceof Error ? error.message : String(error));
    }
    setIsLoading(false);
  };

  const handleDelete = async () => {
    if (!deletingStream) return;
    
    setIsLoading(true);
    try {
      await deleteStream({
        pathName: deletingStream.pathName,
      });
      setIsDeleteOpen(false);
      setDeletingStream(null);
      await loadStreams();
    } catch (error) {
      alert(error instanceof Error ? error.message : String(error));
    }
    setIsLoading(false);
  };

  const openDeleteDialog = (stream: Doc<"streamConfigs">) => {
    setDeletingStream(stream);
    setIsDeleteOpen(true);
  };

  const handleToggle = async (stream: Doc<"streamConfigs">) => {
    try {
      await toggleStream({
        pathName: stream.pathName,
        enable: !stream.isEnabled,
      });
      await loadStreams();
    } catch (error) {
      console.error("Failed to toggle stream:", error);
      alert(error instanceof Error ? error.message : String(error));
    }
  };

  const openEditDialog = (stream: Doc<"streamConfigs">) => {
    setEditingStream(stream);
    setFormData({
      pathName: stream.pathName,
      sourceUrl: stream.sourceUrl,
      streamType: stream.streamType || "",
      description: stream.description || "",
    });
    setTestResult(null);
    setIsEditOpen(true);
  };

  const resetForm = () => {
    setFormData({
      pathName: "",
      sourceUrl: "",
      streamType: "",
      description: "",
    });
    setTestResult(null);
  };

  const streamTypeLabels = {
    inomed: "Inomed System",
    microscope: "Microscope",
    camera: "Camera",
    screen: "Screen Capture",
    external: "External Source",
    local: "Local Stream",
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Stream Sources</h3>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleSync}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="w-4 h-4 mr-2" />
            )}
            Sync
          </Button>
          <Button
            variant="outline"
            onClick={handleImportFromMediaMTX}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Download className="w-4 h-4 mr-2" />
            )}
            Import from MediaMTX
          </Button>
          <AlertDialog open={isResetDialogOpen} onOpenChange={setIsResetDialogOpen}>
            <AlertDialogTrigger asChild>
              <Button variant="outline" className="text-orange-600 hover:text-orange-700">
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset to MediaMTX
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle className="flex items-center gap-2">
                  <AlertTriangle className="w-5 h-5 text-orange-600" />
                  Reset to MediaMTX Configurations
                </AlertDialogTitle>
                <AlertDialogDescription className="space-y-2">
                  <p>This will <strong>permanently delete all current stream configurations</strong> in the database and replace them with configurations from MediaMTX.</p>
                  <p>This action cannot be undone. Use this only when resetting the MediaMTX server to defaults.</p>
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction 
                  onClick={handleResetToMediaMTX}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Reset All Configurations
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
          <Dialog open={isAddOpen} onOpenChange={setIsAddOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => resetForm()}>
                <Plus className="w-4 h-4 mr-2" />
                Add Stream Source
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <form onSubmit={handleAdd}>
                <DialogHeader>
                  <DialogTitle>Add Stream Source</DialogTitle>
                  <DialogDescription>
                    Configure a new RTSP stream source for video monitoring.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="pathName">Path Name</Label>
                    <Input
                      id="pathName"
                      placeholder="or1_main"
                      value={formData.pathName}
                      onChange={(e) => setFormData({...formData, pathName: e.target.value})}
                      pattern="[a-zA-Z0-9._~\-\/]+"
                      required
                    />
                    <p className="text-xs text-muted-foreground">
                      Unique identifier. Use only letters, numbers, underscore, dot, tilde, minus, or slash
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="sourceUrl">RTSP Source URL</Label>
                    <div className="flex gap-2">
                      <Input
                        id="sourceUrl"
                        placeholder="rtsp://*************:8554/main/av"
                        value={formData.sourceUrl}
                        onChange={(e) => setFormData({...formData, sourceUrl: e.target.value})}
                        required
                        className="flex-1"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleTestConnection}
                        disabled={!formData.sourceUrl || isTesting}
                      >
                        {isTesting ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          "Test"
                        )}
                      </Button>
                    </div>
                    {testResult && (
                      <div className={`flex items-center gap-2 text-sm ${testResult.success ? 'text-green-600' : 'text-red-600'}`}>
                        {testResult.success ? (
                          <Check className="w-4 h-4" />
                        ) : (
                          <X className="w-4 h-4" />
                        )}
                        {testResult.message}
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="streamType">Stream Type</Label>
                    <Select value={formData.streamType} onValueChange={(value) => setFormData({...formData, streamType: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select stream type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="inomed">Inomed System</SelectItem>
                        <SelectItem value="microscope">Microscope</SelectItem>
                        <SelectItem value="camera">Camera</SelectItem>
                        <SelectItem value="screen">Screen Capture</SelectItem>
                        <SelectItem value="external">External Source</SelectItem>
                        <SelectItem value="local">Local Stream</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description (Optional)</Label>
                    <Input
                      id="description"
                      placeholder="Operating Room 1 - Main Feed"
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setIsAddOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? (
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    ) : null}
                    Add Stream
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid gap-4">
        {streams.map((stream) => (
          <Card key={stream.pathName} className="p-4">
            <div className="flex items-start justify-between">
              <div className="space-y-1 flex-1">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium">{stream.pathName}</h4>
                  {stream.streamType && (
                    <Badge variant="secondary">
                      {streamTypeLabels[stream.streamType] || stream.streamType}
                    </Badge>
                  )}
                  {stream.isLive ? (
                    <Badge variant="default" className="bg-green-600">
                      <Video className="w-3 h-3 mr-1" />
                      Live
                    </Badge>
                  ) : (
                    <Badge variant="outline">
                      <VideoOff className="w-3 h-3 mr-1" />
                      Offline
                    </Badge>
                  )}
                  {stream.originatedFromMediaMTX && (
                    <Badge variant="outline" className="bg-blue-50">
                      <Settings2 className="w-3 h-3 mr-1" />
                      From MediaMTX
                    </Badge>
                  )}
                  {stream.lastSyncStatus === 'needs_sync' && (
                    <Badge variant="outline" className="bg-orange-50">
                      <AlertCircle className="w-3 h-3 mr-1" />
                      Needs Sync
                    </Badge>
                  )}
                  {stream.lastSyncStatus === 'conflict' && (
                    <Badge variant="outline" className="bg-red-50">
                      <AlertTriangle className="w-3 h-3 mr-1" />
                      Conflict
                    </Badge>
                  )}
                  {!stream.isInMediaMTX && stream.isEnabled && (
                    <Badge variant="outline" className="bg-yellow-50">
                      <AlertCircle className="w-3 h-3 mr-1" />
                      Not in MediaMTX
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">{stream.sourceUrl}</p>
                {stream.description && (
                  <p className="text-sm">{stream.description}</p>
                )}
                {stream?.viewers || 0 > 0 && (
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Users className="w-3 h-3" />
                    {stream.viewers} viewer{stream.viewers !== 1 ? 's' : ''}
                  </div>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Switch
                  checked={stream.isEnabled}
                  onCheckedChange={() => handleToggle(stream)}
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => openEditDialog(stream)}
                >
                  <Edit2 className="w-4 h-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => openDeleteDialog(stream)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {streams.length === 0 && (
        <Card className="p-8 text-center text-muted-foreground">
          No stream sources configured. Add one to get started.
        </Card>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditOpen} onOpenChange={setIsEditOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <form onSubmit={handleEdit}>
            <DialogHeader>
              <DialogTitle>Edit Stream Source</DialogTitle>
              <DialogDescription>
                Update the configuration for {editingStream?.pathName}.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label>Path Name</Label>
                <Input
                  value={editingStream?.pathName || ""}
                  disabled
                  className="bg-muted"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="edit-sourceUrl">RTSP Source URL</Label>
                <div className="flex gap-2">
                  <Input
                    id="edit-sourceUrl"
                    placeholder="rtsp://*************:8554/main/av"
                    value={formData.sourceUrl}
                    onChange={(e) => setFormData({...formData, sourceUrl: e.target.value})}
                    required
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleTestConnection}
                    disabled={!formData.sourceUrl || isTesting}
                  >
                    {isTesting ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      "Test"
                    )}
                  </Button>
                </div>
                {testResult && (
                  <div className={`flex items-center gap-2 text-sm ${testResult.success ? 'text-green-600' : 'text-red-600'}`}>
                    {testResult.success ? (
                      <Check className="w-4 h-4" />
                    ) : (
                      <X className="w-4 h-4" />
                    )}
                    {testResult.message}
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-streamType">Stream Type</Label>
                <Select value={formData.streamType} onValueChange={(value) => setFormData({...formData, streamType: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select stream type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="inomed">Inomed System</SelectItem>
                    <SelectItem value="microscope">Microscope</SelectItem>
                    <SelectItem value="camera">Camera</SelectItem>
                    <SelectItem value="screen">Screen Capture</SelectItem>
                    <SelectItem value="external">External Source</SelectItem>
                    <SelectItem value="local">Local Stream</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-description">Description (Optional)</Label>
                <Input
                  id="edit-description"
                  placeholder="Operating Room 1 - Main Feed"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : null}
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteOpen} onOpenChange={setIsDeleteOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>Delete Stream Source</DialogTitle>
            <DialogDescription>
              {`Are you sure you want to delete the stream source "${deletingStream?.pathName}"? \n
              This action cannot be undone and will remove the configuration from both the database and MediaMTX.`
              }
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
              <div className="flex">
                <AlertCircle className="w-5 h-5 text-yellow-600 mr-2 flex-shrink-0 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium">This will:</p>
                  <ul className="mt-1 list-disc list-inside space-y-1">
                    <li>Remove the stream from MediaMTX configuration</li>
                    <li>Delete the stream configuration from the database</li>
                    <li>Stop any active streaming for this path</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => setIsDeleteOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              type="button" 
              variant="destructive" 
              onClick={handleDelete}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Trash2 className="w-4 h-4 mr-2" />
              )}
              Delete Stream
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}