import React, { ReactNode } from 'react';
import { OnScrollParams } from 'react-virtualized';
import { ITimelineEngine } from '..';
import { Emitter } from '../engine/emitter';
import { EventTypes } from '../engine/events';
import { TimelineEvent, TimelineRow } from './action';
import { TimelineEffect } from './effect';
export * from './action';
export * from './effect';

export interface EditData {
  /**
   * @description Timeline Editing Data
   */
  editorData: TimelineRow[];
  /**
   * @description Timeline Action Effect Map
   */
  effects: Record<string, TimelineEffect>;
  /**
   * @description Single scale mark scope（>0）
   * @default 1
   */
  scale?: number;
  /**
   * @description Minimum scale count（>=1）
   * @default 20
   */
  minScaleCount?: number;
  /**
   * @description Maximum scale count（>=minScaleCount）
   * @default Infinity
   */
  maxScaleCount?: number;
  /**
   * @description Single scale subdivision unit count（>0 integer）
   * @default 10
   */
  scaleSplitCount?: number;
  /**
   * @description Single scale display width（>0, unit：px）
   * @default 160
   */
  scaleWidth?: number;
  /**
   * @description Scale starts from the distance to the left（>=0, unit：px）
   * @default 20
   */
  startLeft?: number;
  /**
   * @description Each editing line default height（>0, unit：px）
   * @default 32
   */
  rowHeight?: number;
  /**
   * @description Whether to enable grid movement adsorption
   * @default false
   */
  gridSnap?: boolean;
  /**
   * @description Whether to enable drag line adsorption
   * @default false
   */
  dragLine?: boolean;
  /**
   * @description Whether to hide cursor
   * @default false
   */
  hideCursor?: boolean;
  /**
   * @description Disable drag all action area
   * @default false
   */
  disableDrag?: boolean;
  /**
   * @description timeline engine, not passed in use the built-in engine
   */
  engine?: ITimelineEngine;
  /**
   * @description Customize action area rendering
   */
  getActionRender?: (action: TimelineEvent, row: TimelineRow) => ReactNode;
  /**
   * @description Customize scale rendering
   */
  getScaleRender?: (scale: number) => ReactNode;
  /**
   * @description Start moving callback
   */
  onActionMoveStart?: (params: { action: TimelineEvent; row: TimelineRow }) => void;
  /**
   * @description Move callback（return false can prevent moving）
   */
  onActionMoving?: (params: { action: TimelineEvent; row: TimelineRow; start: number; end: number }) => void | boolean;
  /**
   * @description Move end callback（return false can prevent onChange trigger）
   */
  onActionMoveEnd?: (params: { action: TimelineEvent; row: TimelineRow; start: number; end: number }) => void;
  /**
   * @description Start size callback（return false can prevent changing）
   */
  onActionResizeStart?: (params: { action: TimelineEvent; row: TimelineRow; dir: 'right' | 'left' }) => void;
  /**
   * @description Start size callback（return false can prevent changing）
   */
  onActionResizing?: (params: { action: TimelineEvent; row: TimelineRow; start: number; end: number; dir: 'right' | 'left' }) => void | boolean;
  /**
   * @description Change size end callback（return false can prevent onChange trigger）
   */
  onActionResizeEnd?: (params: { action: TimelineEvent; row: TimelineRow; start: number; end: number; dir: 'right' | 'left' }) => void;
  /**
   * @description Click row callback
   */
  onClickRow?: (
    e: React.MouseEvent<HTMLElement, MouseEvent>,
    param: {
      row: TimelineRow;
      time: number;
    },
  ) => void;
  /**
   * @description Click action callback
   */
  onClickAction?: (
    e: React.MouseEvent<HTMLElement, MouseEvent>,
    param: {
      action: TimelineEvent;
      row: TimelineRow;
      time: number;
    },
  ) => void;
  /**
   * @description Click action callback（Trigger drag when not executed）
   */
  onClickActionOnly?: (
    e: React.MouseEvent<HTMLElement, MouseEvent>,
    param: {
      action: TimelineEvent;
      row: TimelineRow;
      time: number;
    },
  ) => void;
  /**
   * @description Double-click row callback
   */
  onDoubleClickRow?: (
    e: React.MouseEvent<HTMLElement, MouseEvent>,
    param: {
      row: TimelineRow;
      time: number;
    },
  ) => void;
  /**
   * @description Double-click action callback
   */
  onDoubleClickAction?: (
    e: React.MouseEvent<HTMLElement, MouseEvent>,
    param: {
      action: TimelineEvent;
      row: TimelineRow;
      time: number;
    },
  ) => void;
  /**
   * @description Right-click row callback
   */
  onContextMenuRow?: (
    e: React.MouseEvent<HTMLElement, MouseEvent>,
    param: {
      row: TimelineRow;
      time: number;
    },
  ) => void;
  /**
   * @description Right-click action callback
   */
  onContextMenuAction?: (
    e: React.MouseEvent<HTMLElement, MouseEvent>,
    param: {
      action: TimelineEvent;
      row: TimelineRow;
      time: number;
    },
  ) => void;
  /**
   * @description Get the action id list to prompt the assistant line, calculate it during the move/resize start, and get all by default except the current moving action
   */
  getAssistDragLineActionIds?: (params: { action: TimelineEvent; editorData: TimelineRow[]; row: TimelineRow }) => string[];
  /**
   * @description cursor start drag event
   */
  onCursorDragStart?: (time: number) => void;
  /**
   * @description cursor end drag event
   */
  onCursorDragEnd?: (time: number) => void;
  /**
   * @description cursor drag event
   */
  onCursorDrag?: (time: number) => void;
  /**
   * @description Click time area event, return false to prevent setting time
   */
  onClickTimeArea?: (time: number, e: React.MouseEvent<HTMLDivElement, MouseEvent>) => boolean | undefined;
}

export interface TimelineState {
  /** dom node */
  target: HTMLElement;
  /** Running listener */
  listener: Emitter<EventTypes>;
  /** Whether to play */
  isPlaying: boolean;
  /** Whether to pause */
  isPaused: boolean;
  /** Set current play time */
  setTime: (time: number) => void;
  /** Get current play time */
  getTime: () => number;
  /** Set play rate */
  setPlayRate: (rate: number) => void;
  /** Get play rate */
  getPlayRate: () => number;
  /** Re-render current time */
  reRender: () => void;
  /** Play */
  play: (param: {
    /** Default to run from the beginning to the end, priority over autoEnd */
    toTime?: number;
    /** Whether to end automatically after playing */
    autoEnd?: boolean;
    /** List of actionIds to run, not passed in to run all by default */
    runActionIds?: string[];
  }) => boolean;
  /** Pause */
  pause: () => void;
  /** Update current scroll */
  scrollLeftBy: (by: number) => void;
  /** Set scroll left */
  setScrollLeft: (val: number) => void;
  /** Set scroll top */
  setScrollTop: (val: number) => void;
}

/**
 * Animation Editor Parameters
 * @export
 * @interface TimelineProp
 */
export interface TimelineEditor extends EditData {
  /**
   * @description Editing area distance to the top scroll distance (Please use ref.setScrollTop instead)
   * @deprecated
   */
  scrollTop?: number;
  /**
   * @description Editing area scroll callback (used to control the synchronization of editing lines)
   */
  onScroll?: (params: OnScrollParams) => void;
  /**
   * @description Whether to enable auto scroll during drag
   * @default false
   */
  autoScroll?: boolean;
  /**
   * @description Customize timeline style
   */
  style?: React.CSSProperties;
  /**
   * @description Whether to automatically re-render（When data changes or cursor time changes, update tick）
   * @default true
   */
  autoReRender?: boolean;
  /**
   * @description Data change callback, will be triggered after the operation action ends (return false will prevent automatic engine synchronization, used to reduce performance overhead)
   */
  onChange?: (editorData: TimelineRow[]) => void | boolean;
  /**
   * @description Timeline duration in seconds (used to calculate proper scale bounds)
   */
  duration?: number;
}
