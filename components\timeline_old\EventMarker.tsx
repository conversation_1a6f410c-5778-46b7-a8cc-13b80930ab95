import React from 'react'
import { cn } from '@/lib/utils'
import { TimelineEvent } from '@/types/timeline'
import { 
  ContextMenu, 
  ContextMenuContent, 
  ContextMenuItem, 
  ContextMenuTrigger 
} from '@/components/ui/context-menu'
import { 
  HoverCard, 
  HoverCardContent, 
  HoverCardTrigger 
} from '@/components/ui/hover-card'

interface EventMarkerProps {
  event: TimelineEvent
  position: number
  width?: number
  isVisible: boolean
  showAsDot: boolean
  onEventClick?: (event: TimelineEvent) => void
  onEventEdit?: (event: TimelineEvent) => void
  onEventDelete?: (event: TimelineEvent) => void
}

export function EventMarker({
  event,
  position,
  width = 0,
  isVisible,
  showAsDot,
  onEventClick,
  onEventEdit,
  onEventDelete
}: EventMarkerProps) {
  if (!isVisible) return null

  const formatEventTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  const formatEventTimeRange = (startTime: number, endTime?: number): string => {
    if (!endTime || endTime <= startTime) {
      return formatEventTime(startTime)
    }
    return `${formatEventTime(startTime)} - ${formatEventTime(endTime)}`
  }

  const getSeverityStyles = (severity: TimelineEvent['severity']): string => {
    switch (severity) {
      case 'critical':
        return 'ring-2 ring-red-500 ring-opacity-50 animate-pulse'
      case 'warning':
        return 'ring-1 ring-orange-400 ring-opacity-40'
      case 'normal':
      default:
        return ''
    }
  }

  const eventContent = showAsDot ? (
    <div
      className={cn(
        "w-3 h-3 rounded-full cursor-pointer transition-transform duration-150 hover:scale-125 border-2 z-10",
        getSeverityStyles(event.severity)
      )}
      style={{ 
        backgroundColor: event.modalityColor,
        borderColor: event.modalityColor,
        left: position - 6, // Center the dot
      }}
      onClick={(e) => {
        e.stopPropagation()
        onEventClick?.(event)
      }}
    />
  ) : (
    <div
      className={cn(
        "h-4 rounded cursor-pointer transition-transform duration-150 hover:scale-110 border-2 opacity-90 z-10",
        getSeverityStyles(event.severity)
      )}
      style={{
        backgroundColor: event.modalityColor,
        borderColor: event.modalityColor,
        left: position,
        width: Math.max(4, width)
      }}
      onClick={(e) => {
        e.stopPropagation()
        onEventClick?.(event)
      }}
    />
  )

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        <HoverCard>
          <HoverCardTrigger asChild>
            <div className="absolute top-1/2 -translate-y-1/2">
              {eventContent}
            </div>
          </HoverCardTrigger>
          <HoverCardContent className="w-80" side="top">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: event.modalityColor }}
                />
                <span className="font-semibold">{event.modalityName}</span>
                <span className="text-sm text-muted-foreground">
                  {event.eventType}
                </span>
              </div>
              <div className="text-sm font-medium">{event.title}</div>
              <div className="text-sm text-muted-foreground">
                {event.description}
              </div>
              <div className="text-xs text-muted-foreground">
                <span className="font-medium">Time:</span> {formatEventTimeRange(event.startTime, event.endTime)}
              </div>
              <div className="flex items-center gap-1">
                <span className="text-xs font-medium">Severity:</span>
                <span className={cn(
                  "text-xs px-2 py-1 rounded-full",
                  event.severity === 'critical' && "bg-red-100 text-red-800",
                  event.severity === 'warning' && "bg-orange-100 text-orange-800",
                  event.severity === 'normal' && "bg-green-100 text-green-800"
                )}>
                  {event.severity}
                </span>
              </div>
            </div>
          </HoverCardContent>
        </HoverCard>
      </ContextMenuTrigger>
      <ContextMenuContent>
        <ContextMenuItem 
          onClick={() => onEventClick?.(event)}
          className="cursor-pointer"
        >
          View Details
        </ContextMenuItem>
        <ContextMenuItem 
          onClick={() => onEventEdit?.(event)}
          className="cursor-pointer"
        >
          Edit Event
        </ContextMenuItem>
        <ContextMenuItem 
          onClick={() => onEventDelete?.(event)}
          className="cursor-pointer text-red-600"
        >
          Delete Event
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  )
}
