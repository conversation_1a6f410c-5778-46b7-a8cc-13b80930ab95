"use client"

import { useState, useEffect, useRef, use<PERSON><PERSON>back, useMemo } from "react"
import { NFMTimelineEditor, NFMTimelineControls, TimelineEvent, TimelineState } from "@/components/timeline"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu"
import { Activity, Settings, Download, Upload, Keyboard, Save } from "lucide-react"
import { cn } from "@/lib/utils"
import { Doc, Id } from "@/convex/_generated/dataModel"
import { useProjectContext } from "../contexts/ProjectContext"
import { useVideoTimeline } from "../contexts/VideoTimelineContext"
import { useVideoTimelineSync } from "@/hooks/useVideoTimelineSync"
import { useMutation, useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { toast } from "sonner"
import { useIsMobile } from "@/hooks/use-mobile"
import { TimelineKeyboardShortcutsHelp } from "./NFMTimelineKeyboardShortcuts"
import { InteractionMode } from "@/types/timelineEditor"
import { formatDuration } from "@/utils/mediaHelpers"
import { parserTimeToPixel } from "./utils/deal_data"
import { DEFAULT_SCALE_WIDTH, DEFAULT_START_LEFT } from "./interface/const"


interface NFMTimelineCompleteProps {
  // Configuration
  showControls?: boolean
  showHeader?: boolean

  // Layout
  className?: string
  cardClassName?: string
  timelineHeight?: string
  maxHeight?: string

  // Event handlers
  onEventAdd?: (event: Partial<TimelineEvent>) => void
  onEventUpdate?: (eventId: string, updates: Partial<TimelineEvent>) => void
  onEventDelete?: (eventId: string) => void
  onModalityVisibilityChange?: (modalityIds: string[]) => void

  // Mobile optimization
  isMobile?: boolean

  // Legacy props (deprecated - now handled by VideoTimelineContext)
  //duration?: number
  //currentTime?: number
  //isPlaying?: boolean
  //onTimeChange?: (time: number) => void
 // onPlayStateChange?: (isPlaying: boolean) => void
}
export function NFMTimelineComplete({
  showControls = true,
  showHeader = true,
  className,
  cardClassName,
  // Legacy props - now handled by VideoTimelineContext
  //duration,
  //currentTime,
  //isPlaying,
  //onTimeChange,
  //onPlayStateChange,
}: NFMTimelineCompleteProps) {
  const isMobile = useIsMobile();
  console.debug("[NFMTimelineComplete] Rendering timeline complete component");
  // Use unified video-timeline state management
  const videoTimeline = useVideoTimeline()
  const videoTimelineSync = useVideoTimelineSync({
    timeSyncThreshold: 0.1,
    seekDebounceMs: 100,
    enableBidirectionalSync: true,
    pauseOnSeek: false,
    resumeAfterSeek: false,
  })

  // Timeline state
  const [timelineScale, setTimelineScale] = useState(100)
  const timelineStateRef = useRef<TimelineState>(null)

  
  // Initialize timeline engine listeners
  useEffect(() => {
    if (!timelineStateRef.current) return;
    
    const engine = timelineStateRef.current;
    console.debug('[NFMTimelineComplete] Initializing timeline engine listeners');
    
    // Set up event listeners for timeline engine events
    const handlePlay = () => {
      console.debug('[NFMTimelineComplete] Engine play event');
      if (!videoTimeline.isPlaying) {
        videoTimeline.play();
      }
    };

    const handlePause = () => {
      console.debug('[NFMTimelineComplete] Engine paused event');
      if (videoTimeline.isPlaying) {
        videoTimeline.pause();
      }
    };

    const handleTimeChange = ({ time }: { time: number }) => {
      console.debug(`[NFMTimelineComplete] TimelineEngine time changed to: ${time}`);
      videoTimelineSync.handleTimelineTimeUpdate(time);
    };

    const handleTimeChangeByTick = ({ time }: { time: number }) => {
      console.debug(`[NFMTimelineComplete] Timeline Engine time changed to: ${time}`);
      videoTimelineSync.handleTimelineTimeUpdate(time);
      const autoScrollWhenPlay = true;
      if (autoScrollWhenPlay) {
        const autoScrollFrom = 500;
        const left = time * (DEFAULT_SCALE_WIDTH / timelineScale) + DEFAULT_START_LEFT - autoScrollFrom;
        engine.setScrollLeft(left);
      }
    };

    const handlePlaybackRateChange = ({ rate }: { rate: number }) => {
      console.debug(`[NFMTimelineComplete] Timeline Engine playback rate changed to: ${rate}`);
      videoTimeline.setPlaybackRate(rate);
    };
    
    engine.listener.on('play', handlePlay);
    engine.listener.on('paused', handlePause);
    engine.listener.on('afterSetTime', handleTimeChange);
    engine.listener.on('setTimeByTick', handleTimeChangeByTick);
    engine.listener.on('afterSetPlayRate', handlePlaybackRateChange);
    // Clean up listeners when component unmounts
    return () => {
      console.debug('[NFMTimelineComplete] Cleaning up timeline engine');
      if (!engine) return;
      engine.pause();
      engine.listener.offAll();
    };
  }, []);


  // TODO Session management state
  const [isRecordingSession, setIsRecordingSession] = useState(false)
  const [sessionDuration, setSessionDuration] = useState("00:00:00")
  const [sessionTime, setSessionTime] = useState(0)

  // UI state
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false)
  const [interactionMode, setInteractionModeState] = useState<InteractionMode>(InteractionMode.VIEW);


  // Database states
  const { currentProject, currentProjectId, currentSessionId, currentUserId } = useProjectContext();
  const projectId = currentProjectId!;
  // Fetch data directly with Convex queries
  const modalities = useQuery(api.timeline.getProjectModalities, projectId ? { projectId } : "skip");
  const events = useQuery(api.timeline.getProjectEvents, projectId ? {
    projectId,
    visibleModalitiesOnly: true
  } : "skip");

  // CONVEX Database Mutations
  const createEvent = useMutation(api.timeline.createMonitoringEvent);
  const deleteEvent = useMutation(api.timeline.deleteMonitoringEvent);
  const updateVisibleModalities = useMutation(api.timeline.updateProjectVisibleModalities);
  const getOrCreateSession = useMutation(api.streamSessions.getOrCreateActiveSession);




  // TODO Session timer effect
  useEffect(() => {
    if (!isRecordingSession) return;

    const interval = setInterval(() => {
      const newTime = videoTimeline.currentTime;
      setSessionDuration(formatDuration(newTime));
      setSessionTime(newTime);
    }, 1000);

    return () => clearInterval(interval);
  }, [videoTimeline, isRecordingSession]);

  /* Enhanced playback interval management with playback rate
  useEffect(() => {
    if (internalIsPlaying) {
      playbackIntervalRef.current = setInterval(() => {
        setInternalCurrentTime(prev => {
          const newTime = Math.min(prev + (0.1 * playbackRate), duration) // Update every 100ms with rate
          onTimeChange?.(newTime)
          if (newTime >= duration) {
            setInternalIsPlaying(false)
            onPlayStateChange?.(false)
          }
          return newTime
        })
      }, 100) // 100ms intervals for smooth playback
    } else {
      if (playbackIntervalRef.current) {
        clearInterval(playbackIntervalRef.current)
        playbackIntervalRef.current = null
      }
    }

    return () => {
      if (playbackIntervalRef.current) {
        clearInterval(playbackIntervalRef.current)
      }
    }
  }, [internalIsPlaying, duration, playbackRate, onTimeChange, onPlayStateChange])*/

  ////////////////////////// Session management handlers \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
  const handleStartRecording = async () => {
    if (!projectId) {
      toast.error("No project selected");
      return;
    }

    try {
      // Get or create an active session
      await getOrCreateSession({ projectId });
      setIsRecordingSession(true);
      setSessionTime(0);
      toast.success("Recording session started");
    } catch (error) {
      console.error("Failed to start session:", error);
      toast.error("Failed to start session");
    }
  };

  const handleStopRecording = () => {
    setIsRecordingSession(false);
    toast.info("Recording session stopped");
  };

  ////////////////////////// Event controls handlers \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
  const handlePrevEvent = useCallback(() => {
    // IMPORTANT: This is a placeholder generated by AI, should be handled by the timeline editor
    if (!events || events.length === 0) return;
    
    // Find the previous event based on current time
    const prevEvent = [...events]
      .sort((a, b) => a.startTime - b.startTime)
      .filter(event => event.startTime < videoTimeline.currentTime - 0.5) // Small buffer to avoid the current event
      .pop();
    
    if (prevEvent) {
      console.debug(`[NFMTimelineComplete] Navigating to previous event at ${prevEvent.startTime}`);
      timelineStateRef.current?.setTime(prevEvent.startTime)
    }
  }, [events, timelineStateRef, videoTimeline]);

  const handleNextEvent = useCallback(() => {
        // IMPORTANT: This is a placeholder generated by AI, should be handled by the timeline editor

    if (!events || events.length === 0) return;
    
    // Find the next event based on current time
    const nextEvent = [...events]
      .sort((a, b) => a.startTime - b.startTime)
      .find(event => event.startTime >  videoTimeline.currentTime + 0.5); // Small buffer to avoid the current event
    
    if (nextEvent) {
      console.debug(`[NFMTimelineComplete] Navigating to next event at ${nextEvent.startTime}`);
      timelineStateRef.current?.setTime(nextEvent.startTime);
    }
  }, [events, timelineStateRef, videoTimeline]);

  const handleEventClick = (event: TimelineEvent | Doc<"monitoringEvents">) => {
    const startTime = 'start' in event ? event.start : event.startTime;
    const endTime = 'end' in event ? event.end : event.endTime;
    const timeRange = endTime ?
      `${Math.floor(startTime / 60)}:${(startTime % 60).toString().padStart(2, '0')} - ${Math.floor(endTime / 60)}:${(endTime % 60).toString().padStart(2, '0')}` :
      `${Math.floor(startTime / 60)}:${(startTime % 60).toString().padStart(2, '0')}`;

    toast.info(`Event: ${event.title} - ${timeRange}`);
  };

  const handleCreateEvent = async (event: TimelineEvent, modalityId?: string) => {
    if (!modalityId || !projectId || !currentProject) {
      toast.error("Cannot create event: missing project data");
      return;
    }

    try {
      // Get or create an active session
      const sessionId = currentSessionId || await getOrCreateSession({ projectId });

      if (!sessionId) {
        toast.error("Failed to get session");
        return;
      }

      // Use current user ID from context
      if (!currentUserId) {
        toast.error("No user logged in");
        return;
      }
      const startTime = event.startTime;
      const endTime = event.endTime || startTime + 15; // Default 15 second duration

      await createEvent({
        projectId,
        sessionId,
        startTime,
        endTime, // Default 15 second duration
        modalityId: modalityId as Id<"modalityConfigs">,
        eventType: "Manual Event",
        severity: "normal",
        title: `Event at ${Math.floor(startTime / 60)}:${(startTime % 60).toString().padStart(2, '0')}`,
        description: "Manually created event",
        createdBy: currentUserId
      });

      toast.success("Event created successfully");
    } catch (error) {
      console.error("Failed to create event:", error);
      toast.error("Failed to create event");
    }
  };

  const handleEventDelete = async (eventId: string) => {
    try {
      await deleteEvent({ eventId: eventId as Id<"monitoringEvents"> });
      toast.success("Event deleted successfully");
    } catch (error) {
      console.error("Failed to delete event:", error);
      toast.error("Failed to delete event");
    }
  }


  const handleModalityVisibilityChange = useCallback((modalityIds: string[]) => {
    // Update backend visibility settings
    if (projectId) {
      updateVisibleModalities({
        projectId,
        visibleModalityIds: modalityIds as Id<"modalityConfigs">[]
      }).catch(error => {
        console.error("Failed to update modality visibility:", error);
        toast.error("Failed to update modality visibility");
      });
    }
  }, [projectId, updateVisibleModalities]);



  ////////////////////////// Timeline controls handlers \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\
  /* const handleZoomOut = useCallback((value: number = 1) => {
     setTimelineScale(prev => Math.min(prev * 1.2, 500)) // Zoom OUT = higher scale = less magnification because px/sec is inversely proportional to scale
   }, [])*/

  const handleZoom = useCallback((value: number = 0.83) => {
    setTimelineScale(prev => Math.min(Math.max(prev * value, 10), 500)) // Zoom OUT = lower scale = higher magnification
  }, [])

  const handleFitToView = useCallback(() => {
    // Enhanced fit-to-view: Calculate scale to fit all events
    if (events && events.length > 0) {
      const maxTime = Math.max(...events.map(event => event.endTime || event.startTime + 1));
      if (maxTime > 0) {
        // Assuming timeline width of 800px and scale relationship
        const newScale = Math.max(10, Math.min(500, (800 / maxTime) * 100));
        setTimelineScale(newScale);
      }
    } else {
      setTimelineScale(100); // Default scale
    }
  }, [events])

  const handleReset = useCallback(() => {
    console.debug("[NFMTimelineComplete] Resetting timeline to default state");
    setTimelineScale(100)
    timelineStateRef.current?.setTime(0)
   
  }, [timelineStateRef])

  // Handle auto-scroll to time for event navigation
  const handleScrollToTime = useCallback((time: number) => {
    console.debug(`[NFMTimelineComplete] Scrolling to time: ${time}`);
   // const left = time * (DEFAULT_SCALE_WIDTH / timelineScale) + DEFAULT_START_LEFT;

    // Set the timeline cursor to the target time
    //setInternalCurrentTime(time);
    timelineStateRef.current?.setTime(time)

    // Calculate scroll position to center the time in viewport
    // Using the timeline scale to convert time to pixels
    const pixelsPerSecond = 100 / timelineScale; // 100px per scale unit
    const targetPixelX = parserTimeToPixel(time, {
        startLeft: 0,
        scale: timelineScale,
        scaleWidth: 800,
    });

    // Assume viewport width (this could be passed as a prop or calculated)
    const viewportWidth = 800; // Default viewport width
    const centeredScrollLeft = targetPixelX - viewportWidth / 2;
    const clampedScrollLeft = Math.max(0, centeredScrollLeft);

    // Access the timeline ref through the NFMTimelineEditor
    // The timeline ref should be available through the editor
    if (timelineStateRef.current?.setScrollLeft) {
      timelineStateRef.current.setScrollLeft(clampedScrollLeft);
    }
  }, [timelineScale, timelineStateRef]);


  

  // Show loading state while data is loading
  if (!currentProject || !modalities || !events) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Loading...</h1>
          <p className="text-muted-foreground">
            Loading project data...
          </p>
        </div>
      </div>
    );
  }

  // Backend already filters events by visibility, modalities have isVisible property
  const visibleModalities = modalities.filter(m => m.isVisible);

  // Calculate timeline height based on visible modalities (desktop auto-sizing)
  //const calculatedTimelineHeight = Math.max(200, Math.min(600, visibleModalities.length * 40 + 60)); // 40px per row + 60px for controls
  //const finalTimelineHeight = `${calculatedTimelineHeight}px`;
  

  return (
    <div className={cn("space-y-4", className)}>
      <Card className={cardClassName}>
        {showHeader && (
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Neuromonitoring Timeline
              </CardTitle>

              <div className="flex items-center gap-4">
                {/* Events and Modalities Stats */}
                <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300">
                  <div className="flex items-center gap-1">
                    <span className="text-xs">Events:</span>
                    <span className="font-semibold">{events?.length || 0}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-xs">Modalities:</span>
                    <span className="font-semibold">{modalities?.length || 0}</span>
                  </div>
                </div>

                {/* Options Button with Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      title="Timeline Options"
                      className="h-8 px-3"
                    >
                      <Settings className="h-4 w-4 mr-1" />
                      Options
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem onClick={() => setShowKeyboardHelp(true)}>
                      <Keyboard className="h-4 w-4 mr-2" />
                      Show Keyboard Info
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => {/* TODO: Export functionality */ }}>
                      <Download className="h-4 w-4 mr-2" />
                      Export Timeline
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => {/* TODO: Import functionality */ }}>
                      <Upload className="h-4 w-4 mr-2" />
                      Import Timeline
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => {/* TODO: Save functionality */ }}>
                      <Save className="h-4 w-4 mr-2" />
                      Save Session
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardHeader>
        )}

        <CardContent className={cn("space-y-4", !showHeader && "pt-6")}>
          {/* Timeline Controls */}
          {showControls && (
            <NFMTimelineControls
              currentTime={videoTimeline.currentTime}
              duration={videoTimeline.duration}
              isPlaying={videoTimeline.isPlaying}
              events={events}
              modalities={modalities}
              onPlay={videoTimeline.play}
              onPause={videoTimeline.pause}
              onPrevEvent={handlePrevEvent}
              onNextEvent={handleNextEvent}
              onZoom={handleZoom}
              playbackRate={videoTimeline.playbackRate}
              onPlaybackRateChange={videoTimeline.setPlaybackRate}
              onScrollToTime={handleScrollToTime}
              interactionMode={interactionMode}
              setInteractionMode={setInteractionModeState}
              className=""
            />
          )}

          {/* Timeline Editor */}
          <div
            className="relative"
            style={{
              //height: calculatedTimelineHeight,
            }}
          >
            <NFMTimelineEditor
              ref={timelineStateRef}
              events={events}
              modalities={modalities}
              visibleModalities={visibleModalities}
              currentTime={videoTimeline.currentTime}
              duration={videoTimeline.duration}
              isPlaying={videoTimeline.isPlaying}
              scale={timelineScale}
              onScaleChange={handleZoom}
              //height={calculatedTimelineHeight}
              interactionMode={interactionMode}
             // onTimeChange={onTimeChange}
             // onPlayStateChange={onPlayStateChange}
             // onPlaybackRateChange={setPlaybackRate}
            //  playbackRate={playbackRate}
              onEventCreate={handleCreateEvent}
              onEventDelete={handleEventDelete}
              onVisibilityChange={handleModalityVisibilityChange}
              config={{
                //allowCreate: allowEditingState && !readOnly && !disabled,
                //allowDelete: allowEditingState && !readOnly && !disabled,
                //allowResize: allowEditingState && !readOnly && !disabled,
                //allowDrag: allowEditingState && !readOnly && !disabled,
                enableKeyboardShortcuts: true,
                enableContextMenu: true,
                snapToGrid: true,
                gridInterval: 5, // 5 second grid
                rowHeight: isMobile ? 40 : 50,
                cursorColor: "#3b82f6",
                selectionColor: "#3b82f6",
                //showTimeScale: true,
                //enableZoom: true,
                //enableSelection: !readOnly,
                //mobileOptimized: isMobile
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Keyboard shortcuts help */}
      <TimelineKeyboardShortcutsHelp
        isOpen={showKeyboardHelp}
        onClose={() => setShowKeyboardHelp(false)}
      />
    </div>
  )
}

