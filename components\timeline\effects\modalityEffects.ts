/**
 * Effect definitions for different modalities and event types in the NFM timeline
 */

import { TimelineEffect } from '../interface/timeline';
import { NFMTimelineEffect } from '@/types/timelineEditor';
import { Doc } from '@/convex/_generated/dataModel';

/**
 * Create effect for a specific modality
 */
export function createModalityEffect(modality: Doc<"modalityConfigs">): NFMTimelineEffect {
  return {
    id: `modality-${modality._id}`,
    name: modality.displayName || modality.name,
    color: modality.colorCode,
    category: 'modality',
    priority: 1,
    customRenderer: true,
    source: {
      start: (param) => {
        console.log(`[${modality.name}] Started playing event at ${param.time.toFixed(2)}s`);
        
        // Trigger any modality-specific start behaviors
        if (modality.name === 'EEG') {
          // EEG-specific start logic
          console.log('EEG monitoring started');
        } else if (modality.name === 'EMG') {
          // EMG-specific start logic
          console.log('EMG monitoring started');
        }
      },
      
      enter: (param) => {
        console.log(`[${modality.name}] Entered event at ${param.time.toFixed(2)}s`);
        
        // Visual feedback when entering an event
        const element = document.querySelector(`[data-action-id="${param.action.id}"]`);
        if (element) {
          element.classList.add('timeline-action-active');
        }
      },
      
      update: (param) => {
        // Real-time updates during playback
        const progress = (param.time - param.action.start) / (param.action.end - param.action.start);
        
        // Update progress indicators if needed
        const element = document.querySelector(`[data-action-id="${param.action.id}"]`) as HTMLElement | null;
        if (element) {
          element.style.setProperty('--progress', `${Math.min(progress * 100, 100)}%`);
        }
        
        // Modality-specific update logic
        if (modality.name === 'Video' && param.isPlaying) {
          // Sync video playback if needed
        }
      },
      
      leave: (param) => {
        console.log(`[${modality.name}] Left event at ${param.time.toFixed(2)}s`);
        
        // Clean up visual feedback
        const element = document.querySelector(`[data-action-id="${param.action.id}"]`) as HTMLElement | null;;
        if (element) {
          element.classList.remove('timeline-action-active');
          element.style.removeProperty('--progress');
        }
      },
      
      stop: (param) => {
        console.log(`[${modality.name}] Stopped playing event at ${param.time.toFixed(2)}s`);
        
        // Modality-specific stop logic
        if (modality.name === 'Audio') {
          // Stop audio playback
          console.log('Audio playback stopped');
        }
      }
    }
  };
}

/**
 * Create effects for different event types
 */
export function createEventTypeEffects(): Record<string, NFMTimelineEffect> {
  const eventTypes = [
    {
      id: 'alarm',
      name: 'Alarm Event',
      color: '#ef4444', // red-500
      priority: 5
    },
    {
      id: 'warning',
      name: 'Warning Event',
      color: '#f59e0b', // amber-500
      priority: 4
    },
    {
      id: 'info',
      name: 'Info Event',
      color: '#3b82f6', // blue-500
      priority: 2
    },
    {
      id: 'critical',
      name: 'Critical Event',
      color: '#dc2626', // red-600
      priority: 6
    },
    {
      id: 'custom',
      name: 'Custom Event',
      color: '#6b7280', // gray-500
      priority: 1
    }
  ];

  const effects: Record<string, NFMTimelineEffect> = {};

  eventTypes.forEach(eventType => {
    effects[`event-${eventType.id}`] = {
      id: `event-${eventType.id}`,
      name: eventType.name,
      color: eventType.color,
      category: 'event',
      priority: eventType.priority,
      customRenderer: true,
      source: {
        start: (param) => {
          console.log(`[${eventType.name}] Started at ${param.time.toFixed(2)}s`);
          
          // Event-specific start behaviors
          if (eventType.id === 'alarm' || eventType.id === 'critical') {
            // Flash or highlight critical events
            const element = document.querySelector(`[data-action-id="${param.action.id}"]`);
            if (element) {
              element.classList.add('timeline-action-critical');
            }
          }
        },
        
        enter: (param) => {
          console.log(`[${eventType.name}] Entered at ${param.time.toFixed(2)}s`);
          
          // Add visual feedback
          const element = document.querySelector(`[data-action-id="${param.action.id}"]`);
          if (element) {
            element.classList.add('timeline-action-active');
            
            // Add severity-specific classes
            element.classList.add(`timeline-action-${eventType.id}`);
          }
        },
        
        update: (param) => {
          // Update progress and any real-time indicators
          const progress = (param.time - param.action.start) / (param.action.end - param.action.start);
          const element = document.querySelector(`[data-action-id="${param.action.id}"]`) as HTMLElement | null;;
          
          if (element) {
            element.style.setProperty('--progress', `${Math.min(progress * 100, 100)}%`);
            
            // Pulse effect for critical events
            if (eventType.id === 'critical' && param.isPlaying) {
              const pulseIntensity = Math.sin(param.time * 4) * 0.3 + 0.7;
              element.style.setProperty('--pulse-opacity', pulseIntensity.toString());
            }
          }
        },
        
        leave: (param) => {
          console.log(`[${eventType.name}] Left at ${param.time.toFixed(2)}s`);
          
          // Clean up visual effects
          const element = document.querySelector(`[data-action-id="${param.action.id}"]`) as HTMLElement | null;;
          if (element) {
            element.classList.remove('timeline-action-active');
            element.classList.remove(`timeline-action-${eventType.id}`);
            element.classList.remove('timeline-action-critical');
            element.style.removeProperty('--progress');
            element.style.removeProperty('--pulse-opacity');
          }
        },
        
        stop: (param) => {
          console.log(`[${eventType.name}] Stopped at ${param.time.toFixed(2)}s`);
          
          // Event-specific stop logic
          if (eventType.id === 'alarm') {
            // Stop alarm sounds or notifications
            console.log('Alarm stopped');
          }
        }
      }
    };
  });

  return effects;
}

/**
 * Create system-level effects for timeline functionality
 */
export function createSystemEffects(): Record<string, NFMTimelineEffect> {
  return {
    'system-cursor': {
      id: 'system-cursor',
      name: 'Timeline Cursor',
      color: '#ef4444',
      category: 'system',
      priority: 10,
      customRenderer: false,
      source: {
        update: (param) => {
          // Update cursor position and any related UI elements
          const cursor = document.querySelector('.timeline-cursor');
          if (cursor && param.isPlaying) {
            cursor.classList.add('timeline-cursor-playing');
          } else if (cursor) {
            cursor.classList.remove('timeline-cursor-playing');
          }
        }
      }
    },
    
    'system-selection': {
      id: 'system-selection',
      name: 'Selection Indicator',
      color: '#3b82f6',
      category: 'system',
      priority: 8,
      customRenderer: false,
      source: {
        enter: (param) => {
          // Handle selection visual feedback
          console.log(`Selected action: ${param.action.id}`);
        },
        
        leave: (param) => {
          // Clean up selection feedback
          console.log(`Deselected action: ${param.action.id}`);
        }
      }
    }
  };
}

/**
 * Combine all effects into a single registry
 */
export function createAllEffects(modalities: Doc<"modalityConfigs">[]): Record<string, NFMTimelineEffect> {
  const effects: Record<string, NFMTimelineEffect> = {};
  
  // Add modality-specific effects
  modalities.forEach(modality => {
    const modalityEffect = createModalityEffect(modality);
    effects[modalityEffect.id] = modalityEffect;
  });
  
  // Add event type effects
  const eventTypeEffects = createEventTypeEffects();
  Object.assign(effects, eventTypeEffects);
  
  // Add system effects
  const systemEffects = createSystemEffects();
  Object.assign(effects, systemEffects);
  
  return effects;
}

/**
 * Get effect by ID with fallback
 */
export function getEffect(
  effectId: string, 
  effects: Record<string, NFMTimelineEffect>
): NFMTimelineEffect {
  return effects[effectId] || {
    id: effectId,
    name: 'Unknown Effect',
    color: '#6b7280',
    category: 'custom',
    priority: 0,
    customRenderer: false,
    source: {
      start: () => console.log(`Unknown effect started: ${effectId}`),
      enter: () => console.log(`Unknown effect entered: ${effectId}`),
      update: () => {},
      leave: () => console.log(`Unknown effect left: ${effectId}`),
      stop: () => console.log(`Unknown effect stopped: ${effectId}`)
    }
  };
}

/**
 * Effect priority comparator for sorting
 */
export function compareEffectPriority(
  a: NFMTimelineEffect, 
  b: NFMTimelineEffect
): number {
  return (b.priority || 0) - (a.priority || 0);
}

/**
 * Filter effects by category
 */
export function filterEffectsByCategory(
  effects: Record<string, NFMTimelineEffect>,
  category: 'modality' | 'event' | 'system'
): Record<string, NFMTimelineEffect> {
  const filtered: Record<string, NFMTimelineEffect> = {};
  
  Object.entries(effects).forEach(([id, effect]) => {
    if (effect.category === category) {
      filtered[id] = effect;
    }
  });
  
  return filtered;
}

/**
 * Get effects for a specific modality
 */
export function getModalityEffects(
  modalityId: string,
  effects: Record<string, NFMTimelineEffect>
): NFMTimelineEffect[] {
  return Object.values(effects).filter(effect => 
    effect.category === 'modality' && effect.id.includes(modalityId)
  );
}

/**
 * Validate effect configuration
 */
export function validateEffect(effect: NFMTimelineEffect): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  if (!effect.id) {
    errors.push('Effect must have an ID');
  }
  
  if (!effect.name) {
    errors.push('Effect must have a name');
  }
  
  if (effect.priority !== undefined && (effect.priority < 0 || effect.priority > 10)) {
    errors.push('Effect priority must be between 0 and 10');
  }
  
  if (effect.color && !/^#[0-9a-fA-F]{6}$/.test(effect.color)) {
    errors.push('Effect color must be a valid hex color');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
