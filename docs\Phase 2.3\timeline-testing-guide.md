# Timeline Fixes - Manual Testing Guide

**Purpose**: Comprehensive testing checklist to validate all critical timeline fixes
**Date**: May 31, 2025
**Phase**: 2.3.1 Timeline Critical Fixes Validation

## 🎯 Quick Validation Steps

### 1. Basic Event Positioning Test
1. Open `/live-monitoring` page
2. Click on any event marker on the timeline
3. **✅ Expected**: Dot/bar appears exactly at the correct time position
4. **❌ Bug Fixed**: Previously events appeared offset to the right

### 2. Event Duration Display Test  
1. Zoom IN to most detailed level (15s per 100px)
2. **✅ Expected**: Events with duration show as colored bars
3. Zoom OUT to overview level (300s per 100px)  
4. **✅ Expected**: All events show as dots for overview

### 3. Next Event Auto-Scroll Test
1. Zoom in to see only part of timeline
2. Click "Next Event" button multiple times
3. **✅ Expected**: Timeline auto-scrolls to center each event
4. **❌ Bug Fixed**: Previously timeline didn't scroll

### 4. Time Indicator Dragging Test
1. Click and drag the red time indicator
2. **✅ Expected**: Indicator follows mouse smoothly in real-time
3. **❌ Bug Fixed**: Previously only jumped on mouse release

### 5. Manual Scrolling Test
1. Use mouse wheel to scroll timeline horizontally
2. Try scroll left/right buttons in header
3. Use Ctrl+scroll wheel to zoom in/out
4. **✅ Expected**: All scrolling methods work smoothly

### 6. Context Menu Test
1. Right-click any event marker
2. **✅ Expected**: Context menu appears with View/Edit/Delete
3. Test "Delete" option to remove an event
4. **✅ Expected**: Event disappears from timeline

### 7. Modality Navigation Test
1. Switch to "Expanded View" mode
2. Hover over modality labels (EMG, MEP, etc.)
3. **✅ Expected**: Navigation arrows appear
4. Click arrows to jump between events of that modality

## 🔧 Detailed Technical Validation

### Event Duration Schema Test
1. Create new event via timeline click
2. Check that event has both `time` and `startTime`/`endTime` fields
3. Verify backward compatibility with existing events

### Auto-Scroll Edge Cases
1. Let video play until time indicator reaches edge
2. **✅ Expected**: Timeline auto-scrolls to keep indicator visible
3. Test with different zoom levels

### Performance Test
1. Create several events quickly by clicking timeline
2. Test scrolling with 10+ events visible
3. **✅ Expected**: No lag or performance issues

### Keyboard Shortcuts Test
1. Use left/right arrow keys for seeking
2. Use Ctrl+scroll for zooming
3. Test space bar (if implemented)

## 📊 Sample Events for Testing

The timeline includes these test events with duration:
- **MEP Loss** (15:00-15:30) - 30 second critical event
- **EMG Burst** (20:00-20:15) - 15 second medium event  
- **SSEP Decrease** (30:00) - Point event (no duration)
- **MEP Recovery** (40:00-41:00) - 1 minute high event

## 🎯 Success Criteria

### ✅ All Critical Issues Fixed
- [ ] Event positioning accurate to timeline
- [ ] Duration events display correctly when zoomed in
- [ ] Point events display as dots when zoomed out
- [ ] Next Event navigation scrolls timeline
- [ ] Time indicator drags smoothly
- [ ] Manual scrolling with mouse/keyboard works
- [ ] Context menu provides event actions
- [ ] Modality navigation arrows functional

### ✅ Performance Requirements Met
- [ ] Smooth interaction with 10+ events
- [ ] No frame drops during scrolling
- [ ] Responsive zoom operations
- [ ] Memory usage stable

### ✅ Medical Precision Standards
- [ ] Sub-second timing accuracy
- [ ] Clear visual distinction between event types
- [ ] Proper color coding maintained
- [ ] Professional medical interface feel

## 🐛 Known Limitations (Future Enhancement)

1. **Context Menu Positioning**: May clip at screen edges
2. **Keyboard Focus**: Space bar doesn't control video yet
3. **Event Selection**: No multi-select capability yet
4. **Minimap**: Overview navigation not implemented

## 🚀 Ready for Phase 3.1

With all fixes complete, the timeline is ready for:
- Event creation modal integration
- Advanced event editing capabilities  
- Real-time collaborative features
- Export and reporting functions

---

**Validation Status**: Ready for comprehensive testing
**Next Phase**: 3.1 Event Creation & Annotation System
