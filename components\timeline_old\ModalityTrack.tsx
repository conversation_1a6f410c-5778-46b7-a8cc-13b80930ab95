import React, { useState } from 'react'
import { cn } from '@/lib/utils'
import { type UseTimelineScaleReturn } from '@/hooks/useTimelineScale'
import { TimelineEvent, TimelineModality } from '@/types/timeline'
import { EventMarker } from './EventMarker'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { 
  HoverCard, 
  HoverCardContent, 
  HoverCardTrigger 
} from '@/components/ui/hover-card'

export interface ModalityTrackProps {
  modality: TimelineModality
  timeline: UseTimelineScaleReturn
  events?: TimelineEvent[]
  isExpanded?: boolean
  className?: string
  onEventClick?: (event: TimelineEvent) => void
  onTrackClick?: (time: number) => void
  onEventEdit?: (event: TimelineEvent) => void
  onEventDelete?: (event: TimelineEvent) => void
  onModalityNavigate?: (modalityId: string, direction: 'prev' | 'next') => void
}

export function ModalityTrack({
  modality,
  timeline,
  events = [],
  isExpanded = true,
  className,
  onEventClick,
  onTrackClick,
  onEventEdit,
  onEventDelete,
  onModalityNavigate
}: ModalityTrackProps) {
  const { 
    getTimeAtPosition, 
    pixelsPerSecond,
    scrollLeft,
    viewportWidth,
    duration
  } = timeline

  const [isLabelHovered, setIsLabelHovered] = useState(false)

  // Filter events for this modality (or all events if this is the compact "ALL" track)
  const trackEvents = isExpanded && modality.name !== 'ALL'
    ? events.filter(event => event.modalityId === modality._id)
    : events // Show all events on the compact "ALL" track



  // Navigation logic for this modality
  const currentModalityEvents = events
    .filter(e => e.modalityId === modality._id)
    .sort((a, b) => a.startTime - b.startTime)
  
  const currentTime = timeline.currentTime
  const prevEvent = currentModalityEvents.filter(e => e.startTime < currentTime).pop()
  const nextEvent = currentModalityEvents.find(e => e.startTime > currentTime)

  const handleTrackClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!onTrackClick) return
    
    const rect = event.currentTarget.getBoundingClientRect()
    const clickX = event.clientX - rect.left
    const time = getTimeAtPosition(clickX)
    onTrackClick(Math.max(0, Math.min(time, duration)))
  }

  const handleModalityNavigation = (direction: 'prev' | 'next', event: React.MouseEvent) => {
    event.preventDefault()
    event.stopPropagation()
    
    if (onModalityNavigate) {
      onModalityNavigate(modality._id, direction)
    }
  }

  // Calculate if event should be dot or bar based on actual pixel width
  const shouldShowAsDot = (startTime: number, endTime?: number) => {
    if (!endTime || endTime <= startTime) return true
    
    const durationPixels = (endTime - startTime) * pixelsPerSecond
    const DOT_SIZE = 12 // 12px diameter
    
    return durationPixels <= DOT_SIZE
  }

  return (
    <div
      className={cn(
        "relative border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900",
        isExpanded ? "h-10" : "h-15",
        className
      )}
      onClick={handleTrackClick}
    >
      {/* Track label with navigation */}
      <HoverCard>
        <HoverCardTrigger asChild>
          <div
            className="absolute left-0 top-0 w-16 h-full flex items-center justify-center bg-gray-100 dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 z-10 group cursor-help"
            onMouseEnter={() => setIsLabelHovered(true)}
            onMouseLeave={() => setIsLabelHovered(false)}
          >
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
              {modality.name === 'ALL' ? 'Timeline' : modality.name}
            </span>
            
            {/* Navigation arrows */}
            {isExpanded && modality.name !== 'ALL' && isLabelHovered && (
              <div className="absolute inset-0 flex items-center justify-between px-1 bg-gray-100 dark:bg-gray-800">
                <button
                  onClick={(e) => handleModalityNavigation('prev', e)}
                  disabled={!prevEvent}
                  className="w-4 h-4 flex items-center justify-center text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded disabled:opacity-30 disabled:cursor-not-allowed"
                  title={prevEvent ? `Previous ${modality.name} event` : `No previous ${modality.name} events`}
                >
                  <ChevronLeft className="w-3 h-3" />
                </button>
                <button
                  onClick={(e) => handleModalityNavigation('next', e)}
                  disabled={!nextEvent}
                  className="w-4 h-4 flex items-center justify-center text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded disabled:opacity-30 disabled:cursor-not-allowed"
                  title={nextEvent ? `Next ${modality.name} event` : `No upcoming ${modality.name} events`}
                >
                  <ChevronRight className="w-3 h-3" />
                </button>
              </div>
            )}
          </div>
        </HoverCardTrigger>
        <HoverCardContent className="w-80" side="right">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div 
                className="w-4 h-4 rounded-full"
                style={{ backgroundColor: modality.colorCode }}
              />
              <span className="font-semibold">{modality.displayName}</span>
            </div>
            <div className="text-sm text-muted-foreground">
              Click to create events at specific times
            </div>
            {modality.name !== 'ALL' && (
              <div className="text-xs text-muted-foreground">
                <div>Events: {currentModalityEvents.length}</div>
                <div>Hover for navigation arrows</div>
              </div>
            )}
          </div>
        </HoverCardContent>
      </HoverCard>

      {/* Track content area - corrected positioning */}
      <div className="ml-16 h-full cursor-pointer relative">
        {/* Event markers using the new EventMarker component */}
        {trackEvents.map((event) => {
          // Position calculation: account for the content area being inside ml-16
          const startPixel = event.startTime * pixelsPerSecond - scrollLeft
          const endPixel = event.endTime ? event.endTime * pixelsPerSecond - scrollLeft : startPixel
          
          // Visibility check with buffer
          const BUFFER = 50
          const isVisible = startPixel >= -BUFFER && startPixel <= viewportWidth + BUFFER
          
          const shouldUseDot = shouldShowAsDot(event.startTime, event.endTime)
          const width = shouldUseDot ? 0 : Math.max(4, endPixel - startPixel)

          return (
            <EventMarker
              key={event._id}
              event={event}
              position={startPixel}
              width={width}
              isVisible={isVisible}
              showAsDot={shouldUseDot}
              onEventClick={onEventClick}
              onEventEdit={onEventEdit}
              onEventDelete={onEventDelete}
            />
          )
        })}
      </div>
    </div>
  )
}
