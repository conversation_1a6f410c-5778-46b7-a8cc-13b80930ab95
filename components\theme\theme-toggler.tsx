"use client";

import { MoonI<PERSON>, SunIcon } from "lucide-react";
import { useTheme } from "next-themes";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";

export default function ThemeToggler() {
  const { theme, setTheme } = useTheme();
  const [systemTheme, setSystemTheme] = useState<"light" | "dark">("light");

  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    setSystemTheme(mediaQuery.matches ? "dark" : "light");

    const handleChange = (e: MediaQueryListEvent) => {
      setSystemTheme(e.matches ? "dark" : "light");
    };

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, []);

  const SWITCH = () => {
    switch (theme) {
      case "light":
        setTheme("dark");
        break;
      case "dark":
        setTheme("light");
        break;
      case "system":
        setTheme(systemTheme === "light" ? "dark" : "light");
        break;
      default:
        break;
    }
  };

  const TOGGLE_THEME = () => {
    if (!document.startViewTransition) SWITCH();

    document.startViewTransition(SWITCH);
  };

  return (
    <Button
      onClick={TOGGLE_THEME}
      variant="outline"
      size="sm"
      className="rounded size-7 md:size-8 p-0"
    >
      <SunIcon
        className="rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0 size-3 md:size-4"
      />
      <MoonIcon
        className="absolute rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100 size-3 md:size-4"
      />
      <span className="sr-only">Toggle theme</span>
    </Button>
  );
}