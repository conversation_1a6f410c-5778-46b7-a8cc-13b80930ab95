# NFM System - Development Starter Prompt
## Ready-to-Use LLM Prompt for Project Initiation

### 🚀 STARTER PROMPT

```
I need you to help me build an NFM (NeuroFysiology Monitoring) system for real-time surgical neuromonitoring. I already have a working Convex + Next.js + Convex Auth setup running locally.

PROJECT OVERVIEW:
This is a medical application for real-time monitoring during brain/spinal surgery. It needs to:
- Stream RTSP video from Inomed monitoring system to web browsers via WebRTC
- Provide real-time collaborative timeline with event annotation
- Support pre/post-operative clinical examinations
- Generate comprehensive surgical reports
- Handle patient data securely with role-based access

CURRENT SETUP STATUS:
✅ Next.js 15 project initialized
✅ Convex development server running
✅ Convex Auth configured and working
✅ Basic project structure in place
✅ Tailwind CSS configured

IMMEDIATE TASK:
Implement Phase 1.1 from the implementation checklist: "Database Schema & Authentication Setup"

REQUIREMENTS FOR THIS TASK:
1. Implement the complete Convex database schema from backend-specifications.md
2. Set up role-based authentication system (surgeon, anesthesiologist, neurophysiologist, admin, technician)
3. Create user management functions with proper authorization
4. Add seed data for initial modalities (EMG, MEP, SSEP, BAEP, VEP, AEP) 
5. Include proper indexing for performance and medical data queries

REFERENCE FILES:
- backend-specifications.md: Complete schema implementation details
- implementation-checklist-prompts.md: Specific requirements for Phase 1.1
- progression-log.md: Track your progress here

DELIVERABLES:
1. convex/schema.ts - Complete medical database schema
2. convex/auth.ts - Enhanced authentication with roles
3. convex/users.ts - User management queries and mutations
4. convex/seed.ts - Initial medical data seeding
5. Updated progression-log.md with implementation decisions

VALIDATION CRITERIA:
- All tables have proper medical-specific indexes
- Role-based access control works correctly
- Seed data includes all required modalities with event types
- User creation and login flow works with role assignment
- Patient data handling follows medical privacy requirements

IMPORTANT NOTES:
- This is a medical application - prioritize data security and audit trails
- All database operations should include proper error handling
- Use Convex best practices (reference .cursor/rules/convex_rules.mdc if available)
- Focus on real-time capabilities - this system needs live collaboration
- After completion, update progression-log.md and commit changes

START WITH:
Reading backend-specifications.md for the complete schema structure, then implement the Convex schema with all medical entities, relationships, and indexes.

When complete, mark checkbox "✅ 1.1 Database Schema & Authentication Setup" in progression-log.md and proceed to Phase 1.2 if time permits.
```

---

### 📁 Required Files for Starter Prompt

**Essential Files** (provide these to the LLM):
1. **`backend-specifications.md`** - Complete schema and authentication details
2. **`implementation-checklist-prompts.md`** - Specific task requirements
3. **`progression-log.md`** - Progress tracking template
4. **Original `paste.txt`** - Requirements reference

**Supporting Files** (helpful but not essential):
5. **`frontend-specifications.md`** - UI context for data requirements
6. **`technology-evaluation.md`** - Architecture decisions context

---

### 🔄 Continuation Workflow

**After Phase 1.1 Complete:**

```
CONTINUATION PROMPT:
I'm continuing development of the NFM system. I've completed Phase 1.1 (Database Schema & Authentication).

CURRENT STATUS:
- Database schema implemented and working
- Authentication system with role-based access functional
- Seed data loaded for medical modalities
- [Include any specific progress from progression-log.md]

NEXT TASK: Phase 1.2 - Basic UI Layout & Navigation

REFERENCE FILES:
- progression-log.md: Current status and decisions
- frontend-specifications.md: Layout specifications 
- implementation-checklist-prompts.md: Phase 1.2 requirements

Please implement the main application layout with sidebar and navigation system according to the specifications.
```

**For Each New Phase:**
1. Reference `progression-log.md` for current status
2. Use specific prompt from `implementation-checklist-prompts.md`
3. Include relevant specification files
4. Update progression log after completion
5. Commit changes before starting new chat

---

### 💡 Context Window Management Strategy

**When Context Gets Full:**
1. **Commit Current Work**: `git add . && git commit -m "Phase X.Y complete - [description]"`
2. **Update Progression Log**: Mark completed checkboxes and add implementation notes
3. **Start New Chat** with continuation prompt
4. **Reference Files**: Always include progression-log.md + current phase specifications

**File Priority for Limited Context:**
1. `progression-log.md` (always include)
2. Current phase specification file
3. `implementation-checklist-prompts.md` 
4. Work-in-progress code files

---

### 🎯 Success Indicators

**Phase Completion Criteria:**
- All checkboxes in current phase marked complete
- Validation criteria met for each task
- Progression log updated with key decisions
- Git commit made with descriptive message
- Next phase requirements understood

**Quality Gates:**
- TypeScript compiles without errors
- Convex development server runs without issues
- Real-time functionality tested (multiple browser tabs)
- Medical data privacy and security considered
- Performance acceptable for intended user load

---

### 📞 Support & References

**If Stuck:**
- Re-read the specific phase requirements in `implementation-checklist-prompts.md`
- Check `backend-specifications.md` or `frontend-specifications.md` for detailed examples
- Review `technology-evaluation.md` for architecture decisions
- Consult original `paste.txt` for requirements clarification

**Medical Context:**
- This is for real surgical monitoring - accuracy and reliability are critical
- Real-time collaboration is essential - multiple users must see updates immediately
- Data security and audit trails are mandatory for medical compliance
- User interface must be intuitive for high-stress surgical environments

**Technical Context:**
- Convex provides real-time synchronization automatically
- Use ShadCN/UI components for consistent medical interface design
- MediaMTX will handle video streaming (implement in Phase 2)
- Focus on TypeScript type safety throughout

---

**Starter Prompt Version**: 1.0  
**Compatible With**: Convex + Next.js 15 + Convex Auth setup  
**Created**: May 25, 2025
