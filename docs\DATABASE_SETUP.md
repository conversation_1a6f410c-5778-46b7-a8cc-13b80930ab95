# ✅ FIXED: Database Setup Instructions

## 🚀 Quick Setup Steps

### 1. First, Seed Modalities (if not already done)
In your Convex dashboard:
1. Find `seed:seedData` 
2. Click "Run" (this creates the 6 modalities: EMG, MEP, SSEP, BAEP, VEP, AEP)

### 2. Create Test Project
In your Convex dashboard:
1. Find `seed:seedTestProject`
2. Click "Run" (this creates a complete test project with events)

This will create:
- ✅ 1 test user (neurophysiologist) 
- ✅ 1 test patient (<PERSON>)
- ✅ 1 test project (TEST-001) using the first 3 modalities
- ✅ 1 test session (live status)
- ✅ 3 sample events with proper duration data

### 3. Verify the Setup
After running `seedTestProject`, you should see:
- **3 modality tracks** on the timeline (EMG, MEP, SSEP with their colors)
- **3 sample events** positioned correctly on their tracks
- **Working modality filter** dropdown with checkboxes
- **Functional context menus** on right-click
- **Toast notifications** for all actions

## 🧹 If You Need to Start Over

### Clear Test Data:
```
seed:clearTestData
```
This removes the test project, events, patient, and test user (but keeps modalities).

### Then Re-create:
```
seed:seedTestProject  
```

## 🔧 What Each Function Does

### `seedData` (run once):
- Creates 6 modality configurations with proper colors
- Creates surgery type templates
- Creates admin user

### `seedTestProject` (run when you want test data):
- Uses existing modalities
- Creates complete test project structure
- Creates sample events for testing timeline

### `clearTestData` (cleanup):
- Removes only test project data
- Keeps modalities intact
- Safe to re-run seedTestProject after this

## 🎯 Expected Results

After running `seedTestProject`, the live monitoring page should show:
- **Project info**: "TEST-001" in header
- **3 modality tracks**: With proper colors from the database
- **3 sample events**: 
  - MEP Loss at 15:00-15:30 (red track)
  - EMG Burst at 20:00-20:15 (blue track) 
  - SSEP Decrease at 30:00 (amber track, point event)
- **Working interactions**: All buttons, context menus, filters functional

## 🚨 Troubleshooting

### "Test data already exists"?
- Run `clearTestData` first, then `seedTestProject`

### "No modalities found"?
- Run `seedData` first to create modalities

### Still no timeline tracks?
- Check browser console for errors
- Verify Convex functions completed successfully
- Check that `getTestProject` returns data

---

**The issue was**: The original `seedData` already created modalities, so the check was preventing further seeding. Now we have separate functions for modalities vs. test projects!
