"use client"

import React, { useRef, useCallback, useEffect, useState, Suspense, useMemo } from 'react'
import dynamic from 'next/dynamic'
import { cn } from '@/lib/utils'
import { useVideoTimeline } from '@/components/contexts/VideoTimelineContext'
import { Loader2 } from 'lucide-react'
import ReactPlayer from 'react-player'

// Simple debounce utility
function debounce<T extends (...args: any[]) => void>(func: T, delay: number): T {
  let timeoutId: NodeJS.Timeout;
  return ((...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  }) as T;
}
// Lazy load ReactPlayer for performance
const ReactPlayer2 = dynamic(() => import('react-player'), {
  ssr: false,
 
})

interface ReactPlayerWrapperProps {
  // Video source
  url: string
  
  // Player configuration
  controls?: boolean
  muted?: boolean
  loop?: boolean
  volume?: number
  
  // Styling
  className?: string
  width?: string | number
  height?: string | number
  
  // Event handlers (optional - will use context if not provided)
  onReady?: () => void
  onError?: (error: any) => void
  onLoadStart?: () => void
  onLoadedData?: () => void
  
  // Player configuration
  config?: {
    youtube?: any
    vimeo?: any
    file?: any
    [key: string]: any
  }
  
  // Performance options
  preload?: 'auto' | 'metadata' | 'none'
  playsinline?: boolean
}

const ReactPlayerWrapperComponent = function ReactPlayerWrapper({
  url,
  controls = false,
  muted = false,
  loop = false,
  volume = 1,
  className,
  width = '100%',
  height = '100%',
  onReady,
  onError,
  onLoadStart,
  onLoadedData,
  config = {},
  preload = 'metadata',
  playsinline = true,
}: ReactPlayerWrapperProps) {
  console.log('[ReactPlayerWrapper] RENDER - Component rendering with props:', {
    url: url?.substring(0, 50) + '...',
    controls,
    muted,
    volume
  })

  const playerRef = useRef<HTMLVideoElement>(null)
  const videoTimeline = useVideoTimeline()
  const [isPlayerReady, setIsPlayerReady] = useState(false)
  const [internalVolume, setInternalVolume] = useState(volume)
  const [internalMuted, setInternalMuted] = useState(muted)
  
  // Track if we're currently seeking to prevent feedback loops
  const isSeekingRef = useRef(false)
  const lastSeekTimeRef = useRef<number>(0)

  // Debounce time updates to reduce frequency and improve performance
  const debouncedTimeUpdate = useMemo(
    () => {
      console.log('[ReactPlayerWrapper] DEBOUNCED UPDATE RECREATED - Dependencies changed')
      return debounce((time: number) => {
        if (!isSeekingRef.current && isPlayerReady && !videoTimeline.isSeeking) {
          console.log('[ReactPlayerWrapper] TIME UPDATE - Updating time to:', time)
          videoTimeline.updateCurrentTime(time, 'video');
        }
      }, 100) // 100ms debounce
    },
    [videoTimeline, isPlayerReady]
  );

  // Removed debug logs to prevent performance issues

  /**
   * Handle player ready event
   */
  const handleReady = useCallback(() => {
    console.debug('[ReactPlayerWrapper] Player ready')
    setIsPlayerReady(true)
    videoTimeline.setVideoLoading(false)
    
    // Get initial duration if available
    if (playerRef.current) {
      const duration = playerRef.current.duration
      if (duration && duration > 0) {
        videoTimeline.setDuration(duration)
      }
    }
    
    onReady?.()
  }, [videoTimeline, onReady])

  /**
   * Handle player error
   */
  const handleError = useCallback((error: any) => {
    console.error('[ReactPlayerWrapper] Player error:', error)
    videoTimeline.setVideoError(error?.message || 'Video playback error')
    videoTimeline.setVideoLoading(false)
    onError?.(error)
  }, [videoTimeline, onError])

  /**
   * Handle load start
   */
  const handleLoadStart = useCallback(() => {
    console.debug('[ReactPlayerWrapper] Load start')
    videoTimeline.setVideoLoading(true)
    videoTimeline.setVideoError(null)
    onLoadStart?.()
  }, [videoTimeline, onLoadStart])

  /**
   * Handle loaded data
   */
  const handleLoadedData = useCallback(() => {
    console.debug('[ReactPlayerWrapper] Loaded data')
    onLoadedData?.()
  }, [onLoadedData])

  /**
   * Handle duration change
   */
  const handleDurationChange = useCallback(() => {
    const player = playerRef.current;
    if (!player) return;

    console.debug('[ReactPlayerWrapper] Duration changed:', player.duration)
    videoTimeline.setDuration(player.duration)
  }, [videoTimeline])

  /**
   * Handle progress updates from the player (now debounced for better performance)
   */
  const handleTimeUpdate = useCallback(() => {
    const player = playerRef.current;
    if (player && player.duration) {
      debouncedTimeUpdate(player.currentTime);
    }
  }, [debouncedTimeUpdate])

  /**
   * Handle play event
   */
  const handlePlay = useCallback(() => {
    console.debug('[ReactPlayerWrapper] Play event')
    if (!videoTimeline.isPlaying) {
      videoTimeline.play()
    }
  }, [videoTimeline])

  /**
   * Handle pause event
   */
  const handlePause = useCallback(() => {
    console.debug('[ReactPlayerWrapper] Pause event')
    if (videoTimeline.isPlaying) {
      videoTimeline.pause()
    }
  }, [videoTimeline])

  /**
   * Handle seeking events
   */
  const handleSeekStart = useCallback(() => {
    console.debug('[ReactPlayerWrapper] Seek start')
    isSeekingRef.current = true
  }, [])

  const handleSeekEnd = useCallback(() => {
    console.debug('[ReactPlayerWrapper] Seek end')
    isSeekingRef.current = false
  }, [])

  /**
   * Seek to specific time (called from context)
   */
  const seekToTime = useCallback((time: number) => {
    if (playerRef.current && isPlayerReady) {
      console.debug('[ReactPlayerWrapper] Seeking to time:', time)
      isSeekingRef.current = true
      lastSeekTimeRef.current = time
      playerRef.current.fastSeek(time)
      
      // Reset seeking flag after a short delay
      setTimeout(() => {
        isSeekingRef.current = false
      }, 100)
    }
  }, [isPlayerReady])

  /**
   * Sync play/pause state with context
   */
  useEffect(() => {
    if (!isPlayerReady || !playerRef.current) return

    const player = playerRef.current
    if (!player) return

    if (videoTimeline.isPlaying) {
      if (player.paused) {
        player.play().catch((error: any) => {
          console.error('[ReactPlayerWrapper] Failed to play:', error)
          videoTimeline.setVideoError('Failed to start playback')
        })
      }
    } else {
      if (!player.paused) {
        player.pause()
      }
    }
  }, [videoTimeline.isPlaying, isPlayerReady])

  /**
   * Sync playback rate with context
   */
  useEffect(() => {
    if (!isPlayerReady || !playerRef.current) return

    const player = playerRef.current
    if (player && player.playbackRate !== videoTimeline.playbackRate) {
      player.playbackRate = videoTimeline.playbackRate
    }
  }, [videoTimeline.playbackRate, isPlayerReady])

  /**
   * Handle external seek requests
   */
  useEffect(() => {
    if (videoTimeline.isSeeking && videoTimeline.seekingSource === 'timeline') {
      if (videoTimeline.seekingTime !== null) {
        seekToTime(videoTimeline.seekingTime)
      }
    }
  }, [videoTimeline.isSeeking, videoTimeline.seekingSource, videoTimeline.seekingTime, seekToTime])

  /**
   * Enhanced player configuration
   */
  const playerConfig = {
    file: {
      attributes: {
        preload,
        playsInline: playsinline,
      },
    },
    ...config,
  }

  return (
    <div className={cn("relative w-full h-full", className)}>
      <Suspense fallback={
        <div className="flex items-center justify-center w-full h-full bg-black rounded-lg">
          <div className="text-center text-white">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-sm">Loading video player...</p>
          </div>
        </div>
      }>
        <ReactPlayer
          ref={playerRef}
          src={url}
          width={width}
          height={height}
          playing={videoTimeline.isPlaying}
          controls={controls}
          muted={internalMuted}
          loop={loop}
          volume={internalVolume}
          playbackRate={videoTimeline.playbackRate}
          config={playerConfig}
          onReady={handleReady}
          onStart={() => console.debug('[ReactPlayerWrapper] Start')}
          onPlay={handlePlay}
          onPause={handlePause}
          onEnded={() => videoTimeline.pause()}
          onError={handleError}
          onLoadStart={handleLoadStart}
          onLoadedData={handleLoadedData}
          onDurationChange={handleDurationChange}
          onTimeUpdate={handleTimeUpdate}
          onSeeking={handleSeekStart}
          onSeeked={handleSeekEnd}
          //progressInterval={100} // Update progress every 100ms
          className="react-player"
          style={{
            borderRadius: '0.5rem',
            overflow: 'hidden',
          }}
        />
      </Suspense>
      
      {/* Loading overlay */}
      {videoTimeline.isVideoLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg">
          <div className="text-center text-white">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-sm">Loading video...</p>
          </div>
        </div>
      )}
      
      {/* Error overlay */}
      {videoTimeline.videoError && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-900 bg-opacity-50 rounded-lg">
          <div className="text-center text-white">
            <p className="text-sm font-medium">Video Error</p>
            <p className="text-xs mt-1">{videoTimeline.videoError}</p>
          </div>
        </div>
      )}
    </div>
  )
}

// Export the component directly (React compiler will handle optimizations)
export const ReactPlayerWrapper = ReactPlayerWrapperComponent
export default ReactPlayerWrapper
