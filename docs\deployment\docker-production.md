# Production Docker Deployment Guide
## NFM System - Containerized Deployment

### 🚀 Overview

This guide covers deploying the NFM system in a production environment using Docker containers. The setup includes Next.js application, MediaMTX streaming server, and all supporting services.

---

## 📦 Prerequisites

- Docker Engine 20.10+ installed
- Docker Compose 2.0+ installed
- SSL certificates for HTTPS/WSS
- Access to hospital network (**********/16)
- Minimum 16GB RAM, 4 CPU cores
- 100GB+ SSD storage for recordings

---

## 🏗️ Production Architecture

```
┌─────────────────────────────────────────────────┐
│            Docker Host (Production)              │
├─────────────────────────────────────────────────┤
│                                                 │
│  ┌──────────────┐  ┌──────────────┐           │
│  │   Nginx      │  │   Next.js    │           │
│  │   Reverse    │◄─┤   App        │           │
│  │   Proxy      │  │   (Port 3000)│           │
│  │   (Port 443) │  └──────────────┘           │
│  └──────┬───────┘                              │
│         │                                       │
│         ▼                                       │
│  ┌──────────────┐  ┌──────────────┐           │
│  │   MediaMTX   │  │   PostgreSQL │           │
│  │   Streaming  │  │   Database   │           │
│  │   Server     │  │   (Port 5432)│           │
│  │   (Port 8554)│  └──────────────┘           │
│  └──────────────┘                              │
│                                                 │
└─────────────────────────────────────────────────┘
```

---

## 📁 Production Docker Compose

Create `docker-compose.prod.yml`:

```yaml
version: '3.9'

services:
  # Nginx Reverse Proxy with SSL
  nginx:
    image: nginx:alpine
    container_name: nfm-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
    depends_on:
      - nextjs
      - mediamtx
    networks:
      - nfm-network

  # Next.js Application
  nextjs:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: nfm-nextjs
    restart: always
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_CONVEX_URL=${CONVEX_URL}
      - CONVEX_DEPLOY_KEY=${CONVEX_DEPLOY_KEY}
      - MEDIAMTX_API_URL=http://mediamtx:9997
      - MEDIAMTX_WEBRTC_URL=wss://your-domain.com/webrtc
    volumes:
      - ./uploads:/app/uploads
    networks:
      - nfm-network
    depends_on:
      - postgres

  # MediaMTX Streaming Server
  mediamtx:
    image: bluenviron/mediamtx:latest-ffmpeg
    container_name: nfm-mediamtx
    restart: always
    environment:
      - MTX_PROTOCOLS=tcp
      - MTX_ENCRYPTION=optional
      - MTX_SERVERKEY=/certs/server.key
      - MTX_SERVERCERT=/certs/server.crt
    ports:
      - "8554:8554"     # RTSP
      - "8889:8889/tcp" # WebRTC TCP
      - "8189:8189/udp" # WebRTC UDP
    volumes:
      - ./mediamtx/mediamtx.yml:/mediamtx.yml:ro
      - ./mediamtx/recordings:/recordings
      - ./certs:/certs:ro
    networks:
      - nfm-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9997/v3/config/get"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: nfm-postgres
    restart: always
    environment:
      - POSTGRES_DB=nfm_production
      - POSTGRES_USER=nfm_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - nfm-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U nfm_user -d nfm_production"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: nfm-redis
    restart: always
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - nfm-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backup service
  backup:
    image: offen/docker-volume-backup:latest
    container_name: nfm-backup
    restart: always
    environment:
      - BACKUP_CRON_EXPRESSION=0 2 * * *
      - BACKUP_FILENAME=nfm-backup-%Y-%m-%d_%H-%M-%S.tar.gz
      - BACKUP_ARCHIVE=/archive
      - BACKUP_RETENTION_DAYS=30
    volumes:
      - postgres_data:/backup/postgres:ro
      - ./mediamtx/recordings:/backup/recordings:ro
      - ./backups:/archive
    networks:
      - nfm-network

networks:
  nfm-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
  redis_data:
```

---

## 🔧 Configuration Files

### Nginx Configuration

Create `nginx/nginx.conf`:

```nginx
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 4096;
    use epoll;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    access_log /var/log/nginx/access.log;

    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Gzip
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml application/json application/javascript;

    # Include site configurations
    include /etc/nginx/conf.d/*.conf;
}
```

Create `nginx/conf.d/nfm.conf`:

```nginx
# Upstream definitions
upstream nextjs {
    server nextjs:3000;
}

upstream mediamtx_webrtc {
    server mediamtx:8889;
}

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL configuration
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;

    # Security headers
    add_header Content-Security-Policy "default-src 'self' wss: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';" always;

    # Next.js application
    location / {
        proxy_pass http://nextjs;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebRTC signaling
    location /webrtc {
        proxy_pass http://mediamtx_webrtc;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebRTC specific
        proxy_buffering off;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # MediaMTX API
    location /api/media/ {
        proxy_pass http://mediamtx:9997/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # Static files
    location /_next/static {
        proxy_pass http://nextjs;
        proxy_cache_valid 60m;
        add_header Cache-Control "public, max-age=3600";
    }
}
```

### MediaMTX Production Configuration

Create `mediamtx/mediamtx.yml`:

```yaml
###############################################################################
# MediaMTX Production Configuration for NFM System

# General settings
logLevel: info
logDestinations: [stdout, file]
logFile: /recordings/mediamtx.log

# API settings
api: yes
apiAddress: :9997

# Metrics for monitoring
metrics: yes
metricsAddress: :9998

# RTSP settings for incoming streams
rtsp: yes
protocols: [tcp, udp]
rtspAddress: :8554
encryption: optional
serverKey: /certs/server.key
serverCert: /certs/server.crt

# WebRTC settings
webrtc: yes
webrtcAddress: :8889
webrtcEncryption: yes
webrtcServerKey: /certs/server.key
webrtcServerCert: /certs/server.crt
webrtcAllowOrigin: "https://your-domain.com"
webrtcICEServers:
  - urls: [stun:stun.l.google.com:19302]

# HLS settings (backup streaming)
hls: yes
hlsAddress: :8888
hlsAllowOrigin: "https://your-domain.com"
hlsSegmentCount: 7
hlsSegmentDuration: 2s

# Recording settings
record: yes
recordPath: /recordings/%path/%Y-%m-%d_%H-%M-%S.mp4
recordFormat: mp4
recordSegmentDuration: 1h
recordDeleteAfter: 720h # 30 days

# Path configurations
paths:
  # Operating Room 1 - Main Inomed Stream
  or1_main:
    source: rtsp://*************:8554/main/av
    sourceProtocol: tcp
    sourceOnDemand: no
    record: yes
    recordDeleteAfter: 2160h # 90 days for OR recordings
    
  # Operating Room 1 - Sub Stream (backup)
  or1_sub:
    source: rtsp://*************:8554/sub/av
    sourceProtocol: tcp
    sourceOnDemand: yes
    
  # Operating Room 2 - Main Inomed Stream
  or2_main:
    source: rtsp://*************:8554/main/av
    sourceProtocol: tcp
    sourceOnDemand: no
    record: yes
    recordDeleteAfter: 2160h
    
  # Microscope Feed
  microscope:
    sourceOnDemand: yes
    record: yes
    
  # Screen capture streams (dynamic)
  screen~^([0-9]+)$:
    sourceOnDemand: yes
    record: yes
```

### Dockerfile for Next.js

Create `Dockerfile.prod`:

```dockerfile
# Build stage
FROM node:20-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY convex.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM node:20-alpine AS runner

WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# Create upload directory
RUN mkdir -p /app/uploads && chown nextjs:nodejs /app/uploads

USER nextjs

EXPOSE 3000

ENV NODE_ENV production
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

---

## 🚀 Deployment Steps

### 1. Prepare Server

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com | bash

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create deployment directory
sudo mkdir -p /opt/nfm
cd /opt/nfm
```

### 2. Set Up Environment

Create `.env.production`:

```bash
# Application
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Convex
CONVEX_URL=https://your-convex-instance.convex.cloud
CONVEX_DEPLOY_KEY=your-deploy-key

# Database
DB_PASSWORD=secure-password-here
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=nfm_production
POSTGRES_USER=nfm_user

# Redis
REDIS_PASSWORD=secure-redis-password

# MediaMTX
MEDIAMTX_API_KEY=secure-api-key

# Hospital Network
HOSPITAL_NETWORK=**********/16
```

### 3. SSL Certificates

```bash
# Using Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ./nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ./nginx/ssl/key.pem
```

### 4. Deploy Application

```bash
# Clone repository
git clone https://github.com/your-org/nfm-system.git .

# Start services
docker-compose -f docker-compose.prod.yml up -d

# Check logs
docker-compose -f docker-compose.prod.yml logs -f

# Verify health
curl https://your-domain.com/api/health
```

---

## 🔒 Security Considerations

### Network Security

1. **Firewall Rules**:
```bash
# Allow only necessary ports
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8554/tcp  # RTSP from hospital network only
sudo ufw enable
```

2. **Network Isolation**:
- Use Docker networks to isolate services
- Restrict RTSP access to hospital network only
- Use VPN for remote access

### Data Security

1. **Encryption**:
- All traffic over HTTPS/WSS
- Database encryption at rest
- Encrypted backups

2. **Access Control**:
- Strong passwords for all services
- Role-based access in application
- API key authentication for MediaMTX

### Monitoring

1. **Health Checks**:
```bash
# Create monitoring script
cat > /opt/nfm/monitor.sh << 'EOF'
#!/bin/bash
# Check all services
docker-compose -f docker-compose.prod.yml ps
# Check disk space
df -h | grep -E "/$|/opt/nfm"
# Check memory
free -h
EOF

chmod +x /opt/nfm/monitor.sh
```

2. **Alerts**:
- Set up Prometheus/Grafana for metrics
- Configure alerts for service failures
- Monitor disk space for recordings

---

## 🔧 Maintenance

### Regular Tasks

1. **Backup Schedule**:
```bash
# Automated daily backups at 2 AM
# Configured in docker-compose.prod.yml
```

2. **Log Rotation**:
```bash
# Add to /etc/logrotate.d/nfm
/opt/nfm/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    notifempty
    create 0640 root root
    sharedscripts
    postrotate
        docker exec nfm-nginx nginx -s reload
    endscript
}
```

3. **Updates**:
```bash
# Update containers
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

### Troubleshooting

1. **Service Issues**:
```bash
# Check service logs
docker logs nfm-mediamtx
docker logs nfm-nextjs

# Restart service
docker-compose -f docker-compose.prod.yml restart mediamtx
```

2. **Performance**:
```bash
# Check resource usage
docker stats

# Clean up old recordings
find /opt/nfm/mediamtx/recordings -type f -mtime +90 -delete
```

---

## 📊 Scaling Considerations

### Horizontal Scaling

For multiple operating rooms:

1. **Load Balancer**:
- Deploy HAProxy or use cloud load balancer
- Distribute WebRTC connections
- Session affinity for WebSocket connections

2. **Multiple MediaMTX Instances**:
- One instance per 2-3 operating rooms
- Shared storage for recordings
- Centralized configuration management

### Storage Scaling

1. **Network Attached Storage**:
- Mount NFS/CIFS for recordings
- Implement storage quotas
- Regular archival to cold storage

2. **Database Scaling**:
- PostgreSQL streaming replication
- Read replicas for reporting
- Connection pooling with PgBouncer

---

## 🆘 Disaster Recovery

### Backup Strategy

1. **Database**: Daily automated backups
2. **Recordings**: Replicated to secondary storage
3. **Configuration**: Version controlled in Git
4. **Certificates**: Backed up securely

### Recovery Procedure

1. **Service Failure**:
```bash
# Quick recovery
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d
```

2. **Full System Recovery**:
- Restore from latest backup
- Verify database integrity
- Reconnect RTSP streams
- Test all functionality

---

**Production Deployment Version**: 1.0  
**Last Updated**: May 2025  
**Tested With**: Docker 24.0, MediaMTX 1.6.0, Next.js 15
