import React from 'react'
import { cn } from '@/lib/utils'
import { generateTimeMarkers, type UseTimelineScaleReturn } from '@/hooks/useTimelineScale'

export interface TimelineRulerProps {
  timeline: UseTimelineScaleReturn
  className?: string
  onTimeClick?: (time: number) => void
}

export function TimelineRuler({ timeline, className, onTimeClick }: TimelineRulerProps) {
  const {
    scale,
    duration,
    scrollLeft,
    viewportWidth,
    pixelsPerSecond,
    getTimeAtPosition
  } = timeline

  // Calculate visible time range
  const startTime = scrollLeft / pixelsPerSecond
  const endTime = (scrollLeft + viewportWidth) / pixelsPerSecond

  // Generate grid markers for visible area
  const markers = generateTimeMarkers(startTime, endTime, pixelsPerSecond, scale)

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!onTimeClick) return
    
    const rect = event.currentTarget.getBoundingClientRect()
    const clickX = event.clientX - rect.left
    const time = getTimeAtPosition(clickX)
    onTimeClick(Math.max(0, Math.min(time, duration)))
  }

  return (
    <div
      className={cn(
        "relative h-12 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 overflow-hidden cursor-pointer select-none",
        className
      )}
      onClick={handleClick}
    >
      {/* Grid lines and labels */}
      <div className="relative h-full">
        {markers.map((marker) => {
          const position = marker.position - scrollLeft
          
          // Only render markers that are visible
          if (position < -50 || position > viewportWidth + 50) {
            return null
          }

          return (
            <div
              key={marker.time}
              className="absolute top-0 flex flex-col items-center"
              style={{ left: position }}
            >
              {/* Grid line */}
              <div
                className={cn(
                  "w-px bg-gray-300 dark:bg-gray-600",
                  marker.isMajor
                    ? "h-full opacity-60"
                    : "h-2/3 opacity-30 mt-auto"
                )}
              />
              
              {/* Time label for major markers */}
              {marker.isMajor && marker.label && (
                <div className="absolute top-1 text-xs text-gray-600 dark:text-gray-300 font-mono -translate-x-1/2 whitespace-nowrap">
                  {marker.label}
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* Scale indicator */}
      <div className="absolute bottom-1 right-2 text-xs text-gray-500 dark:text-gray-400 font-mono">
        {scale}s/100px
      </div>
    </div>
  )
}