import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Patients } from "./schema";
import { omit, pick } from "convex-helpers";

// Type-safe field selectors
const patientCreateFields = Patients.withoutSystemFields;
const patientSearchFields = pick(patientCreateFields, ["firstName", "lastName", "medicalRecordNumber"]);
const patientUpdateFields = omit(patientCreateFields, ["createdBy"]);

// Query to get all patients with pagination
export const getPatients = query({
  args: {
    search: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  returns: v.array(Patients.doc),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const query = ctx.db.query("patients");
    
    // Simple search implementation - in production you'd want better text search
    if (args.search) {
      const searchTerm = args.search.toLowerCase();
      const allPatients = await query.collect();
      const filteredPatients = allPatients.filter(patient => 
        `${patient.firstName} ${patient.lastName}`.toLowerCase().includes(searchTerm) ||
        patient.medicalRecordNumber.toLowerCase().includes(searchTerm)
      );
      return filteredPatients.slice(0, args.limit ?? 50);
    }
    
    const patients = await query
      .order("desc")
      .take(args.limit ?? 50);

    return patients;
  },
});

// Query to get a single patient with related projects
export const getPatientWithProjects = query({
  args: { patientId: v.id("patients") },
  returns: v.union(
    v.object({
      ...Patients.doc.fields,
      projects: v.array(v.any()), // Projects will be enriched separately
    }),
    v.null()
  ),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const patient = await ctx.db.get(args.patientId);
    if (!patient) {
      return null;
    }

    // Get related projects for this patient
    const projects = await ctx.db
      .query("projects")
      .withIndex("by_patient", (q) => q.eq("patientId", args.patientId))
      .order("desc")
      .collect();

    return {
      ...patient,
      projects,
    };
  },
});

// Create a new patient
export const createPatient = mutation({
  args: patientCreateFields,
  returns: v.id("patients"),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Check if MRN already exists
    const existingPatient = await ctx.db
      .query("patients")
      .withIndex("by_mrn", (q) => q.eq("medicalRecordNumber", args.medicalRecordNumber))
      .first();

    if (existingPatient) {
      throw new Error("A patient with this medical record number already exists");
    }

    const patientId = await ctx.db.insert("patients", {
      ...args,
      createdBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return patientId;
  },
});

// Update patient information
export const updatePatient = mutation({
  args: {
    patientId: v.id("patients"),
    ...patientUpdateFields,
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const { patientId, ...updateData } = args;
    
    const patient = await ctx.db.get(patientId);
    if (!patient) {
      throw new Error("Patient not found");
    }

    await ctx.db.patch(patientId, {
      ...updateData,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});
