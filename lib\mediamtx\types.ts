/**
 * MediaMTX API Types and Interfaces
 * Complete type definitions for MediaMTX configuration and runtime data
 */

/**
 * Complete PathConfig interface based on MediaMTX configuration schema
 * Contains all possible configuration options for a MediaMTX path
 * All properties are optional to allow partial updates
 */
export interface PathConfig {
  // Basic identification and source
  name: string; // name is always required
  source?: string;
  sourceFingerprint?: string;
  
  // Source behavior settings
  sourceOnDemand?: boolean;
  sourceOnDemandStartTimeout?: string;
  sourceOnDemandCloseAfter?: string;
  maxReaders?: number;
  srtReadPassphrase?: string;
  fallback?: string;
  useAbsoluteTimestamp?: boolean;
  
  // Recording settings
  record?: boolean;
  recordPath?: string;
  recordFormat?: string;
  recordPartDuration?: string;
  recordSegmentDuration?: string;
  recordDeleteAfter?: string;
  
  // Publishing settings
  overridePublisher?: boolean;
  srtPublishPassphrase?: string;
  
  // RTSP settings
  rtspTransport?: string;
  rtspAnyPort?: boolean;
  rtspRangeType?: string;
  rtspRangeStart?: string;
  sourceRedirect?: string;  
  // Raspberry Pi Camera settings
  rpiCameraCamID?: number;
  rpiCameraSecondary?: boolean;
  rpiCameraWidth?: number;
  rpiCameraHeight?: number;
  rpiCameraHFlip?: boolean;
  rpiCameraVFlip?: boolean;
  rpiCameraBrightness?: number;
  rpiCameraContrast?: number;
  rpiCameraSaturation?: number;
  rpiCameraSharpness?: number;
  rpiCameraExposure?: string;
  rpiCameraAWB?: string;
  rpiCameraAWBGains?: [number, number];
  rpiCameraDenoise?: string;
  rpiCameraShutter?: number;
  rpiCameraMetering?: string;
  rpiCameraGain?: number;
  rpiCameraEV?: number;
  rpiCameraROI?: string;
  rpiCameraHDR?: boolean;
  rpiCameraTuningFile?: string;
  rpiCameraMode?: string;
  rpiCameraFPS?: number;
  rpiCameraAfMode?: string;
  rpiCameraAfRange?: string;
  rpiCameraAfSpeed?: string;
  rpiCameraLensPosition?: number;
  rpiCameraAfWindow?: string;
  rpiCameraFlickerPeriod?: number;
  rpiCameraTextOverlayEnable?: boolean;
  rpiCameraTextOverlay?: string;
  rpiCameraCodec?: string;
  rpiCameraIDRPeriod?: number;
  rpiCameraBitrate?: number;
  rpiCameraProfile?: string;
  rpiCameraLevel?: string;
  rpiCameraJPEGQuality?: number;  
  // Runtime hooks
  runOnInit?: string;
  runOnInitRestart?: boolean;
  runOnDemand?: string;
  runOnDemandRestart?: boolean;
  runOnDemandStartTimeout?: string;
  runOnDemandCloseAfter?: string;
  runOnUnDemand?: string;
  runOnReady?: string;
  runOnReadyRestart?: boolean;
  runOnNotReady?: string;
  runOnRead?: string;
  runOnReadRestart?: boolean;
  runOnUnread?: string;
  runOnRecordSegmentCreate?: string;
  runOnRecordSegmentComplete?: string;
}

/**
 * Type for creating basic path configurations
 * Only requires name and source, with common defaults
 */
export type PathConfigCreate = Pick<PathConfig, 'name' | 'source'> & 
  Partial<Omit<PathConfig, 'name' | 'source'>>;

/**
 * Type for updating path configurations
 * Allows partial updates of any field except name
 */
export type PathConfigUpdate = Pick<PathConfig, 'name'> & 
  Partial<Omit<PathConfig, 'name'>>;

/**
 * Runtime path information from MediaMTX
 */
export interface PathInfo {
  name: string;
  confName: string;
  source?: {
    type: string;
    id: string;
  };
  ready: boolean;
  readyTime: string | null;
  tracks: string[];
  bytesReceived: number;
  bytesSent: number;
  readers: Array<{
    type: string;
    id: string;
  }>;
}
/**
 * Response structure for path listing
 */
export interface PathsListResponse {
  itemCount: number;
  pageCount: number;
  items: PathInfo[];
}

/**
 * Response structure for configuration path listing
 */
export interface ConfigPathsListResponse {
  itemCount: number;
  pageCount: number;
  items: PathConfig[];
}

/**
 * API response wrapper for operations
 */
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data?: T;
}

/**
 * Test connection result
 */
export interface TestConnectionResult {
  success: boolean;
  message: string;
}

/**
 * Import/sync operation result
 */
export interface SyncResult {
  success: boolean;
  imported?: number;
  updated?: number;
  added?: number;
  removed?: number;
  errors?: string[];
  message: string;
}