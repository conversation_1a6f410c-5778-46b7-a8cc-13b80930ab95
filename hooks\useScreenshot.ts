"use client";

import { useCallback, useRef } from "react";

interface UseScreenshotOptions {
  streamPath?: string;
  onScreenshot?: (timestamp: number, blob: Blob) => void;
}

export function useScreenshot({ streamPath, onScreenshot }: UseScreenshotOptions = {}) {
  const canvasRef = useRef<HTMLCanvasElement | null>(null);

  const captureScreenshot = useCallback((
    videoElement: HTMLVideoElement,
    filename?: string
  ): Promise<Blob | null> => {
    return new Promise((resolve) => {
      if (!videoElement || videoElement.videoWidth === 0) {
        resolve(null);
        return;
      }

      // Create or reuse canvas
      let canvas = canvasRef.current;
      if (!canvas) {
        canvas = document.createElement("canvas");
        canvasRef.current = canvas;
      }

      const ctx = canvas.getContext("2d");
      if (!ctx) {
        resolve(null);
        return;
      }

      // Set canvas dimensions to match video
      canvas.width = videoElement.videoWidth;
      canvas.height = videoElement.videoHeight;

      // Draw current video frame
      ctx.drawImage(videoElement, 0, 0);

      // Convert to blob
      canvas.toBlob((blob) => {
        if (blob) {
          const timestamp = Date.now();
          
          // Trigger download if filename provided
          if (filename) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = filename;
            a.click();
            URL.revokeObjectURL(url);
          }

          onScreenshot?.(timestamp, blob);
        }
        resolve(blob);
      }, "image/png");
    });
  }, [onScreenshot]);

  const captureAndDownload = useCallback((
    videoElement: HTMLVideoElement,
    customFilename?: string
  ) => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
    const filename = customFilename || 
      `nfm-screenshot-${streamPath || "stream"}-${timestamp}.png`;
    
    return captureScreenshot(videoElement, filename);
  }, [captureScreenshot, streamPath]);

  return {
    captureScreenshot,
    captureAndDownload
  };
}
