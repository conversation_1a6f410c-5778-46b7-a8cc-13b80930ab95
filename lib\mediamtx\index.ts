/**
 * MediaMTX Library
 * Centralized exports for MediaMTX types and API functions
 */

// Export types
export type {
  PathConfig,
  PathConfigCreate,
  PathConfigUpdate,
  PathInfo,
  PathsListResponse,
  ConfigPathsListResponse,
  ApiResponse,
  TestConnectionResult,
  SyncResult
} from './types';

// Export API functions
export {
  listPaths,
  listConfigPaths,
  addPath,
  updatePath,
  deletePath,
  testStreamConnection,
  getServerHealth,
  getServerMetrics,
  getPathInfo
} from './api';

// Export client (for WebRTC connections)
export { MediaMTXClient, mediaMTXClient } from './mediamtx-client';