/* NFM Timeline Modality Effects Styling */

/* Base action styling */
.timeline-editor-action {
  position: relative;
  border-radius: 4px;
  transition: all 0.2s ease;
}

/* Modality-specific effect styling using effectId system */
/* These classes are automatically applied based on effectId: modality-{modalityId} */

/* Default modality styling - will be overridden by specific modality colors */
.timeline-editor-action-effect-modality {
  background-color: var(--modality-color, #6b7280);
  border: 1px solid var(--modality-color-dark, #4b5563);
  color: white;
}

/* Dynamic modality color application */
/* The effectId system will create classes like: .timeline-editor-action-effect-modality-{modalityId} */
/* These will be dynamically styled using CSS custom properties */

/* Hover and selection states */
.timeline-editor-action:hover {
  filter: brightness(1.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.timeline-editor-action.action-selected {
  box-shadow: 0 0 0 2px #3b82f6;
  z-index: 10;
}

/* Resize handles styling */
.timeline-editor-action-left-stretch,
.timeline-editor-action-right-stretch {
  position: absolute;
  top: 0;
  width: 8px;
  height: 100%;
  cursor: ew-resize;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.timeline-editor-action:hover .timeline-editor-action-left-stretch,
.timeline-editor-action:hover .timeline-editor-action-right-stretch {
  opacity: 1;
}

.timeline-editor-action-left-stretch {
  left: -4px;
  background: linear-gradient(to right, rgba(255,255,255,0.3), transparent);
}

.timeline-editor-action-right-stretch {
  right: -4px;
  background: linear-gradient(to left, rgba(255,255,255,0.3), transparent);
}

/* Diamond shape for short events */
.timeline-action-diamond {
  width: 20px !important;
  height: 20px !important;
  transform: rotate(45deg);
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 50% !important;
  transform-origin: center;
  margin-top: -10px;
}

.timeline-action-diamond .timeline-action-content {
  transform: rotate(-45deg);
  font-size: 10px;
  font-weight: bold;
  line-height: 1;
}

/* Severity-based styling for diamond events */
.timeline-action-diamond.timeline-action-critical {
  animation: pulse-critical 1s infinite;
}

.timeline-action-diamond.timeline-action-alarm {
  animation: pulse-alarm 2s infinite;
}

.timeline-action-diamond.timeline-action-warning {
  filter: brightness(1.2);
}

/* Animations */
@keyframes pulse-critical {
  0%, 100% { 
    transform: rotate(45deg) scale(1);
    filter: brightness(1);
  }
  50% { 
    transform: rotate(45deg) scale(1.1);
    filter: brightness(1.3);
  }
}

@keyframes pulse-alarm {
  0%, 100% { 
    opacity: 1;
  }
  50% { 
    opacity: 0.7;
  }
}

/* Text content styling */
.timeline-editor-action .action-content {
  padding: 2px 6px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  height: 100%;
}

.timeline-editor-action .action-icon {
  margin-right: 4px;
  flex-shrink: 0;
}

.timeline-editor-action .action-title {
  flex: 1;
  min-width: 0;
}

/* Disabled state */
.timeline-editor-action.action-disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* Focus state for keyboard navigation */
.timeline-editor-action:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 1px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .timeline-editor-action {
    border-width: 2px;
  }
  
  .timeline-editor-action:hover {
    filter: none;
    border-color: white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .timeline-editor-action,
  .timeline-editor-action-left-stretch,
  .timeline-editor-action-right-stretch {
    transition: none;
  }
  
  .timeline-action-diamond.timeline-action-critical,
  .timeline-action-diamond.timeline-action-alarm {
    animation: none;
  }
}
