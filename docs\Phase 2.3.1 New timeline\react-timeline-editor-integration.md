# React Timeline Editor Integration Guide

## Overview

The react-timeline-editor is a powerful React-based timeline editing component designed for building animation editors, video editors, and other timeline-based applications. It has been cloned into `components/timeline/` and will replace our current timeline implementation in `components/timeline_old/`.

## Key Features

### 🛠 Core Capabilities
- **Drag & Drop**: Full support for dragging and resizing timeline actions
- **Zoom & Scale**: Multiple zoom levels with smooth scaling
- **Grid Snapping**: Automatic snapping to grid lines for precise positioning
- **Auxiliary Line Snapping**: Smart snapping to other timeline elements
- **Auto-scroll**: Automatic scrolling during playback and editing
- **Infinite Timeline**: Dynamically expanding timeline based on content
- **Custom Styling**: Highly customizable appearance and behavior

### 📡 Decoupled Architecture
- **Timeline Component**: Main editing interface
- **Timeline Engine**: Standalone runtime engine that can operate independently
- **Effect System**: Pluggable effect system with lifecycle callbacks

## Data Structures

### TimelineRow
Represents a single row/track in the timeline (extends NFM ModalityConfig):
```typescript
interface TimelineRow extends Omit<Doc<"modalityConfigs">, '_id'> {
  id: Id<"modalityConfigs">;     // Timeline editor expects 'id' instead of '_id'
  actions: TimelineEvent[];      // Array of events in this modality row
  rowHeight?: number;           // Custom row height (optional)
  selected?: boolean;           // Whether row is selected
  classNames?: string[];        // Additional CSS classes
  isVisible?: boolean;          // Additional display properties
}
```

### TimelineEvent
Represents an individual event on the timeline (extends NFM MonitoringEvent):
```typescript
interface TimelineEvent extends Omit<Doc<"monitoringEvents">, '_id'> {
  id: Id<"monitoringEvents">;  // Timeline editor expects 'id' instead of '_id'
  start: number;               // Timeline editor expects 'start' instead of 'startTime'
  end: number;                 // Timeline editor expects 'end' instead of 'endTime'
  effectId: string;            // Effect ID for timeline rendering
  selected?: boolean;          // Whether event is selected
  flexible?: boolean;          // Whether event can be resized (default: true)
  movable?: boolean;           // Whether event can be moved (default: true)
  disable?: boolean;           // Whether event is disabled (default: false)
  minStart?: number;           // Minimum start time constraint
  maxEnd?: number;             // Maximum end time constraint
  modalityName?: string;       // Additional display properties
  modalityColor?: string;
}
```

### TimelineEffect
Defines the behavior and appearance of actions:
```typescript
interface TimelineEffect {
  id: string;                    // Unique effect identifier
  name?: string;                 // Display name
  source?: TimeLineEffectSource; // Runtime behavior definition
}

interface TimeLineEffectSource {
  start?: (param: EffectSourceParam) => void;   // Called when playback starts in action range
  enter?: (param: EffectSourceParam) => void;   // Called when entering action range
  update?: (param: EffectSourceParam) => void;  // Called every frame during playback
  leave?: (param: EffectSourceParam) => void;   // Called when leaving action range
  stop?: (param: EffectSourceParam) => void;    // Called when playback stops in action range
}
```

## Timeline Component API

### Basic Usage
```typescript
import { Timeline } from '@/components/timeline';

<Timeline
  editorData={timelineRows}
  effects={effectDefinitions}
  onChange={handleDataChange}
  hideCursor={false}
  autoScroll={true}
/>
```

### Key Props
- `editorData: TimelineRow[]` - Timeline data structure
- `effects: Record<string, TimelineEffect>` - Effect definitions
- `onChange: (data: TimelineRow[]) => void` - Data change callback
- `hideCursor?: boolean` - Hide/show playback cursor
- `autoScroll?: boolean` - Enable auto-scroll during playback
- `scale?: number` - Timeline scale factor
- `scaleWidth?: number` - Width of scale units
- `startLeft?: number` - Left offset for timeline start
- `getActionRender?: (action, row) => ReactNode` - Custom action renderer

## Timeline Engine API

### Standalone Engine Usage
```typescript
import { TimelineEngine } from '@/components/timeline';

const engine = new TimelineEngine();
engine.effects = effectDefinitions;
engine.data = timelineData;

// Playback control
engine.play({ autoEnd: true });
engine.pause();
engine.setTime(timeInSeconds);
engine.setPlayRate(1.0);

// Event listeners
engine.on('play', ({ engine }) => { /* playback started */ });
engine.on('paused', ({ engine }) => { /* playback paused */ });
engine.on('setTimeByTick', ({ time, engine }) => { /* time updated by tick */ });
```

### Engine Events
- `play` - Playback started
- `paused` - Playback paused
- `ended` - Playback completed
- `setTimeByTick` - Time updated by engine tick
- `beforeSetTime` - Before manual time change (can be cancelled)
- `afterSetTime` - After manual time change
- `beforeSetPlayRate` - Before playback rate change
- `afterSetPlayRate` - After playback rate change

## Integration with NFM Project

### Current Architecture
- **Old Timeline**: `components/timeline_old/` (to be replaced)
- **New Timeline**: `components/timeline/` (react-timeline-editor clone)
- **Data Source**: ProjectContext provides events and modalities
- **Integration Point**: `app/dashboard/live-monitoring/page.tsx`

### Data Mapping Strategy
Direct integration approach - NFM data structures extend timeline editor interfaces:

```typescript
// NFM -> Timeline Editor mapping (Direct Integration)
ModalityConfig -> TimelineRow (extends ModalityConfig)
MonitoringEvent -> TimelineEvent (extends MonitoringEvent)
EventType/Modality -> TimelineEffect
```

### Key Integration Points
1. **Direct Data Usage**: Timeline components work directly with NFM data structures
2. **Field Mapping**: Map NFM field names to timeline editor expectations (e.g., _id → id, startTime → start)
3. **Effect System**: Define effects for different event types and modalities
4. **Styling**: Apply NFM design system to timeline components
5. **Event Handling**: Direct integration with NFM event handlers
6. **Playback Sync**: Synchronize with video playback system

## Styling Integration

### Theme Integration
The timeline editor supports custom styling through:
- CSS classes and custom renderers
- Theme-aware color schemes
- Responsive design patterns
- Integration with shadcn/ui components

### Design System Alignment
- Use NFM color palette for modality tracks
- Apply consistent spacing and typography
- Maintain accessibility standards
- Support dark/light theme switching

## Performance Considerations

### Optimization Strategies
- Virtualization for large datasets
- Efficient re-rendering with React.memo
- Debounced updates for real-time changes
- Lazy loading of timeline segments

### Memory Management
- Proper cleanup of engine listeners
- Efficient data structures for large timelines
- Garbage collection of unused effects

## Migration Strategy

### Phase 1: Direct Integration Setup ✅ COMPLETED
1. ✅ Updated timeline interfaces to extend NFM types directly (`components/timeline/interface/action.ts`)
2. ✅ Renamed TimelineAction → TimelineEvent for better semantic fit
3. ✅ Build timeline wrapper component (`components/timeline/NFMTimelineEditor.tsx`)
4. ✅ Implement core effect definitions (`components/timeline/effects/modalityEffects.ts`)
5. ✅ Create custom event renderers (`components/timeline/effects/eventRenderers.tsx`)
6. ✅ Add timeline controls component (`components/timeline/NFMTimelineControls.tsx`)
7. ✅ Create React hook for direct data management (`hooks/useTimelineData.ts`)
8. ✅ Add comprehensive type definitions (`types/timelineEditor.ts`)
9. ✅ Integrate CSS styling (`components/timeline/timeline-nfm.css`)

### Phase 2: Direct Integration Migration ✅ COMPLETED
1. ✅ Removed conversion layer utilities (eliminated performance overhead)
2. ✅ Updated all timeline components to work with NFM data directly
3. ✅ Fixed field mapping (id ↔ _id, start ↔ startTime, end ↔ endTime)
4. ✅ Replace timeline usage in dashboard (`app/dashboard/live-monitoring/page.tsx`)
5. ✅ Implement modality visibility controls
6. ✅ Add keyboard shortcuts and navigation
7. ✅ Integrate with video playback system
8. ✅ Maintain existing API contracts

### Phase 3: Enhanced Features 🚧 READY FOR IMPLEMENTATION
1. ✅ Advanced editing capabilities (drag & drop, resize) - **WORKING**
2. 🔄 Context menu for timeline actions
3. 🔄 Timeline export/import functionality
4. 🔄 Performance optimizations for large datasets
5. 🔄 Real-time collaboration features

## Implementation Status

### ✅ Completed Components

#### Core Infrastructure
- **Direct Integration Layer** (No conversion overhead!)
  - Timeline interfaces directly extend NFM Convex types
  - Field mapping handled at interface level (id ↔ _id, start ↔ startTime, end ↔ endTime)
  - Zero data transformation overhead
  - Native TypeScript support for NFM data structures

- **Timeline Data Hook** (`hooks/useTimelineData.ts`)
  - Manages timeline state with direct NFM data integration
  - Provides undo/redo functionality
  - Handles auto-save and change tracking
  - Includes comprehensive validation
  - Zero conversion overhead

- **Type System** (`types/timelineEditor.ts`)
  - Complete type definitions for NFM timeline integration
  - Extends base timeline types with NFM-specific properties
  - Provides type safety for all timeline operations

#### Timeline Components
- **NFMTimelineEditor** (`components/timeline/NFMTimelineEditor.tsx`)
  - Main wrapper component integrating react-timeline-editor
  - Handles NFM-specific configuration and styling
  - Provides keyboard shortcuts and interaction handling
  - Supports real-time validation and error display

- **NFMTimelineControls** (`components/timeline/NFMTimelineControls.tsx`)
  - Comprehensive control panel for timeline operations
  - Includes playback, zoom, navigation, and modality controls
  - Responsive design with compact variant
  - Integrates with shadcn/ui components

#### Effects System
- **Modality Effects** (`components/timeline/effects/modalityEffects.ts`)
  - Effect definitions for different modalities (EEG, EMG, ECG, etc.)
  - Lifecycle callbacks for start, enter, update, leave, stop
  - Priority-based effect management
  - Validation and error handling

- **Event Renderers** (`components/timeline/effects/eventRenderers.tsx`)
  - Custom React components for rendering different event types
  - Severity-based styling (critical, alarm, warning, info)
  - Modality-specific renderers with appropriate icons
  - Responsive and accessible design

#### Styling Integration
- **CSS Styles** (`components/timeline/timeline-nfm.css`)
  - Complete styling for timeline animations and effects
  - Dark mode support
  - Responsive design patterns
  - Performance-optimized animations

### 🔄 Integration Points

#### Dashboard Integration
- **Live Monitoring Page** (`app/dashboard/live-monitoring/page.tsx`)
  - Replaced old timeline with new NFMTimelineEditor
  - Maintains all existing functionality
  - Enhanced with new features like keyboard shortcuts
  - Improved user experience with better controls

#### Data Flow
```
ProjectContext → useTimelineData → NFMTimelineEditor
     ↓                ↓                    ↓
  Events &      Direct Data         Timeline Display
 Modalities    Processing          & Interaction
              (No Conversion!)
```

### 🎯 Key Features Implemented

1. **Drag & Drop Editing**: Full support for moving and resizing timeline events
2. **Multi-Modal Support**: Different renderers for EEG, EMG, ECG, Video, Audio
3. **Real-Time Sync**: Bidirectional synchronization with video playback
4. **Keyboard Shortcuts**: Arrow keys, delete, escape, ctrl+a for selection
5. **Visual Feedback**: Hover effects, selection indicators, progress bars
6. **Error Handling**: Comprehensive validation with user-friendly error messages
7. **Performance**: Optimized rendering with minimal re-renders
8. **Accessibility**: ARIA labels, keyboard navigation, screen reader support

### 📊 Data Mapping

| NFM Type | Timeline Editor Type | Purpose |
|----------|---------------------|---------|
| `ModalityConfig` | `TimelineRow` | Represents modality tracks (direct extension) |
| `MonitoringEvent` | `TimelineEvent` | Represents individual events (direct extension) |
| `EventType/Modality` | `TimelineEffect` | Defines event behavior and rendering |

### 🎨 Styling Features

- **Theme Integration**: Seamless integration with NFM design system
- **Dark Mode**: Full support for light/dark theme switching
- **Animations**: Smooth transitions and hover effects
- **Responsive**: Works on desktop, tablet, and mobile devices
- **Customizable**: Easy to modify colors, spacing, and behavior

## Testing Strategy

### Unit Tests (Recommended)
- Data transformation functions
- Effect lifecycle callbacks
- Engine state management
- Component rendering

### Integration Tests (Recommended)
- Timeline-video synchronization
- Real-time data updates
- User interaction flows
- Performance benchmarks

### End-to-End Tests (Recommended)
- Complete editing workflows
- Multi-user collaboration scenarios
- Data persistence and recovery
- Cross-browser compatibility
