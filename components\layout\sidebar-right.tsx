"use client"

import { LiveMonitoringContent } from "@/components/right-sidebar-content/live-monitoring-content"
import { useIsMobile } from "@/hooks/use-mobile"
import { Sheet, SheetContent } from "../ui/sheet"

interface SidebarRightProps {
  currentPage: string
  isOpen: boolean
  onOpenChange: (open: boolean) => void
}

const pagesWithRightSidebar = ["live-monitoring"]
export function SidebarRight({ currentPage, isOpen, onOpenChange }: SidebarRightProps) {
  const isMobile = useIsMobile()
  // Don't show sidebar if current page doesn't have content
  if (!pagesWithRightSidebar.includes(currentPage.toLowerCase())) {
    return null
  }

  const renderContent = () => {
    switch (currentPage.toLowerCase()) {
      case "live-monitoring":
        return <LiveMonitoringContent isCollapsed={!isOpen} />
      default:
        return null
    }
  }

    // Mobile overlay using Sheet
  if (isMobile) {
    return (
      <Sheet open={isOpen} onOpenChange={onOpenChange}>
        <SheetContent side="right" className="w-80 bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden">
          <div className="flex h-full w-full flex-col">{renderContent()}</div>
        </SheetContent>
      </Sheet>
    )
  }

  return (
    <div className={`sticky top-0 h-svh transition-all duration-200 ease-linear ${isOpen ? "w-80" : "w-16"  }`}>
      <div
        className={`h-full ${isOpen ? "w-80" : "w-16" } border-l bg-sidebar transition-all duration-200 ease-linear`}
      >
        <div className="flex h-full w-full flex-col">{renderContent()}</div>
      </div>
    </div>
  )
}
