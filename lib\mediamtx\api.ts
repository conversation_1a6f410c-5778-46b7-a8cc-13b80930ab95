/**
 * MediaMTX API Client
 * Handles all API calls to the MediaMTX server
 */

import { 
  PathConfigCreate,
  PathConfigUpdate,
  PathsListResponse, 
  ConfigPathsListResponse, 
  TestConnectionResult,
  PathInfo
} from './types';

/**
 * Get authentication header for MediaMTX API calls
 */
const getAuth = (): string => {
  const username = process.env.MEDIAMTX_USERNAME || 'admin';
  const password = process.env.MEDIAMTX_PASSWORD || 'nfm_dev_2025';
  return `Basic ${btoa(`${username}:${password}`)}`;
};

/**
 * Fetch with authentication header
 */
const fetchWithAuth = async (url: string, options: RequestInit = {}): Promise<Response> => {
  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': getAuth(),
    },
  });
};

/**
 * Get MediaMTX API URL from environment
 */
const getApiUrl = (): string => {
  return process.env.MEDIAMTX_API_URL || 'http://localhost:9997';
};
/**
 * List all active paths from MediaMTX
 */
export const listPaths = async (): Promise<PathsListResponse> => {
  const apiUrl = getApiUrl();
  const response = await fetchWithAuth(`${apiUrl}/v3/paths/list`);
  
  if (!response.ok) {
    throw new Error(`Failed to list paths: ${response.statusText}`);
  }
  
  return response.json();
};

/**
 * List all configured paths from MediaMTX
 */
export const listConfigPaths = async (): Promise<ConfigPathsListResponse> => {
  const apiUrl = getApiUrl();
  const response = await fetchWithAuth(`${apiUrl}/v3/config/paths/list`);
  
  if (!response.ok) {
    throw new Error(`Failed to list config paths: ${response.statusText}`);
  }
  
  return response.json();
};

/**
 * Add a new path configuration to MediaMTX
 */
export const addPath = async (pathName: string, config: PathConfigCreate): Promise<void> => {
  const apiUrl = getApiUrl();
  const response = await fetchWithAuth(`${apiUrl}/v3/config/paths/add/${pathName}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(config),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to add path: ${response.statusText} - ${errorText}`);
  }
};
/**
 * Update/patch an existing path configuration in MediaMTX
 */
export const updatePath = async (pathName: string, config: PathConfigUpdate): Promise<void> => {
  const apiUrl = getApiUrl();
  const response = await fetchWithAuth(`${apiUrl}/v3/config/paths/patch/${pathName}`, {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(config),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Failed to update path: ${response.statusText} - ${errorText}`);
  }
};

/**
 * Delete a path configuration from MediaMTX
 */
export const deletePath = async (pathName: string): Promise<void> => {
  const apiUrl = getApiUrl();
  const response = await fetchWithAuth(`${apiUrl}/v3/config/paths/delete/${pathName}`, {
    method: 'DELETE',
  });

  if (!response.ok && response.status !== 404) {
    const errorText = await response.text();
    throw new Error(`Failed to delete path: ${response.statusText} - ${errorText}`);
  }
};

/**
 * Test a stream connection by creating a temporary path
 */
export const testStreamConnection = async (sourceUrl: string): Promise<TestConnectionResult> => {
  const testPath = `test_conn_${Date.now()}`;
  
  try {
    // Create test path configuration
    const testConfig: PathConfigCreate = {
      name: testPath,
      source: sourceUrl,
      sourceOnDemand: false,
      rtspTransport: "tcp",
    };

    await addPath(testPath, testConfig);

    // Wait for connection
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check if connected
    const pathsList = await listPaths();
    const testStreamInfo = pathsList.items?.find(item => item.name === testPath);
    const isConnected = testStreamInfo?.ready === true;

    // Clean up
    await deletePath(testPath);

    return {
      success: isConnected,
      message: isConnected ? "Connection successful" : "Failed to connect to stream"
    };
  } catch (error) {
    // Try to clean up even if test failed
    try {
      await deletePath(testPath);
    } catch (cleanupError) {
      console.warn("Failed to cleanup test path:", cleanupError);
    }
    
    return {
      success: false,
      message: `Connection test failed: ${error instanceof Error ? error.message : error}`
    };
  }
};
/**
 * Get server health status
 */
export const getServerHealth = async (): Promise<boolean> => {
  const apiUrl = getApiUrl();
  try {
    const response = await fetchWithAuth(`${apiUrl}/v3/config/get`);
    return response.ok;
  } catch {
    return false;
  }
};

/**
 * Get server metrics (if metrics server is available)
 */
export const getServerMetrics = async (): Promise<string> => {
  const apiUrl = getApiUrl().replace('9997', '9998'); // Default metrics port
  const response = await fetch(`${apiUrl}/metrics`);
  
  if (!response.ok) {
    throw new Error('Failed to get server metrics');
  }
  
  return response.text();
};

/**
 * Get specific path information
 */
export const getPathInfo = async (pathName: string): Promise<PathInfo> => {
  const apiUrl = getApiUrl();
  const response = await fetchWithAuth(`${apiUrl}/v3/paths/get/${pathName}`);
  
  if (!response.ok) {
    throw new Error(`Failed to get path info for ${pathName}: ${response.statusText}`);
  }
  
  return response.json();
};