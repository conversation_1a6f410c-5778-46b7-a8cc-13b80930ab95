# Deployment & Setup Guide
## NFM System Installation & Configuration

### 🚀 Production Deployment Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                Hospital Network (**********/16)            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌──────────────────┐              │
│  │   NFM Server    │    │  MediaMTX Stream │              │
│  │  (Docker Host)  │◄──►│     Server       │              │
│  │                 │    │                  │              │
│  │ ├─ Next.js App  │    │ ├─ RTSP Input    │              │
│  │ ├─ Convex       │    │ ├─ WebRTC Output │              │
│  │ ├─ PostgreSQL   │    │ ├─ Recording     │              │
│  │ └─ MinIO        │    │ └─ Transcoding   │              │
│  └─────────────────┘    └──────────────────┘              │
│           │                       │                        │
│           │              ┌────────▼────────┐              │
│           │              │ Inomed System   │              │
│           │              │*************    │              │
│           │              │RTSP Stream      │              │
│           │              └─────────────────┘              │
│           │                                               │
│  ┌────────▼─────────────────────────────────────────────┐ │
│  │              Client Devices                          │ │
│  │ ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐  │ │
│  │ │Neurophysio. │ │OR Tablet    │ │Admin Workstation│  │ │
│  │ │Workstation  │ │(Surgeon)    │ │                 │  │ │
│  │ └─────────────┘ └─────────────┘ └─────────────────┘  │ │
│  └──────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

### 📦 Prerequisites & System Requirements

**Server Hardware:**
- **CPU**: Intel Xeon or AMD EPYC, 8+ cores
- **RAM**: 16GB minimum, 32GB recommended
- **Storage**: 500GB SSD minimum (for video recordings)
- **Network**: Gigabit Ethernet, redundant connections preferred
- **OS**: Ubuntu 22.04 LTS or Red Hat Enterprise Linux 8+

**Network Requirements:**
- **Bandwidth**: 10 Mbps per concurrent stream minimum
- **Latency**: <1ms within hospital network
- **Ports**: 3000 (NFM App), 8554 (RTSP), 8889/8890 (WebRTC), 5432 (PostgreSQL)
- **Security**: VLAN isolation, firewall rules configured

**Client Requirements:**
- **Browser**: Chrome 100+, Firefox 100+, Safari 15+, Edge 100+
- **WebRTC Support**: Required for video streaming
- **Screen Resolution**: 1920x1080 minimum, 4K recommended for review stations
- **Input Devices**: Mouse/trackpad for timeline interaction

---

### 🐳 Docker Deployment Setup

**1. Create Project Directory:**
```bash
mkdir /opt/nfm-system
cd /opt/nfm-system
```

**2. Docker Compose Configuration:**
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  # NFM Application
  nfm-app:
    image: nfm-system:latest
    container_name: nfm-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - CONVEX_DEPLOYMENT=production
      - DATABASE_URL=postgresql://nfm_user:${DB_PASSWORD}@postgres:5432/nfm_system
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - MEDIAMTX_URL=http://mediamtx:9997
    depends_on:
      - postgres
      - mediamtx
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    restart: unless-stopped
    networks:
      - nfm-network

  # Database
  postgres:
    image: postgres:15-alpine
    container_name: nfm-postgres
    environment:
      POSTGRES_DB: nfm_system
      POSTGRES_USER: nfm_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--auth-host=md5"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
      - ./init-scripts:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - nfm-network
    command: >
      postgres 
      -c shared_preload_libraries=pg_stat_statements
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB

  # Video Streaming Server
  mediamtx:
    image: bluenviron/mediamtx:latest
    container_name: nfm-mediamtx
    ports:
      - "8554:8554"    # RTSP
      - "8889:8889"    # WebRTC
      - "8890:8890"    # WebRTC WHEP
      - "9997:9997"    # API
    volumes:
      - ./mediamtx.yml:/mediamtx.yml
      - mediamtx_recordings:/recordings
    restart: unless-stopped
    networks:
      - nfm-network

  # Object Storage (for files/videos)
  minio:
    image: minio/minio:latest
    container_name: nfm-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: ${MINIO_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_PASSWORD}
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    restart: unless-stopped
    networks:
      - nfm-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: nfm-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      - nfm-app
    restart: unless-stopped
    networks:
      - nfm-network

  # Monitoring & Backup
  backup:
    image: postgres:15-alpine
    container_name: nfm-backup
    environment:
      PGPASSWORD: ${DB_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./backup-scripts:/scripts
    command: >
      sh -c "
        while true; do
          sleep 24h
          pg_dump -h postgres -U nfm_user -d nfm_system > /backups/backup_$(date +%Y%m%d_%H%M%S).sql
          find /backups -name '*.sql' -mtime +7 -delete
        done
      "
    depends_on:
      - postgres
    restart: unless-stopped
    networks:
      - nfm-network

networks:
  nfm-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
  minio_data:
  mediamtx_recordings:
  app_uploads:
  app_logs:
  nginx_logs:
```

**3. Environment Configuration:**
```bash
# .env.production
DB_PASSWORD=your_secure_database_password
NEXTAUTH_SECRET=your_nextauth_secret_key
MINIO_USER=admin
MINIO_PASSWORD=your_minio_password

# Convex Configuration
CONVEX_DEPLOYMENT=prod:your-deployment-name
CONVEX_SITE_URL=https://your-hospital-domain.com

# MediaMTX Configuration
MEDIAMTX_API_KEY=your_api_key
INOMED_RTSP_URL=rtsp://*************:8554

# Hospital-specific Configuration
HOSPITAL_NAME="Your Hospital Name"
HOSPITAL_NETWORK=**********/16
OR_ROOMS="OR1,OR2,OR3,OR4,OR5,OR6,OR7,OR8,OR9,OR10,OR11,OR12"
```

---

### ⚙️ MediaMTX Configuration

```yaml
# mediamtx.yml
# General settings
logLevel: info
logDestinations: [file]
logFile: /var/log/mediamtx/mediamtx.log

# Performance settings
readTimeout: 10s
writeTimeout: 10s
readBufferCount: 512

# API settings
api: yes
apiAddress: :9997

# Metrics for monitoring
metrics: yes
metricsAddress: :9998

# WebRTC settings
webrtc: yes
webrtcAddress: :8889
webrtcEncryption: no
webrtcAllowOrigin: "*"
webrtcTrustedProxies: ["**********/16"]
webrtcICEServers:
  - url: stun:stun.l.google.com:19302

# HLS settings for fallback
hls: yes
hlsAddress: :8888
hlsEncryption: no
hlsAllowOrigin: "*"
hlsSegmentCount: 3
hlsSegmentDuration: 1s

# Path settings for streams
paths:
  # Main Inomed high-quality stream
  inomed_main:
    source: rtsp://*************:8554/main/av
    sourceProtocol: automatic
    record: yes
    recordPath: /recordings/inomed_main_%Y-%m-%d_%H-%M-%S.mp4
    recordFormat: mp4
    recordPartDuration: 10m
    recordSegmentDuration: 10m
    
  # Inomed low-quality backup stream
  inomed_sub:
    source: rtsp://*************:8554/sub/av
    sourceProtocol: automatic
    
  # Dynamic microscope stream
  microscope:
    runOnInit: echo "Microscope stream initialized"
    runOnInitRestart: yes
    
  # Additional OR cameras
  or_camera_~^[0-9]+$:
    # Regex pattern for OR camera streams (or_camera_1, or_camera_2, etc.)
    runOnInit: echo "OR Camera stream $MTX_PATH initialized"
```

---

### 🔧 Initial Setup & Configuration

**1. Clone and Build Application:**
```bash
git clone https://github.com/your-org/nfm-system.git
cd nfm-system
docker build -t nfm-system:latest .
```

**2. Initialize Database:**
```bash
# Create database initialization script
cat > init-scripts/01-init.sql << EOF
-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create application user
CREATE USER nfm_app WITH PASSWORD 'app_password';
GRANT CONNECT ON DATABASE nfm_system TO nfm_app;
GRANT USAGE ON SCHEMA public TO nfm_app;
GRANT CREATE ON SCHEMA public TO nfm_app;

-- Performance settings
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
EOF
```

**3. SSL Configuration (for production):**
```bash
# Generate SSL certificates
mkdir ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/nfm.key -out ssl/nfm.crt \
  -subj "/CN=nfm.hospital.local"
```

**4. Start Services:**
```bash
# Set environment variables
source .env.production

# Start all services
docker-compose -f docker-compose.prod.yml up -d

# Verify services are running
docker-compose ps
docker logs nfm-app
docker logs nfm-mediamtx
```

**5. Initial System Configuration:**
```bash
# Access the application
open https://your-hospital-domain.com

# Create admin user (first login)
# Configure modalities (EMG, MEP, SSEP, etc.)
# Set up OR rooms and equipment
# Configure user roles and permissions
```
