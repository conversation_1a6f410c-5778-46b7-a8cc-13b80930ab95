# Detailed Implementation Guide
## NeuroFysiology Monitoring System

### 🏗️ Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend       │    │   Streaming     │
│   (Next.js)     │◄──►│   (Convex)       │◄──►│   (MediaMTX)    │
│                 │    │                  │    │                 │
│ ├─ Dashboard    │    │ ├─ Database      │    │ ├─ RTSP Input   │
│ ├─ Timeline     │    │ ├─ File Storage  │    │ ├─ WebRTC Out   │
│ ├─ Video Player │    │ ├─ Real-time     │    │ ├─ Recording    │
│ └─ Reports      │    │ └─ API Layer     │    │ └─ Stream Mgmt  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
          │                       │                       │
          └───────────────────────┼───────────────────────┘
                                  │
                    ┌─────────────▼─────────────┐
                    │     Hospital Network      │
                    │   (172.16.0.0/16)        │
                    │                           │
                    │ ├─ Inomed System         │
                    │ ├─ OR Workstations       │
                    │ ├─ Mobile Devices        │
                    │ └─ Admin Workstations    │
                    └───────────────────────────┘
```

---

## 📦 Phase 1: Project Setup & Core Infrastructure

### 1.1 Environment Setup

**Initialize Next.js Project:**
```bash
npx create-next-app@latest nfm-system --typescript --tailwind --app
cd nfm-system
npm install @convex-dev/convex lucide-react @radix-ui/react-*
npm install react-timeline-editor react-player motion
```

**Convex Setup:**
```bash
npx convex dev
# Configure convex/schema.ts with medical entities
# Set up local PostgreSQL integration
```

**Project Structure:**
```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication routes
│   ├── dashboard/         # Main dashboard
│   ├── projects/          # Project management
│   ├── live/              # Live monitoring
│   └── reports/           # Report generation
├── components/            # Reusable UI components
│   ├── ui/                # ShadCN components
│   ├── timeline/          # Timeline components
│   ├── video/             # Video player components
│   └── forms/             # Form components
├── lib/                   # Utility functions
│   ├── convex/            # Convex client setup
│   ├── utils/             # Helper functions
│   └── types/             # TypeScript types
├── hooks/                 # Custom React hooks
└── contexts/              # React contexts
```

### 1.2 Database Schema Design

**Core Convex Schema:**
```typescript
// convex/schema.ts
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    name: v.string(),
    email: v.string(),
    role: v.union(
      v.literal("surgeon"), 
      v.literal("anesthesiologist"), 
      v.literal("neurophysiologist"),
      v.literal("admin")
    ),
    specialization: v.optional(v.string()),
    credentials: v.array(v.string()),
    isActive: v.boolean(),
  }).index("by_email", ["email"]),

  projects: defineTable({
    patientId: v.id("patients"),
    surgeryType: v.string(),
    operatingRoom: v.string(),
    status: v.union(
      v.literal("scheduled"),
      v.literal("pre-op"),
      v.literal("live"),
      v.literal("post-op"),
      v.literal("completed")
    ),
    teamMembers: v.object({
      surgeon: v.id("users"),
      anesthesiologist: v.id("users"),
      neurophysiologist: v.id("users"),
    }),
    scheduledStart: v.number(),
    actualStart: v.optional(v.number()),
    actualEnd: v.optional(v.number()),
    notes: v.optional(v.string()),
  }).index("by_status", ["status"])
    .index("by_date", ["scheduledStart"]),

  patients: defineTable({
    patientId: v.string(), // Hospital patient ID
    name: v.string(),
    dateOfBirth: v.number(),
    gender: v.union(v.literal("M"), v.literal("F"), v.literal("O")),
    diagnosis: v.string(),
    medicalHistory: v.array(v.string()),
    allergies: v.array(v.string()),
    medications: v.array(v.string()),
  }).index("by_patient_id", ["patientId"]),

  modalityConfigs: defineTable({
    name: v.string(), // EMG, MEP, SSEP, etc.
    displayName: v.string(),
    colorCode: v.string(),
    eventTypes: v.array(v.object({
      name: v.string(),
      severity: v.union(
        v.literal("normal"),
        v.literal("warning"), 
        v.literal("critical")
      ),
      defaultDuration: v.number(),
    })),
    isActive: v.boolean(),
  }),
};
```

  monitoringEvents: defineTable({
    projectId: v.id("projects"),
    sessionId: v.string(),
    timestamp: v.number(), // Milliseconds since session start
    modalityId: v.id("modalityConfigs"),
    eventType: v.string(),
    severity: v.union(
      v.literal("normal"),
      v.literal("warning"),
      v.literal("critical")
    ),
    description: v.string(),
    screenshots: v.array(v.id("_storage")),
    videoClip: v.optional(v.object({
      startTime: v.number(),
      endTime: v.number(),
      storageId: v.id("_storage"),
    })),
    reviewerId: v.optional(v.id("users")),
    reviewed: v.boolean(),
    createdBy: v.id("users"),
  }).index("by_project", ["projectId"])
    .index("by_timestamp", ["timestamp"]),

  clinicalExams: defineTable({
    projectId: v.id("projects"),
    type: v.union(v.literal("pre-op"), v.literal("post-op")),
    cranialNerves: v.array(v.object({
      nerve: v.string(), // "CN I", "CN II", etc.
      status: v.union(
        v.literal("normal"),
        v.literal("abnormal"),
        v.literal("not-tested")
      ),
      notes: v.optional(v.string()),
    })),
    motorFunction: v.array(v.object({
      muscle: v.string(),
      side: v.union(v.literal("left"), v.literal("right")),
      strength: v.number(), // 0-5 MRC scale
      notes: v.optional(v.string()),
    })),
    sensoryFunction: v.array(v.object({
      dermatome: v.string(),
      side: v.union(v.literal("left"), v.literal("right")),
      sensation: v.union(
        v.literal("normal"),
        v.literal("decreased"),
        v.literal("absent"),
        v.literal("hyperesthetic")
      ),
      notes: v.optional(v.string()),
    })),
    examiner: v.id("users"),
    timestamp: v.number(),
  }).index("by_project", ["projectId"]),
});
```

### 1.3 Authentication System

**Convex Auth Setup:**
```typescript
// convex/auth.ts
import { convexAuth } from "@convex-dev/auth/server";
import { Password } from "@convex-dev/auth/providers/Password";

export const { auth, signIn, signOut, store } = convexAuth({
  providers: [Password],
});

// convex/users.ts - User management functions
export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) return null;
    
    return await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", identity.email))
      .unique();
  },
});

export const createUser = mutation({
  args: {
    name: v.string(),
    email: v.string(),
    role: v.union(
      v.literal("surgeon"),
      v.literal("anesthesiologist"),
      v.literal("neurophysiologist"),
      v.literal("admin")
    ),
  },
  handler: async (ctx, args) => {
    // Check if user already exists
    const existing = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();
    
    if (existing) throw new Error("User already exists");
    
    return await ctx.db.insert("users", {
      ...args,
      credentials: [],
      isActive: true,
    });
  },
});
```

---

## 📹 Phase 2: Video Streaming Implementation

### 2.1 MediaMTX Server Setup

**Docker Compose Configuration:**
```yaml
# docker-compose.yml
version: '3.8'
services:
  mediamtx:
    image: bluenviron/mediamtx:latest
    container_name: nfm-streaming
    ports:
      - "8554:8554"    # RTSP
      - "8889:8889"    # WebRTC
      - "8890:8890"    # WebRTC WHEP
    volumes:
      - ./mediamtx.yml:/mediamtx.yml
    restart: unless-stopped
    networks:
      - nfm-network

  postgres:
    image: postgres:15
    container_name: nfm-database
    environment:
      POSTGRES_DB: nfm_system
      POSTGRES_USER: nfm_user
      POSTGRES_PASSWORD: secure_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - nfm-network

networks:
  nfm-network:
    driver: bridge

volumes:
  postgres_data:
```

**MediaMTX Configuration:**
```yaml
# mediamtx.yml
# General settings
logLevel: info
logDestinations: [stdout]
logFile: mediamtx.log

# API settings for management
api: yes
apiAddress: :9997

# WebRTC settings for browser streaming
webrtc: yes
webrtcAddress: :8889
webrtcEncryption: no
webrtcServerKey: server.key
webrtcServerCert: server.crt
webrtcAllowOrigin: "*"

# Path settings for incoming RTSP streams
paths:
  # Main Inomed stream (high quality)
  inomed_main:
    source: rtsp://*************:8554/main/av
    sourceProtocol: automatic
    
  # Inomed sub stream (low quality backup)
  inomed_sub:
    source: rtsp://*************:8554/sub/av
    sourceProtocol: automatic
    
  # Additional video sources (microscope, etc.)
  microscope:
    # Will be configured dynamically
    runOnInit: echo "Microscope stream ready"
```

### 2.2 React Video Player Component

**WebRTC Video Player:**
```typescript
// components/video/WebRTCPlayer.tsx
import React, { useRef, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Play, Pause, Volume2, VolumeX, Maximize } from 'lucide-react';

interface WebRTCPlayerProps {
  streamUrl: string;
  onTimeUpdate?: (currentTime: number) => void;
  onScreenshot?: (dataUrl: string) => void;
  className?: string;
}

export function WebRTCPlayer({ 
  streamUrl, 
  onTimeUpdate, 
  onScreenshot,
  className 
}: WebRTCPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const pcRef = useRef<RTCPeerConnection | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [connectionState, setConnectionState] = useState<RTCPeerConnectionState>('new');

  useEffect(() => {
    initializeWebRTC();
    return () => {
      cleanup();
    };
  }, [streamUrl]);

  const initializeWebRTC = async () => {
    try {
      const pc = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });
      
      pcRef.current = pc;

      pc.ontrack = (event) => {
        if (videoRef.current) {
          videoRef.current.srcObject = event.streams[0];
        }
      };

      pc.oniceconnectionstatechange = () => {
        setConnectionState(pc.iceConnectionState as RTCPeerConnectionState);
      };

      // Create offer for WHEP protocol
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);

      // Send offer to MediaMTX WHEP endpoint
      const response = await fetch(streamUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/sdp',
        },
        body: offer.sdp,
      });

      if (response.ok) {
        const answerSdp = await response.text();
        await pc.setRemoteDescription({
          type: 'answer',
          sdp: answerSdp,
        });
      }
    } catch (error) {
      console.error('WebRTC initialization failed:', error);
    }
  };

  const cleanup = () => {
    if (pcRef.current) {
      pcRef.current.close();
      pcRef.current = null;
    }
  };

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const takeScreenshot = () => {
    if (videoRef.current && onScreenshot) {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      canvas.width = videoRef.current.videoWidth;
      canvas.height = videoRef.current.videoHeight;
      
      ctx?.drawImage(videoRef.current, 0, 0);
      const dataUrl = canvas.toDataURL('image/png');
      onScreenshot(dataUrl);
    }
  };

  const enterFullscreen = () => {
    if (videoRef.current) {
      videoRef.current.requestFullscreen();
    }
  };

  return (
    <div className={`relative group ${className}`}>
      <video
        ref={videoRef}
        className="w-full h-full object-contain bg-black"
        onTimeUpdate={(e) => {
          onTimeUpdate?.(e.currentTarget.currentTime);
        }}
        playsInline
        controls={false}
      />
      
      {/* Video Controls Overlay */}
      <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between bg-black/50 rounded-lg p-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={togglePlay}
            className="text-white hover:text-white"
          >
            {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleMute}
            className="text-white hover:text-white"
          >
            {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <div className={`px-2 py-1 rounded text-xs ${
            connectionState === 'connected' ? 'bg-green-600 text-white' :
            connectionState === 'connecting' ? 'bg-yellow-600 text-white' :
            'bg-red-600 text-white'
          }`}>
            {connectionState}
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={takeScreenshot}
            className="text-white hover:text-white"
          >
            📸
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={enterFullscreen}
            className="text-white hover:text-white"
          >
            <Maximize className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
```
