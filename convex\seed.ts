import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const seedTestProject = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if test project already exists
    const existingTestProject = await ctx.db
      .query("projects")
      .filter((q) => q.eq(q.field("projectCode"), "TEST-001"))
      .first();
    
    if (existingTestProject) {
      return { success: false, message: "Test project TEST-001 already exists" };
    }

    // Get existing modalities (should already be seeded by seedData)
    const modalities = await ctx.db.query("modalityConfigs").collect();
    if (modalities.length === 0) {
      return { success: false, message: "No modalities found. Please run seedData first." };
    }

    // Get first 3 modalities (EMG, MEP, SSEP)
    const modalityIds = modalities.slice(0, 3).map(m => m._id);

    // Create a test user first
    const testUserId = await ctx.db.insert("users", {
      email: "<EMAIL>",
      name: "Test User",
      role: "neurophysiologist",
      isActive: true,
      createdAt: Date.now()
    });

    // Create a test patient
    const testPatientId = await ctx.db.insert("patients", {
      firstName: "John",
      lastName: "Doe",
      medicalRecordNumber: "MRN-12345",
      dateOfBirth: Date.now() - (30 * 365 * 24 * 60 * 60 * 1000), // 30 years old
      gender: "male",
      allergies: "None",
      medications: [],
      medicalHistory: ["Spinal stenosis"],
      createdBy: testUserId,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    // Create a test project
    const testProjectId = await ctx.db.insert("projects", {
      projectCode: "TEST-001",
      patientId: testPatientId,
      surgeryType: "Spinal Fusion",
      operatingRoom: "OR-12",
      status: "in-progress",
      teamMembers: {
        primarySurgeon: testUserId,
        assistingSurgeons: [],
        anesthesiologist: testUserId,
        neurophysiologist: testUserId,
        nurses: [],
        technicians: []
      },
      scheduledStart: Date.now(),
      scheduledDuration: 3600 * 3, // 3 hours
      enabledModalities: modalityIds,
      visibleModalities: modalityIds, // All modalities visible by default
      streamingSources: [],
      complications: [],
      createdBy: testUserId,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    // Create a test session
    const testSessionId = await ctx.db.insert("streamSessions", {
      projectId: testProjectId,
      status: "live",
      streamSources: [],
      startTime: Date.now(),
      viewers: [testUserId],
      eventCount: 0
    });

    // Create some test events
    const testEvents = [
      {
        projectId: testProjectId,
        sessionId: testSessionId,
        timestamp: 900,
        startTime: 900,
        endTime: 930,
        modalityId: modalityIds[1], // MEP (should be the second modality)
        eventType: "MEP Loss",
        severity: "critical" as const,
        title: "MEP Loss - L5 Nerve Root",
        description: "Complete loss of MEP response in left L5 nerve root",
        location: undefined,
        screenshots: [],
        videoClip: undefined,
        reviewerId: undefined,
        reviewStatus: "unreviewed" as const,
        reviewNotes: undefined,
        createdBy: testUserId,
        createdAt: Date.now(),
        updatedAt: Date.now()
      },
      {
        projectId: testProjectId,
        sessionId: testSessionId,
        timestamp: 1200,
        startTime: 1200,
        endTime: 1215,
        modalityId: modalityIds[0], // EMG (should be the first modality)
        eventType: "Burst Activity",
        severity: "warning" as const,
        title: "EMG Burst Activity",
        description: "Increased EMG activity in lumbar paraspinals",
        location: undefined,
        screenshots: [],
        videoClip: undefined,
        reviewerId: undefined,
        reviewStatus: "unreviewed" as const,
        reviewNotes: undefined,
        createdBy: testUserId,
        createdAt: Date.now(),
        updatedAt: Date.now()
      },
      {
        projectId: testProjectId,
        sessionId: testSessionId,
        timestamp: 1800,
        startTime: 1800,
        endTime: undefined,
        modalityId: modalityIds[2], // SSEP (should be the third modality)
        eventType: "Amplitude Decrease",
        severity: "warning" as const,
        title: "SSEP Amplitude Decrease",
        description: "10% decrease in SSEP amplitude",
        location: undefined,
        screenshots: [],
        videoClip: undefined,
        reviewerId: undefined,
        reviewStatus: "unreviewed" as const,
        reviewNotes: undefined,
        createdBy: testUserId,
        createdAt: Date.now(),
        updatedAt: Date.now()
      }
    ];

    const eventIds = [];
    for (const event of testEvents) {
      const id = await ctx.db.insert("monitoringEvents", event);
      eventIds.push(id);
    }

    return {
      success: true,
      message: "Test project created successfully",
      data: {
        userId: testUserId,
        patientId: testPatientId,
        projectId: testProjectId,
        sessionId: testSessionId,
        modalityIds,
        eventIds,
        modalitiesUsed: modalities.slice(0, 3).map(m => ({ name: m.name, color: m.colorCode }))
      }
    };
  },
});

export const getTestProject = query({
  args: {},
  handler: async (ctx) => {
    const project = await ctx.db
      .query("projects")
      .filter((q) => q.eq(q.field("projectCode"), "TEST-001"))
      .first();
    return project || null;
  },
});

export const clearTestData = mutation({
  args: {},
  handler: async (ctx) => {
    // Find and delete test project and related data
    const testProject = await ctx.db
      .query("projects")
      .filter((q) => q.eq(q.field("projectCode"), "TEST-001"))
      .first();
    
    if (!testProject) {
      return { success: false, message: "No test project found" };
    }

    // Delete events
    const events = await ctx.db
      .query("monitoringEvents")
      .withIndex("by_project", (q) => q.eq("projectId", testProject._id))
      .collect();
    
    for (const event of events) {
      await ctx.db.delete(event._id);
    }

    // Delete sessions
    const sessions = await ctx.db
      .query("streamSessions")
      .withIndex("by_project", (q) => q.eq("projectId", testProject._id))
      .collect();
    
    for (const session of sessions) {
      await ctx.db.delete(session._id);
    }

    // Delete patient
    if (testProject.patientId) {
      await ctx.db.delete(testProject.patientId);
    }

    // Delete test user (find by email)
    const testUser = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("email"), "<EMAIL>"))
      .first();
    
    if (testUser) {
      await ctx.db.delete(testUser._id);
    }

    // Delete project
    await ctx.db.delete(testProject._id);

    return {
      success: true,
      message: "Test data cleared successfully",
      deleted: {
        events: events.length,
        sessions: sessions.length,
        project: 1,
        patient: 1,
        user: testUser ? 1 : 0
      }
    };
  },
});
