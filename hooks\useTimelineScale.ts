import { useState, useCallback, useMemo } from 'react'

// Time scale options (seconds per 100px)
export const TIMELINE_SCALES = [15, 30, 60, 120, 300] as const
export type TimelineScale = typeof TIMELINE_SCALES[number]

export interface TimelineScaleState {
  // Current scale (seconds per 100px)
  scale: TimelineScale
  // Total duration in seconds
  duration: number
  // Current time position in seconds
  currentTime: number
  // Viewport width available for timeline
  viewportWidth: number
}

export interface TimelineScaleActions {
  setScale: (scale: TimelineScale) => void
  zoomIn: () => void
  zoomOut: () => void
  fitToView: () => void
  setCurrentTime: (time: number) => void
  setDuration: (duration: number) => void
  setViewportWidth: (width: number) => void
  getTimeAtPosition: (pixelX: number) => number
  getPositionAtTime: (time: number) => number
  scrollToTime: (time: number) => void
}

export interface UseTimelineScaleReturn extends TimelineScaleState, TimelineScaleActions {
  // Calculated values
  totalWidth: number
  pixelsPerSecond: number
  maxScrollLeft: number
  scrollLeft: number
  setScrollLeft: (scroll: number) => void
}

export function useTimelineScale(
  initialDuration: number = 3600, // Default 1 hour
  initialScale: TimelineScale = 60
): UseTimelineScaleReturn {
  const [scale, setScale] = useState<TimelineScale>(initialScale)
  const [duration, setDuration] = useState(initialDuration)
  const [currentTime, setCurrentTime] = useState(0)
  const [viewportWidth, setViewportWidth] = useState(800)
  const [scrollLeft, setScrollLeft] = useState(0)

  // Calculate pixels per second based on scale
  const pixelsPerSecond = useMemo(() => {
    return 100 / scale // scale is seconds per 100px
  }, [scale])

  // Calculate total timeline width in pixels
  const totalWidth = useMemo(() => {
    return duration * pixelsPerSecond
  }, [duration, pixelsPerSecond])

  // Calculate maximum scroll position
  const maxScrollLeft = useMemo(() => {
    return Math.max(0, totalWidth - viewportWidth)
  }, [totalWidth, viewportWidth])

  // Zoom controls with center focus
  const zoomIn = useCallback(() => {
    const currentIndex = TIMELINE_SCALES.indexOf(scale)
    if (currentIndex > 0) {
      const centerTime = (scrollLeft + viewportWidth / 2) / pixelsPerSecond
      setScale(TIMELINE_SCALES[currentIndex - 1])
      
      // Recalculate scroll position to keep center time in center
      setTimeout(() => {
        const newPixelsPerSecond = 100 / TIMELINE_SCALES[currentIndex - 1]
        const newScrollLeft = centerTime * newPixelsPerSecond - viewportWidth / 2
        setScrollLeft(Math.max(0, Math.min(newScrollLeft, (duration * newPixelsPerSecond) - viewportWidth)))
      }, 0)
    }
  }, [scale, scrollLeft, viewportWidth, pixelsPerSecond, duration])

  const zoomOut = useCallback(() => {
    const currentIndex = TIMELINE_SCALES.indexOf(scale)
    if (currentIndex < TIMELINE_SCALES.length - 1) {
      const centerTime = (scrollLeft + viewportWidth / 2) / pixelsPerSecond
      setScale(TIMELINE_SCALES[currentIndex + 1])
      
      // Recalculate scroll position to keep center time in center
      setTimeout(() => {
        const newPixelsPerSecond = 100 / TIMELINE_SCALES[currentIndex + 1]
        const newScrollLeft = centerTime * newPixelsPerSecond - viewportWidth / 2
        setScrollLeft(Math.max(0, Math.min(newScrollLeft, (duration * newPixelsPerSecond) - viewportWidth)))
      }, 0)
    }
  }, [scale, scrollLeft, viewportWidth, pixelsPerSecond, duration])

  const fitToView = useCallback(() => {
    if (duration <= 0 || viewportWidth <= 0) return
    
    // Calculate required scale to fit entire duration in viewport
    const requiredPixelsPerSecond = viewportWidth / duration
    const requiredSecondsPerHundredPx = 100 / requiredPixelsPerSecond
    
    // Find closest scale
    const closestScale = TIMELINE_SCALES.reduce((closest, current) => {
      return Math.abs(current - requiredSecondsPerHundredPx) < Math.abs(closest - requiredSecondsPerHundredPx)
        ? current
        : closest
    })
    
    setScale(closestScale)
    setScrollLeft(0)
  }, [duration, viewportWidth])

  // Position calculations
  const getTimeAtPosition = useCallback((pixelX: number): number => {
    const totalPixelX = pixelX + scrollLeft
    return totalPixelX / pixelsPerSecond
  }, [pixelsPerSecond, scrollLeft])

  const getPositionAtTime = useCallback((time: number): number => {
    const totalPixelX = time * pixelsPerSecond
    return totalPixelX - scrollLeft
  }, [pixelsPerSecond, scrollLeft])

  // Scroll to specific time
  const scrollToTime = useCallback((time: number) => {
    const targetPixelX = time * pixelsPerSecond
    const centeredScrollLeft = targetPixelX - viewportWidth / 2
    const clampedScrollLeft = Math.max(0, Math.min(centeredScrollLeft, maxScrollLeft))
    setScrollLeft(clampedScrollLeft)
  }, [pixelsPerSecond, viewportWidth, maxScrollLeft])

  return {
    // State
    scale,
    duration,
    currentTime,
    viewportWidth,
    scrollLeft,
    
    // Calculated values
    totalWidth,
    pixelsPerSecond,
    maxScrollLeft,
    
    // Actions
    setScale,
    zoomIn,
    zoomOut,
    fitToView,
    setCurrentTime,
    setDuration,
    setViewportWidth,
    setScrollLeft,
    getTimeAtPosition,
    getPositionAtTime,
    scrollToTime,
  }
}

// Utility functions for time formatting
export function formatTimePosition(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`
  }
  return `${secs}s`
}

// Generate time grid markers
export interface TimeMarker {
  time: number
  position: number
  label: string
  isMajor: boolean
}

export function generateTimeMarkers(
  startTime: number,
  endTime: number,
  pixelsPerSecond: number,
  scale: TimelineScale
): TimeMarker[] {
  const markers: TimeMarker[] = []
  
  // Determine grid intervals based on scale
  let majorInterval: number
  let minorInterval: number
  
  if (scale <= 30) {
    majorInterval = 60 // 1 minute
    minorInterval = 15 // 15 seconds
  } else if (scale <= 120) {
    majorInterval = 300 // 5 minutes
    minorInterval = 60 // 1 minute
  } else {
    majorInterval = 600 // 10 minutes
    minorInterval = 300 // 5 minutes
  }
  
  // Generate minor markers
  const startMinor = Math.floor(startTime / minorInterval) * minorInterval
  for (let time = startMinor; time <= endTime; time += minorInterval) {
    if (time >= startTime) {
      const isMajor = time % majorInterval === 0
      markers.push({
        time,
        position: time * pixelsPerSecond,
        label: isMajor ? formatTimePosition(time) : '',
        isMajor
      })
    }
  }
  
  return markers
}