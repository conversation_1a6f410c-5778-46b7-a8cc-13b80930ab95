"use client";

import { MainLayout } from "@/components/layout/main-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { StreamSourceManager } from "@/components/streams/StreamSourceManager";
import { Settings, Video, Users, FileText } from "lucide-react";

export default function SettingsPage() {
  return (
      <div className="flex flex-col gap-6">
        <div className="flex items-center gap-3">
          <Settings className="w-8 h-8" />
          <h1 className="text-3xl font-bold">Settings</h1>
        </div>

        <Tabs defaultValue="streams" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="streams">
              <Video className="w-4 h-4 mr-2" />
              Stream Sources
            </TabsTrigger>
            <TabsTrigger value="users">
              <Users className="w-4 h-4 mr-2" />
              Users
            </TabsTrigger>
            <TabsTrigger value="modalities">
              <FileText className="w-4 h-4 mr-2" />
              Modalities
            </TabsTrigger>
            <TabsTrigger value="system">
              <Settings className="w-4 h-4 mr-2" />
              System
            </TabsTrigger>
          </TabsList>

          <TabsContent value="streams" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Video Stream Sources</CardTitle>
                <CardDescription>
                  Configure RTSP stream sources for operating rooms and medical equipment.
                  These sources will be available for live monitoring during surgeries.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <StreamSourceManager />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>User Management</CardTitle>
                <CardDescription>
                  Manage user accounts and permissions.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">User management coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="modalities" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Modality Configuration</CardTitle>
                <CardDescription>
                  Configure monitoring modalities and event types.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">Modality configuration coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="system" className="mt-6">
            <Card>
              <CardHeader>
                <CardTitle>System Settings</CardTitle>
                <CardDescription>
                  Configure system-wide settings and preferences.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">System settings coming soon...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
  );
}