# Implementation Checklist with LLM Prompts
## NFM System - Step-by-Step Development Guide

### 🎯 How to Use This Checklist

Each phase contains **specific prompts** that can be given to an LLM to complete that implementation step. After completing each major checkpoint, **update the progression log** and consider making a git commit.

**Important**: Due to context window limitations, break work into phases and start new chats when needed.

---

## 📋 Phase 1: Core Infrastructure (Weeks 1-3)

### 1.1 Database Schema & Authentication Setup

**✅ Task**: Implement complete Convex schema and authentication system

**LLM Prompt**:
```
I need you to implement the complete Convex database schema and authentication system for an NFM (NeuroFysiology Monitoring) medical application.

REQUIREMENTS:
- Implement the full schema from backend-specifications.md
- Set up Convex Auth with role-based access control
- Create seed data for modalities and surgery types
- Add proper indexing for performance
- Include data validation and error handling

FILES TO REFERENCE:
- backend-specifications.md (complete schema implementation)
- Original requirements from paste.txt

DELIVERABLES:
1. convex/schema.ts - Complete database schema
2. convex/auth.ts - Authentication setup  
3. convex/users.ts - User management functions
4. convex/seed.ts - Initial data seeding
5. Update progression-log.md with schema decisions

VALIDATION:
- All tables should have proper indexes
- Authentication should work with role-based access
- Seed data should include EMG, MEP, SSEP modalities
- Test user creation and login flow
```

**Checkbox**: [ ] Database schema and authentication complete

---

### 1.2 Basic UI Layout & Navigation

**✅ Task**: Create main application layout with sidebar and navigation

**LLM Prompt**:
```
Create the main application layout and navigation system for the NFM medical monitoring application.

REQUIREMENTS:
- Implement responsive layout from frontend-specifications.md
- Create sidebar with live session status and navigation
- Add header with user profile and hospital branding
- Implement navigation routing between main sections
- Use ShadCN/UI components throughout

FILES TO REFERENCE:
- frontend-specifications.md (Layout Structure section)
- frontend-specifications.md (Sidebar Component section)

DELIVERABLES:
1. app/layout.tsx - Root layout with header
2. components/layout/Sidebar.tsx - Navigation sidebar with live status
3. components/layout/Header.tsx - Top header component
4. components/layout/Navigation.tsx - Navigation menu items
5. app/(dashboard)/layout.tsx - Dashboard layout wrapper

VALIDATION:
- Sidebar shows live session status when available
- Navigation works between Projects, Live View, Settings
- Responsive design works on tablet/desktop
- ShadCN components used for UI elements
```

**Checkbox**: [ ] Basic layout and navigation complete

---

### 1.3 Project Management System

**✅ Task**: Build project overview page with ShadCN table

**LLM Prompt**:
```
Implement the Projects overview page with a comprehensive project management table and creation workflow.

REQUIREMENTS:
- Build ShadCN table exactly as specified in frontend-specifications.md
- Include search, filtering, and sorting capabilities
- Create project creation and editing modals
- Implement project status management
- Add team assignment functionality

FILES TO REFERENCE:
- frontend-specifications.md (Projects Overview Page section)
- backend-specifications.md (Projects schema and queries)

DELIVERABLES:
1. app/projects/page.tsx - Main projects page
2. components/projects/ProjectsTable.tsx - ShadCN table implementation
3. components/projects/ProjectActionModal.tsx - Action selection modal
4. components/projects/CreateProjectModal.tsx - Project creation form
5. convex/projects.ts - Project queries and mutations

VALIDATION:
- Table shows all project columns as specified
- Search works across patient names and surgeons
- Filtering by status, room, date works correctly
- Project creation includes team assignment
- Action modal provides all specified options
```

**Checkbox**: [ ] Project management system complete

---

## 📋 Phase 2: Video Streaming Core (Weeks 4-6)

### 2.1 MediaMTX Server Setup

**✅ Task**: Configure MediaMTX server for RTSP to WebRTC streaming

**LLM Prompt**:
```
Set up MediaMTX streaming server with Docker configuration for the NFM system.

REQUIREMENTS:
- Configure MediaMTX for RTSP input from Inomed system (172.16.25.123:8554)
- Set up WebRTC output for browser streaming
- Create Docker Compose configuration for development
- Add health monitoring and automatic restart
- Configure recording capabilities

FILES TO REFERENCE:
- deployment-guide.md (MediaMTX Configuration section)
- backend-specifications.md (Stream Sessions management)

DELIVERABLES:
1. docker-compose.dev.yml - Development MediaMTX setup
2. mediamtx.yml - MediaMTX configuration file
3. convex/streams.ts - Stream session management
4. Stream health monitoring endpoint
5. Documentation for testing RTSP connections

VALIDATION:
- MediaMTX starts correctly in Docker
- Can accept RTSP streams from test source
- WebRTC output accessible from browser
- Recording functionality works
- Health monitoring responds correctly
```

**Checkbox**: [ ] MediaMTX server setup complete

---

### 2.2 WebRTC Video Player Component

**✅ Task**: Build WebRTC video player with medical-specific features

**LLM Prompt**:
```
Create a WebRTC video player component specifically designed for medical monitoring with screenshot and fullscreen capabilities.

REQUIREMENTS:
- Implement WebRTC player from frontend-specifications.md
- Add screenshot capture at current timestamp
- Include connection status monitoring
- Implement fullscreen and picture-in-picture modes
- Add video controls overlay with medical-specific features

FILES TO REFERENCE:
- frontend-specifications.md (Video Stream Component section)
- detailed-implementation.md (WebRTC Player code example)

DELIVERABLES:
1. components/video/WebRTCPlayer.tsx - Main video player component
2. components/video/VideoControls.tsx - Control overlay
3. hooks/useWebRTC.ts - WebRTC connection management
4. hooks/useScreenshot.ts - Screenshot capture functionality
5. utils/mediaHelpers.ts - Video utility functions

VALIDATION:
- Connects to MediaMTX WebRTC stream
- Screenshot capture works with timestamp
- Fullscreen mode functions correctly
- Connection status updates in real-time
- Video controls appear on hover
```

**Checkbox**: [ ] WebRTC video player complete

---

### 2.3 Basic Timeline Foundation

**✅ Task**: Create timeline component foundation with time navigation

**LLM Prompt**:
```
Build the foundation for the medical timeline component with time scaling and basic event display.

REQUIREMENTS:
- Create timeline container with time ruler
- Implement zoom controls (time scale adjustment)
- Add current time indicator
- Create modality track structure
- Implement basic click-to-seek functionality

FILES TO REFERENCE:
- frontend-specifications.md (Timeline Component section)
- timeline-implementation.md (complete timeline specifications)

DELIVERABLES:
1. components/timeline/TimelineContainer.tsx - Main timeline wrapper
2. components/timeline/TimelineRuler.tsx - Time scale ruler
3. components/timeline/ModalityTrack.tsx - Individual modality tracks
4. components/timeline/CurrentTimeIndicator.tsx - Time position marker
5. hooks/useTimelineScale.ts - Time scaling logic

VALIDATION:
- Timeline scales correctly with zoom controls
- Time ruler shows accurate timestamps
- Current time indicator updates smoothly
- Modality tracks display with correct colors
- Click to seek updates video player time
```

**Checkbox**: [ ] Basic timeline foundation complete

---

## 📋 Phase 3: Event Management System (Weeks 7-10)

### 3.1 Event Creation & Annotation System

**✅ Task**: Build real-time event creation with modality-specific buttons

**LLM Prompt**:
```
Implement the event creation system with configurable buttons and real-time synchronization.

REQUIREMENTS:
- Create event creation buttons based on surgery type configuration
- Implement real-time event synchronization via Convex
- Add event markers on timeline with proper styling
- Create basic event editing capabilities
- Include severity level selection and color coding

FILES TO REFERENCE:
- frontend-specifications.md (Event Creation Buttons section)
- backend-specifications.md (Event Management System)

DELIVERABLES:
1. components/events/EventCreationBar.tsx - Quick action buttons
2. components/timeline/EventMarker.tsx - Timeline event markers
3. components/events/BasicEventForm.tsx - Event editing form
4. convex/events.ts - Event queries and mutations
5. hooks/useEventCreation.ts - Event creation logic

VALIDATION:
- Event buttons appear based on enabled modalities
- Events sync in real-time across all clients
- Event markers appear on timeline immediately
- Color coding matches modality configuration
- Basic event editing works correctly
```

**Checkbox**: [ ] Event creation and annotation complete

---

### 3.2 Advanced Timeline Features

**✅ Task**: Implement advanced timeline interactions and filtering

**LLM Prompt**:
```
Add advanced timeline features including expandable tracks, filtering, and event interactions.

REQUIREMENTS:
- Implement compact vs expanded timeline views
- Add modality filtering and search
- Create event hover tooltips and interactions
- Implement timeline scrolling and navigation
- Add keyboard shortcuts for timeline control

FILES TO REFERENCE:
- frontend-specifications.md (Timeline Track Layout sections)
- frontend-specifications.md (Timeline specifications)

DELIVERABLES:
1. components/timeline/TimelineHeader.tsx - Controls and filters
2. components/timeline/EventTooltip.tsx - Event hover information
3. components/timeline/TimelineNavigation.tsx - Navigation controls
4. hooks/useTimelineFilters.ts - Filtering logic
5. hooks/useKeyboardShortcuts.ts - Keyboard navigation

VALIDATION:
- Expanded view shows individual modality tracks
- Filtering by modality and severity works
- Event tooltips show correct information
- Timeline scrolling smooth and responsive
- Keyboard shortcuts function correctly
```

**Checkbox**: [ ] Advanced timeline features complete

---

### 3.3 Event Review Interface

**✅ Task**: Build comprehensive event review popup with video scrubbing

**LLM Prompt**:
```
Create the event review interface with video clips, screenshots, and collaborative annotation features.

REQUIREMENTS:
- Implement both large popup and sidebar review modes
- Add video player with ±1s/±5s controls and scrubbing
- Create screenshot gallery with timestamp labels
- Build severity assessment and description forms
- Include reviewer assignment and status tracking

FILES TO REFERENCE:
- frontend-specifications.md (Event Review Popup section)
- backend-specifications.md (Event update functions)

DELIVERABLES:
1. components/events/EventReviewModal.tsx - Large popup review interface
2. components/events/EventReviewSidebar.tsx - Compact sidebar version
3. components/events/VideoScrubber.tsx - Video clip review controls
4. components/events/ScreenshotGallery.tsx - Screenshot management
5. convex/files.ts - File upload and screenshot handling

VALIDATION:
- Large popup pauses live stream correctly
- Video scrubbing works with extend controls
- Screenshots can be captured and annotated
- Review status updates in real-time
- Both popup modes function correctly
```

**Checkbox**: [ ] Event review interface complete

---

### 3.4 Event Log Sidebar

**✅ Task**: Create real-time event log with filtering and navigation

**LLM Prompt**:
```
Build the event log sidebar with real-time updates, filtering, and timeline synchronization.

REQUIREMENTS:
- Create scrollable event list with real-time updates
- Implement filtering by modality, severity, and review status
- Add timeline synchronization (hover to highlight)
- Include export and search functionality
- Create event status badges and indicators

FILES TO REFERENCE:
- frontend-specifications.md (Event Log Sidebar section)
- backend-specifications.md (Event queries)

DELIVERABLES:
1. components/events/EventLogSidebar.tsx - Main event log component
2. components/events/EventLogItem.tsx - Individual event display
3. components/events/EventFilters.tsx - Filtering controls
4. hooks/useEventSync.ts - Timeline synchronization
5. utils/eventHelpers.ts - Event formatting utilities

VALIDATION:
- Events appear in real-time as they're created
- Filtering works for all specified criteria
- Hovering events highlights timeline markers
- Export functionality works correctly
- Event status updates reflect review progress
```

**Checkbox**: [ ] Event log sidebar complete

---

## 📋 Phase 4: Clinical Assessment Features (Weeks 11-13)

### 4.1 Patient Information Management

**✅ Task**: Build comprehensive patient detail pages and management

**LLM Prompt**:
```
Implement the patient information system with detailed medical history and current project tracking.

REQUIREMENTS:
- Create patient detail page from frontend-specifications.md
- Build patient search and management interface
- Add medical history and medication tracking
- Include emergency contact and allergy management
- Implement patient privacy and security measures

FILES TO REFERENCE:
- frontend-specifications.md (Patient Detail Page section)
- backend-specifications.md (Patients schema)

DELIVERABLES:
1. app/patients/[id]/page.tsx - Patient detail page
2. components/patients/PatientOverview.tsx - Patient summary card
3. components/patients/MedicalHistory.tsx - Medical history display
4. components/patients/PatientSearch.tsx - Search interface
5. convex/patients.ts - Patient queries and mutations

VALIDATION:
- Patient detail page shows all required information
- Medical history displays chronologically
- Allergies and medications clearly highlighted
- Search works across patient names and IDs
- Privacy controls function correctly
```

**Checkbox**: [ ] Patient information management complete

---

### 4.2 Clinical Examination Interface

**✅ Task**: Build interactive pre/post-op examination forms

**LLM Prompt**:
```
Create the clinical examination interface with interactive assessments for cranial nerves, motor function, and sensory testing.

REQUIREMENTS:
- Build the complete examination form from frontend-specifications.md
- Create interactive cranial nerve assessment with one-click buttons
- Implement MRC scale motor function testing with dropdowns
- Add interactive dermatome body diagram for sensory assessment
- Include reflex testing and overall assessment tools

FILES TO REFERENCE:
- frontend-specifications.md (Clinical Examination Interface section)
- backend-specifications.md (Clinical Data Management)

DELIVERABLES:
1. components/clinical/ClinicalExamForm.tsx - Main examination form
2. components/clinical/CranialNerveAssessment.tsx - CN testing interface
3. components/clinical/MotorFunctionTest.tsx - MRC scale motor testing
4. components/clinical/SensoryAssessment.tsx - Dermatome testing
5. components/clinical/BodyDiagram.tsx - Interactive SVG body diagram

VALIDATION:
- All examination components save data correctly
- MRC scale dropdowns work with color coding
- Body diagram allows dermatome selection
- Auto-calculations for motor scores work
- Examination data persists and loads correctly
```

**Checkbox**: [ ] Clinical examination interface complete

---

### 4.3 Project & Team Management

**✅ Task**: Build surgery scheduling and team assignment system

**LLM Prompt**:
```
Implement the project scheduling and team management system with role assignments and workflow tracking.

REQUIREMENTS:
- Create surgery scheduling interface
- Build team assignment with role validation
- Implement OR room management
- Add workflow status tracking
- Create project templates based on surgery types

FILES TO REFERENCE:
- backend-specifications.md (Projects schema)
- frontend-specifications.md (Settings & Configuration)

DELIVERABLES:
1. components/projects/SchedulingInterface.tsx - Surgery scheduling
2. components/projects/TeamAssignment.tsx - Team member selection
3. components/projects/ORRoomManager.tsx - Operating room management
4. components/projects/ProjectWorkflow.tsx - Status tracking
5. convex/scheduling.ts - Scheduling queries and mutations

VALIDATION:
- Scheduling prevents double-booking
- Team assignments respect role requirements
- OR room availability checks work
- Project status updates correctly
- Surgery templates apply modalities correctly
```

**Checkbox**: [ ] Project and team management complete

---

## 📋 Phase 5: Reporting & Production Features (Weeks 14-16)

### 5.1 Report Generation System

**✅ Task**: Build automated report generation with embedded media

**LLM Prompt**:
```
Create the report generation system that compiles surgical monitoring data into comprehensive reports.

REQUIREMENTS:
- Build report template system with medical formatting
- Include event timeline with screenshots
- Add clinical assessment comparison (pre vs post-op)
- Implement PDF generation with embedded images
- Create report customization and sharing features

FILES TO REFERENCE:
- Original requirements (report generation section)
- All previous components for data integration

DELIVERABLES:
1. components/reports/ReportGenerator.tsx - Main report interface
2. components/reports/ReportTemplate.tsx - Report formatting
3. components/reports/EventSummary.tsx - Event compilation
4. utils/pdfGeneration.ts - PDF creation utilities
5. convex/reports.ts - Report data aggregation

VALIDATION:
- Reports include all event data with timestamps
- Screenshots embedded correctly in PDF
- Clinical assessments compared pre vs post-op
- Report sharing works with team members
- PDF generation handles large datasets
```

**Checkbox**: [ ] Report generation system complete

---

### 5.2 Settings & Configuration Management

**✅ Task**: Build comprehensive settings system for modalities and surgery types

**LLM Prompt**:
```
Implement the settings and configuration system allowing customization of modalities, surgery types, and system preferences.

REQUIREMENTS:
- Create modality configuration interface from frontend-specifications.md
- Build surgery type template management
- Add user preference settings
- Implement system configuration options
- Create backup and export functionality

FILES TO REFERENCE:
- frontend-specifications.md (Settings & Configuration section)
- backend-specifications.md (System Configuration)

DELIVERABLES:
1. app/settings/page.tsx - Main settings page
2. components/settings/ModalityConfig.tsx - Modality management
3. components/settings/SurgeryTemplates.tsx - Surgery type templates
4. components/settings/SystemSettings.tsx - System configuration
5. convex/configuration.ts - Configuration queries and mutations

VALIDATION:
- Modality configuration saves event types correctly
- Surgery templates apply to new projects
- User preferences persist across sessions
- System settings update globally
- Export/import functionality works
```

**Checkbox**: [ ] Settings and configuration complete

---

### 5.3 Performance Optimization & Testing

**✅ Task**: Optimize application performance and implement comprehensive testing

**LLM Prompt**:
```
Optimize the NFM application for production deployment with performance improvements and testing coverage.

REQUIREMENTS:
- Implement lazy loading for large datasets
- Add virtual scrolling for timeline and event lists
- Optimize video streaming performance
- Create comprehensive test suites
- Add error tracking and monitoring

DELIVERABLES:
1. Performance optimization across all components
2. Test suites for critical functionality
3. Error tracking and logging system
4. Performance monitoring dashboard
5. Production deployment checklist

VALIDATION:
- Application loads quickly with large datasets
- Timeline scrolling is smooth with many events
- Video streaming maintains low latency
- Tests cover all critical user workflows
- Error tracking captures issues correctly
```

**Checkbox**: [ ] Performance optimization and testing complete

---

## 🎯 Success Criteria Validation

### Technical Requirements
- [ ] Sub-second video latency (< 0.5s) achieved
- [ ] Real-time event synchronization working across all clients
- [ ] 99.9% uptime during surgery hours
- [ ] Support for 10+ concurrent users
- [ ] Mobile responsive design functional
- [ ] HIPAA compliant data handling implemented

### User Experience Requirements  
- [ ] Intuitive interface requiring minimal training
- [ ] One-click event annotation working
- [ ] Automatic data backup and recovery functional
- [ ] Sub-3-second report generation
- [ ] Seamless multi-device synchronization
- [ ] Comprehensive help and documentation

### Medical Requirements
- [ ] Accurate timestamp synchronization
- [ ] Complete audit trail for all actions
- [ ] Secure patient data handling
- [ ] Integration with Inomed system working
- [ ] Customizable clinical assessment forms
- [ ] Comprehensive reporting capabilities

---

## 📝 Notes for Future Development

**Context Window Management**:
- After completing 2-3 major checkboxes, commit changes and start new chat
- Always reference progression-log.md for completed work
- Provide specific file lists to new LLM sessions
- Include key implementation decisions in progression log

**Quality Control**:
- Test each component thoroughly before marking complete
- Validate real-time functionality across multiple browser tabs
- Check mobile responsiveness on actual devices
- Verify medical data accuracy and compliance
