import { Password } from "@convex-dev/auth/providers/Password";
import { convexAuth, getAuthSessionId, getAuthUserId } from "@convex-dev/auth/server";
import { ConvexError } from "convex/values";
import { mutation, query, QueryCtx } from "./_generated/server";

export const { auth, signIn, signOut, store, isAuthenticated } = convexAuth({
  providers: [Password],
  callbacks: {
    async afterUserCreatedOrUpdated(ctx, args) {
      console.log("User created or updated:", args);
      if (args.existingUserId) return;
     
      // For new users, set their default role to READ
      await ctx.db.patch(args.userId, {
        role: "default", 
        createdAt: Date.now(),
        credentials: [],
        preferences: {
          defaultModalities: [],
          notificationSettings: {
            email: true,
            inApp: true,
            criticalEvents: true,
          },
          uiPreferences: {
            theme: "light",
            timelineView: "expanded",
            defaultTimeScale: 60, // 1 minute per major tick
          },
        },
      });
    },
  }
});

// Helper to get current user with role information
export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (userId === null) {
      return null;
    }
    return await ctx.db.get(userId);
  },
});

export const currentSession = query({
  args: {},
  handler: async (ctx) => {
    const sessionId = await getAuthSessionId(ctx);
    if (sessionId === null) {
      return null;
    }
    return await ctx.db.get(sessionId);
  },
});

// Helper to check if user has required role
export function hasRole(userRole: string, requiredRoles: string[]): boolean {
  // Admin has access to everything
  if (userRole === "admin") return true;
  
  // Check if user has one of the required roles
  return requiredRoles.includes(userRole);
}



// Helper to require authentication and role
export async function requireAuth(ctx: QueryCtx, requiredRoles?: string[]) {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new ConvexError("Not authenticated");
  }
  
  const user = await ctx.db
    .query("users")
    .withIndex("by_email", (q) => q.eq("email", identity.email!))
    .unique();
    
  if (!user) {
    throw new ConvexError("User not found");
  }
  
  if (!user.isActive) {
    throw new ConvexError("User account is deactivated");
  }
  
  if (requiredRoles && !hasRole(user.role!, requiredRoles)) {
    throw new ConvexError("Insufficient permissions");
  }
  
  return user;
}
