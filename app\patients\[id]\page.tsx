"use client"

import { useQuery } from "convex/react"
import { api } from "@/convex/_generated/api"
import { Id } from "@/convex/_generated/dataModel"
import { MainLayout } from "@/components/layout/main-layout"
import { PatientOverview } from "@/components/patients/PatientOverview"
import { MedicalHistory } from "@/components/patients/MedicalHistory"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowLeft,
  Brain,
  Eye,
  Edit,
  FileText,
  Calendar,
  Clock,
  MapPin,
  User
} from "lucide-react"
import Link from "next/link"
import { useParams } from "next/navigation"

export default function PatientDetailPage() {
  const params = useParams()
  const patientId = params.id as Id<"patients">
  
  const patientData = useQuery(api.patients.getPatientWithProjects, { patientId })
  const activeCount = useQuery(api.projects.getActiveProjectsCount)

  // Mock user data - in real app this would come from authentication
  const user = {
    name: "Dr. <PERSON>",
    email: "<EMAIL>", 
    role: "Neurologist",
  }

  const formatDateTime = (timestamp: number) => {
    const date = new Date(timestamp)
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled": return "default"
      case "pre-op": return "secondary"  
      case "in-progress": return "destructive"
      case "post-op": return "outline"
      case "completed": return "secondary"
      case "cancelled": return "outline"
      default: return "default"
    }
  }

  if (patientData === undefined) {
    return (
      <MainLayout user={user} activeSessionsCount={activeCount || 0}>
        <div className="flex items-center justify-center h-96">
          <div className="text-muted-foreground">Loading patient details...</div>
        </div>
      </MainLayout>
    )
  }

  if (patientData === null) {
    return (
      <MainLayout user={user} activeSessionsCount={activeCount || 0}>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">Patient not found</h3>
            <p className="text-muted-foreground mb-4">
              The patient you're looking for doesn't exist or has been removed.
            </p>
            <Link href="/patients">
              <Button>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Patients
              </Button>
            </Link>
          </div>
        </div>
      </MainLayout>
    )
  }

  const { projects, ...patient } = patientData
  const currentProject = projects?.find(p => p.status === "in-progress" || p.status === "scheduled")

  return (
    <MainLayout user={user} activeSessionsCount={activeCount || 0}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/patients">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Patients
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                Patient: {patient.firstName} {patient.lastName} (#{patient.medicalRecordNumber})
              </h1>
              <p className="text-muted-foreground">
                Complete patient information and medical history
              </p>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Patient Overview */}
          <PatientOverview patient={patient} />
          
          {/* Current Project / Quick Actions */}
          <div className="space-y-6">
            {/* Current Project Card */}
            {currentProject ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Brain className="h-5 w-5" />
                    <span>Current Project</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-lg">{currentProject.surgeryType}</h3>
                    <p className="text-muted-foreground">
                      Dr. {currentProject.teamMembers?.primarySurgeon || "Not assigned"}
                    </p>
                  </div>
                  
                  <div className="grid gap-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <span>{currentProject.operatingRoom}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span>{formatDateTime(currentProject.scheduledStart).date}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <span>{formatDateTime(currentProject.scheduledStart).time}</span>
                      <span className="text-muted-foreground">
                        ({Math.round(currentProject.scheduledDuration / 60)} hours est.)
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Badge variant={getStatusColor(currentProject.status)}>
                      {currentProject.status}
                    </Badge>
                  </div>

                  <div className="flex space-x-2">
                    {currentProject.status === "in-progress" && (
                      <Button size="sm">
                        <Eye className="mr-2 h-4 w-4" />
                        View Live
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </Button>
                    <Button variant="outline" size="sm">
                      <FileText className="mr-2 h-4 w-4" />
                      Report
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>No Active Project</CardTitle>
                  <CardDescription>
                    This patient doesn't have any scheduled or in-progress surgeries
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button>
                    <Brain className="mr-2 h-4 w-4" />
                    Create New Project
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="mr-2 h-4 w-4" />
                  New Pre-op Exam
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Brain className="mr-2 h-4 w-4" />
                  View Previous Surgeries
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <FileText className="mr-2 h-4 w-4" />
                  Upload Documents
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <User className="mr-2 h-4 w-4" />
                  Contact Emergency Contact
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Medical History Section */}
        <MedicalHistory 
          patient={patient}
          projects={projects || []}
        />
      </div>
    </MainLayout>
  )
}
