import React, { FC, useEffect, useRef } from 'react';
import { AutoSizer, Grid, GridCellRenderer, OnScrollParams } from 'react-virtualized';
import { CommonProp } from '../../interface/common_prop';
import { prefix } from '../../utils/deal_class_prefix';
import './time_area.css';
import { parserPixelToTime } from '../../utils/deal_data';

/** Animation Timeline Component Parameters */
export type TimeAreaProps = CommonProp & {
  /** Left scroll distance */
  scrollLeft: number;
  /** Scroll callback, used to synchronize scrolling */
  onScroll: (params: OnScrollParams) => void;
  /** Set cursor position */
  setCursor: (param: { left?: number; time?: number }) => void;
};

/** Animation Timeline Component */
export const TimeArea: FC<TimeAreaProps> = ({ setCursor, maxScaleCount, hideCursor, scale, scaleWidth, scaleCount, scaleSplitCount, startLeft, scrollLeft, onClickTimeArea, getScaleRender }) => {
  const gridRef = useRef<Grid>(null);
  const showUnit = scaleSplitCount! > 0;

  /** Get the content of each cell */
  const cellRenderer: GridCellRenderer = ({ columnIndex, key, style }) => {
    const isShowScale = showUnit ? columnIndex % scaleSplitCount! === 0 : true;
    const classNames = ['time-unit'];
    if (isShowScale) classNames.push('time-unit-big');
    const item = (showUnit ? columnIndex / scaleSplitCount! : columnIndex) * scale!;
    return (
      <div key={key} style={style} className={prefix(...classNames)}>
        {isShowScale && <div className={prefix('time-unit-scale')}>{getScaleRender ? getScaleRender(item) : item}</div>}
      </div>
    );
  };

  useEffect(() => {
    gridRef.current?.recomputeGridSize();
  }, [scaleWidth, startLeft]);

  /** Get the width of each column */
  const getColumnWidth = (data: { index: number }) => {
    switch (data.index) {
      case 0:
        return startLeft!;
      default:
        return showUnit ? scaleWidth! / scaleSplitCount! : scaleWidth!;
    }
  };
  const estColumnWidth=getColumnWidth({index:1})!;
  return (
    <div className={prefix('time-area')}>
      <AutoSizer>
        {({ width, height }) => {
          return (
            <>
              <Grid
                ref={gridRef}
                columnCount={showUnit ? scaleCount * scaleSplitCount! + 1 : scaleCount}
                columnWidth={getColumnWidth || -1}
                estimatedColumnSize={estColumnWidth}
                rowCount={1}
                rowHeight={height}
                width={width}
                height={height}
                overscanRowCount={0}
                overscanColumnCount={10}
                cellRenderer={cellRenderer}
                scrollLeft={scrollLeft}
              ></Grid>
              <div
                style={{ width, height }}
                onClick={(e) => {
                  if (hideCursor) return;
                  const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
                  const position = e.clientX - rect.x;
                  const left = Math.max(position + scrollLeft, startLeft!);
                  if (left > maxScaleCount! * scaleWidth! + startLeft! - scrollLeft) return;

                  const time = parserPixelToTime(left, { startLeft: startLeft!, scale: scale!, scaleWidth: scaleWidth! });

                  // Clamp time to valid bounds (0 to maxScaleCount * scale)
                  const maxTime = maxScaleCount! * scale!;
                  const clampedTime = Math.max(0, Math.min(time, maxTime));

                  const result = onClickTimeArea && onClickTimeArea(clampedTime, e);
                  if (result === false) return; // Return false to prevent setting time
                  setCursor({ time: clampedTime });
                }}
                className={prefix('time-area-interact')}
              ></div>
            </>
          );
        }}
      </AutoSizer>
    </div>
  );
};
