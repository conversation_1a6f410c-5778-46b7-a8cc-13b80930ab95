"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { cn } from "@/lib/utils"
import {
  Activity,
  Brain,
  Calendar,
  FileText,
  LayoutDashboard,
  Settings,
  Users,
  Zap,
  Circle,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  Monitor
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useProjectContext } from "@/components/contexts/ProjectContext"

interface SidebarProps {
  activeSessionsCount?: number
  className?: string
}

const navigationItems = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Live Monitoring",
    href: "/dashboard/live-monitoring",
    icon: Monitor,
  },
  {
    title: "Projects",
    href: "/dashboard/projects",
    icon: <PERSON>,
  },
  {
    title: "Patients",
    href: "/dashboard/patients",
    icon: Users,
  },
  {
    title: "Recordings",
    href: "/dashboard/recordings",
    icon: Zap,
  },
  {
    title: "Reports",
    href: "/dashboard/reports",
    icon: FileText,
  },
  {
    title: "Schedule",
    href: "/dashboard/schedule",
    icon: Calendar,
  },
]

const settingsItems = [
  {
    title: "General",
    href: "/dashboard/settings",
    icon: Settings,
  },
  {
    title: "Users",
    href: "/dashboard/settings/users",
    icon: Users,
  },
  {
    title: "System",
    href: "/dashboard/settings/system",
    icon: Brain,
  },
]

export function Sidebar({ activeSessionsCount = 0, className }: SidebarProps) {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [isSessionsVisible, setIsSessionsVisible] = useState(true)

  // Get project context data
  const { currentProject } = useProjectContext()

  return (
    <div className={cn(
      "flex h-full flex-col bg-gradient-to-b from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800",
      "border-r border-slate-200 dark:border-slate-700 shadow-lg transition-all duration-300 ease-in-out",
      isCollapsed ? "w-12" : "w-48", // Reduced from w-16/w-64 to w-12/w-48
      "min-h-screen md:min-h-full", // Ensure full height on mobile
      className
    )}>
      {/* Collapse Toggle - Hidden on mobile since Sheet has close button */}
      <div className="flex items-center justify-between p-4">
        {!isCollapsed && (
          <h2 className="text-lg font-semibold">NFM System</h2>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="hidden md:flex h-8 w-8 p-0"
        >
          {isCollapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      <div className="mx-4 h-px bg-gradient-to-r from-transparent via-slate-300 to-transparent dark:via-slate-600" />

      {/* Live Session Status */}
      {isSessionsVisible && (
        <div className="p-4">
          <div className="rounded-lg border bg-card p-3">
            {!isCollapsed ? (
              <>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Circle className={cn(
                      "h-2 w-2 fill-current",
                      activeSessionsCount > 0 ? "text-green-500" : "text-gray-400"
                    )} />
                    <span className="text-sm font-medium">
                      {activeSessionsCount > 0 ? "Live Sessions" : "No Active Sessions"}
                    </span>
                  </div>
                  {activeSessionsCount > 0 && (
                    <Badge variant="default" className="ml-2">
                      {activeSessionsCount}
                    </Badge>
                  )}
                </div>
                {activeSessionsCount > 0 && (
                  <p className="mt-1 text-xs text-muted-foreground">
                    {activeSessionsCount} patient{activeSessionsCount !== 1 ? 's' : ''} monitored
                  </p>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsSessionsVisible(false)}
                  className="mt-2 h-6 w-full text-xs"
                >
                  Hide
                </Button>
              </>
            ) : (
              <div className="flex justify-center">
                <Circle className={cn(
                  "h-4 w-4 fill-current",
                  activeSessionsCount > 0 ? "text-green-500" : "text-gray-400"
                )} />
                {activeSessionsCount > 0 && (
                  <Badge variant="default" className="ml-1 h-4 text-xs">
                    {activeSessionsCount}
                  </Badge>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {!isSessionsVisible && !isCollapsed && (
        <div className="px-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsSessionsVisible(true)}
            className="w-full text-xs"
          >
            Show Live Sessions
          </Button>
        </div>
      )}

      <Separator />

      {/* Navigation */}
      <nav className="flex-1 space-y-1 p-4">
        {navigationItems.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link key={item.href} href={item.href}>
              <Button
                variant={isActive ? "secondary" : "ghost"}
                className={cn(
                  "w-full",
                  isCollapsed ? "justify-center px-2" : "justify-start",
                  isActive && "bg-secondary"
                )}
                title={isCollapsed ? item.title : undefined}
              >
                <item.icon className={cn("h-4 w-4", !isCollapsed && "mr-2")} />
                {!isCollapsed && item.title}
                {!isCollapsed && item.title === "Live Monitoring" && activeSessionsCount > 0 && (
                  <Badge variant="outline" className="ml-auto">
                    {activeSessionsCount}
                  </Badge>
                )}
              </Button>
            </Link>
          )
        })}

        {/* Settings Section */}
        <Collapsible open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
          <CollapsibleTrigger asChild>
            <Button
              variant="ghost"
              className={cn(
                "w-full",
                isCollapsed ? "justify-center px-2" : "justify-start"
              )}
              title={isCollapsed ? "Settings" : undefined}
            >
              <Settings className={cn("h-4 w-4", !isCollapsed && "mr-2")} />
              {!isCollapsed && (
                <>
                  Settings
                  <ChevronDown className={cn(
                    "ml-auto h-4 w-4 transition-transform duration-200",
                    isSettingsOpen && "rotate-180"
                  )} />
                </>
              )}
            </Button>
          </CollapsibleTrigger>
          {!isCollapsed && (
            <CollapsibleContent className="space-y-1">
              {settingsItems.map((item) => {
                const isActive = pathname === item.href
                return (
                  <Link key={item.href} href={item.href}>
                    <Button
                      variant={isActive ? "secondary" : "ghost"}
                      className={cn(
                        "w-full justify-start pl-8",
                        isActive && "bg-secondary"
                      )}
                    >
                      <item.icon className="mr-2 h-3 w-3" />
                      {item.title}
                    </Button>
                  </Link>
                )
              })}
            </CollapsibleContent>
          )}
        </Collapsible>
      </nav>

      {/* Footer */}
      <div className="p-4">
        <Separator className="mb-4" />
        {!isCollapsed ? (
          <div className="text-xs text-muted-foreground">
            <p>NFM System v1.0</p>
            <p>Neural Function Monitor</p>
            {currentProject && (
              <p className="mt-2 font-medium text-primary">
                Project: {currentProject.projectCode}
              </p>
            )}
          </div>
        ) : (
          <div className="flex justify-center">
            <div className="text-xs text-muted-foreground" title="NFM System v1.0">
              NFM
            </div>
          </div>
        )}
      </div>
    </div>
  )
}