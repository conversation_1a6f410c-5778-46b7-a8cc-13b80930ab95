"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  FileText,
  Calendar,
  User,
  Star,
  Eye,
  Edit,
  Copy,
  Printer,
  Brain,
  Activity,
  ClipboardList,
  TrendingUp
} from "lucide-react"

interface Patient {
  _id: string
  firstName: string
  lastName: string
  medicalRecordNumber: string
  medicalHistory: string[]
  diagnosis?: string
}

interface Project {
  _id: string
  surgeryType: string
  scheduledStart: number
  status: string
  teamMembers?: {
    primarySurgeon?: string
  }
  operatingRoom: string
}

interface MedicalHistoryProps {
  patient: Patient
  projects: Project[]
}

export function MedicalHistory({ patient, projects }: MedicalHistoryProps) {
  const [activeTab, setActiveTab] = useState("all")

  const formatDateTime = (timestamp: number) => {
    const date = new Date(timestamp)
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled": return "default"
      case "pre-op": return "secondary"
      case "in-progress": return "destructive"
      case "post-op": return "outline"
      case "completed": return "secondary"
      default: return "default"
    }
  }

  // Mock examination data - in real app this would come from database
  const mockExaminations = [
    {
      id: "exam-1",
      type: "pre-op",
      date: Date.now() - 86400000, // Yesterday
      examiner: "Dr. Smith",
      isBaseline: true,
      cranialNerves: "CN I-XII: All normal",
      motorFunction: "Right leg: 5/5 all groups, Left leg: 4/5 dorsiflexion",
      sensoryFunction: "L4: Decreased sensation, L5: Normal, S1: Normal",
      reflexes: "Patellar: 2+ bilateral, Achilles: 1+ left, 2+ right",
      notes: "Patient reports increasing pain and weakness over 6 months. Weakness most pronounced in left dorsiflexion. Sensory deficit in L4 distribution consistent with imaging findings.",
      status: "completed",
      findings: ["Motor weakness", "Sensory deficit", "Reflex asymmetry"]
    }
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <ClipboardList className="h-5 w-5" />
          <span>Examination History</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="all" className="flex items-center space-x-1">
              <ClipboardList className="h-4 w-4" />
              <span>All Exams</span>
            </TabsTrigger>
            <TabsTrigger value="pre-op" className="flex items-center space-x-1">
              <FileText className="h-4 w-4" />
              <span>Pre-op</span>
            </TabsTrigger>
            <TabsTrigger value="post-op" className="flex items-center space-x-1">
              <Activity className="h-4 w-4" />
              <span>Post-op</span>
            </TabsTrigger>
            <TabsTrigger value="documents" className="flex items-center space-x-1">
              <FileText className="h-4 w-4" />
              <span>Documents</span>
            </TabsTrigger>
            <TabsTrigger value="trends" className="flex items-center space-x-1">
              <TrendingUp className="h-4 w-4" />
              <span>Trends</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-6 mt-6">
            {/* Projects History */}
            <div>
              <h3 className="font-semibold mb-4">Surgery Projects</h3>
              <div className="space-y-4">
                {projects.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Brain className="h-8 w-8 mx-auto mb-2" />
                    <p>No surgery projects found for this patient</p>
                  </div>
                ) : (
                  projects.map((project) => (
                    <Card key={project._id} className="border-l-4 border-l-primary">
                      <CardContent className="pt-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium">{project.surgeryType}</h4>
                            <p className="text-sm text-muted-foreground">
                              {formatDateTime(project.scheduledStart).date} at {formatDateTime(project.scheduledStart).time}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {project.operatingRoom} • Dr. {project.teamMembers?.primarySurgeon || "Not assigned"}
                            </p>
                          </div>
                          <Badge variant={getStatusColor(project.status)}>
                            {project.status}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </div>

            {/* Clinical Examinations */}
            <div>
              <h3 className="font-semibold mb-4">Clinical Examinations</h3>
              <div className="space-y-4">
                {mockExaminations.map((exam) => (
                  <Card key={exam.id} className="border-l-4 border-l-blue-500">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg flex items-center space-x-2">
                            <span>{exam.type === "pre-op" ? "Pre-operative" : "Post-operative"} Examination</span>
                            {exam.isBaseline && (
                              <Badge variant="outline" className="flex items-center space-x-1">
                                <Star className="h-3 w-3" />
                                <span>Baseline</span>
                              </Badge>
                            )}
                          </CardTitle>
                          <p className="text-sm text-muted-foreground">
                            {formatDateTime(exam.date).date} • Examiner: {exam.examiner}
                          </p>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <h5 className="font-medium mb-2 flex items-center space-x-1">
                            <Brain className="h-4 w-4" />
                            <span>Cranial Nerves:</span>
                          </h5>
                          <p className="text-sm">{exam.cranialNerves}</p>
                          {exam.findings.includes("Motor weakness") && (
                            <Badge variant="destructive" className="mt-1">⚠️ No deficits noted</Badge>
                          )}
                        </div>
                        
                        <div>
                          <h5 className="font-medium mb-2 flex items-center space-x-1">
                            <Activity className="h-4 w-4" />
                            <span>Motor Function:</span>
                          </h5>
                          <p className="text-sm">{exam.motorFunction}</p>
                          {exam.findings.includes("Motor weakness") && (
                            <Badge variant="destructive" className="mt-1">⚠️ Weakness noted</Badge>
                          )}
                        </div>
                        
                        <div>
                          <h5 className="font-medium mb-2">Sensory Function:</h5>
                          <p className="text-sm">{exam.sensoryFunction}</p>
                        </div>
                        
                        <div>
                          <h5 className="font-medium mb-2">Reflexes:</h5>
                          <p className="text-sm">{exam.reflexes}</p>
                          {exam.findings.includes("Reflex asymmetry") && (
                            <Badge variant="destructive" className="mt-1">⚠️ Asymmetric</Badge>
                          )}
                        </div>
                      </div>
                      
                      <div>
                        <h5 className="font-medium mb-2">Notes:</h5>
                        <p className="text-sm bg-muted p-3 rounded-lg">{exam.notes}</p>
                      </div>
                      
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="mr-1 h-3 w-3" />
                          View Details
                        </Button>
                        <Button variant="outline" size="sm">
                          <Printer className="mr-1 h-3 w-3" />
                          Print
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="mr-1 h-3 w-3" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm">
                          <Copy className="mr-1 h-3 w-3" />
                          Copy to New Exam
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="pre-op" className="mt-6">
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-8 w-8 mx-auto mb-2" />
              <p>Pre-operative examinations will be shown here</p>
            </div>
          </TabsContent>

          <TabsContent value="post-op" className="mt-6">
            <div className="text-center py-8 text-muted-foreground">
              <Activity className="h-8 w-8 mx-auto mb-2" />
              <p>Post-operative examinations will be shown here</p>
            </div>
          </TabsContent>

          <TabsContent value="documents" className="mt-6">
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-8 w-8 mx-auto mb-2" />
              <p>Patient documents will be shown here</p>
            </div>
          </TabsContent>

          <TabsContent value="trends" className="mt-6">
            <div className="text-center py-8 text-muted-foreground">
              <TrendingUp className="h-8 w-8 mx-auto mb-2" />
              <p>Examination trends and analytics will be shown here</p>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
