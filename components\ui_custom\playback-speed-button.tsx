"use client"

import type React from "react"

import { useState, useEffect, useRef, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"

const SPEED_VALUES = [0.25, 0.5, 1.0, 1.5, 2.0, 4.0]
const SPEED_LABELS = ["x0.25", "x0.5", "x1.0", "x1.5", "x2.0", "x4.0"]

interface PlaybackSpeedButtonProps {
    className?: string
    startSpeed?: number
    onSpeedChange?: (speed: number) => void
}

export default function PlaybackSpeedButton({
    className = "",
    startSpeed = 1.0,
    onSpeedChange,
}: PlaybackSpeedButtonProps) {
    const [isExpanded, setIsExpanded] = useState(false)
    const [currentSpeed, setCurrentSpeed] = useState(startSpeed)
    const [fillPercentage, setFillPercentage] = useState(() => {
        const speedIndex = SPEED_VALUES.indexOf(startSpeed)
        return speedIndex >= 0
            ? (speedIndex / (SPEED_VALUES.length - 1)) * 100
            : 20 // fallback to x1.0 (20%)
    })
    const [isHolding, setIsHolding] = useState(false)
    const buttonRef = useRef<HTMLButtonElement>(null)
    const timeoutRef = useRef<NodeJS.Timeout>(null)

    const calculateSpeedFromPosition = useCallback((percentage: number) => {
        const clampedPercentage = Math.max(0, Math.min(100, percentage))
        const speedIndex = Math.round((clampedPercentage / 100) * (SPEED_VALUES.length - 1))
        return SPEED_VALUES[speedIndex]
    }, [])

    const calculatePercentageFromSpeed = useCallback((speed: number) => {
        const speedIndex = SPEED_VALUES.indexOf(speed)
        return (speedIndex / (SPEED_VALUES.length - 1)) * 100
    }, [])

    const handleMouseDown = (e: React.MouseEvent) => {
        e.preventDefault()
        setIsHolding(true)
        timeoutRef.current = setTimeout(() => {
            setIsExpanded(true)
        }, 150)
    }

    const handleMouseMove = useCallback(
        (e: MouseEvent) => {
            if (!isExpanded || !buttonRef.current || !isHolding) return

            const rect = buttonRef.current.getBoundingClientRect()
            const x = e.clientX - rect.left
            const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))

            setFillPercentage(percentage)
            const newSpeed = calculateSpeedFromPosition(percentage)
            setCurrentSpeed(newSpeed)
            if (onSpeedChange) onSpeedChange(newSpeed)
        },
        [isExpanded, isHolding, calculateSpeedFromPosition, onSpeedChange],
    )

    const handleMouseUp = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
        }
        setIsHolding(false)
        setIsExpanded(false)

        // Snap to nearest speed value
        const nearestSpeed = calculateSpeedFromPosition(fillPercentage)
        setCurrentSpeed(nearestSpeed)
        setFillPercentage(calculatePercentageFromSpeed(nearestSpeed))
        if (onSpeedChange) onSpeedChange(nearestSpeed)
    }, [fillPercentage, calculateSpeedFromPosition, calculatePercentageFromSpeed, onSpeedChange])

    const handleTouchStart = (e: React.TouchEvent) => {
        e.preventDefault()
        setIsHolding(true)
        timeoutRef.current = setTimeout(() => {
            setIsExpanded(true)
        }, 150)
    }

    const handleTouchMove = useCallback(
        (e: TouchEvent) => {
            if (!isExpanded || !buttonRef.current || !isHolding) return

            const touch = e.touches[0]
            const rect = buttonRef.current.getBoundingClientRect()
            const x = touch.clientX - rect.left
            const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))

            setFillPercentage(percentage)
            const newSpeed = calculateSpeedFromPosition(percentage)
            setCurrentSpeed(newSpeed)
            if (onSpeedChange) onSpeedChange(newSpeed)
        },
        [isExpanded, isHolding, calculateSpeedFromPosition, onSpeedChange],
    )

    useEffect(() => {
        // Only add global listeners when actually holding/interacting
        if (isHolding) {
            if (isExpanded) {
                document.addEventListener("mousemove", handleMouseMove)
                document.addEventListener("touchmove", handleTouchMove)
            }
            document.addEventListener("mouseup", handleMouseUp)
            document.addEventListener("touchend", handleMouseUp)
        }

        return () => {
            document.removeEventListener("mousemove", handleMouseMove)
            document.removeEventListener("touchmove", handleTouchMove)
            document.removeEventListener("mouseup", handleMouseUp)
            document.removeEventListener("touchend", handleMouseUp)
        }
    }, [isExpanded, isHolding, handleMouseMove, handleTouchMove, handleMouseUp])

    useEffect(() => {
        setCurrentSpeed(startSpeed)
        const speedIndex = SPEED_VALUES.indexOf(startSpeed)
        setFillPercentage(
            speedIndex >= 0
                ? (speedIndex / (SPEED_VALUES.length - 1)) * 100
                : 20
        )
    }, [startSpeed])

    const currentSpeedLabel = SPEED_LABELS[SPEED_VALUES.indexOf(currentSpeed)]

    return (
        <div className={`flex items-center justify-center bg-background ${className}`}>
            <motion.div
                layout
                className="relative"
                initial={false}
                animate={{
                    width: isExpanded ? 320 : 70,
                }}
                transition={{
                    type: "spring",
                    stiffness: 400,
                    damping: 35,
                }}
            >
                <Button
                    ref={buttonRef}
                    variant="outline"
                    className={`relative overflow-hidden transition-all duration-300 select-none ${
                        isExpanded
                            ? "w-full h-10 rounded-full border-primary/30 cursor-grab active:cursor-grabbing"
                            : "h-8 px-6 hover:bg-accent cursor-pointer"
                    }`}
                    onMouseDown={handleMouseDown}
                    onTouchStart={handleTouchStart}
                    style={{ userSelect: "none" }}
                >
                    {/* Background fill effect */}
                    <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-primary/20 via-primary/15 to-primary/10 rounded-full"
                        initial={{ scaleX: 0, originX: 0 }}
                        animate={{
                            scaleX: isExpanded ? fillPercentage / 100 : 0,
                            originX: 0,
                        }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 30,
                        }}
                    />

                    {/* Speed markers when expanded */}
                    {isExpanded && (
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 0.4 }}
                            exit={{ opacity: 0 }}
                            className="absolute inset-0 flex items-center justify-between px-8"
                        >
                            {SPEED_VALUES.map((_, index) => (
                                <div key={index} className="w-0.5 h-6 bg-muted-foreground/30 rounded-full" />
                            ))}
                        </motion.div>
                    )}

                    <AnimatePresence mode="wait">
                        {!isExpanded ? (
                            <motion.div
                                key="button-content"
                                initial={{ opacity: 0, scale: 0.9 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0.9 }}
                                transition={{
                                    duration: 0.25,
                                    ease: "easeOut",
                                }}
                                className="relative z-10"
                            >
                                <span className="font-semibold text-foreground">{currentSpeedLabel}</span>
                            </motion.div>
                        ) : (
                            <motion.div
                                key="expanded-content"
                                initial={{ opacity: 0, y: 5 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -5 }}
                                transition={{
                                    duration: 0.3,
                                    delay: 0.1,
                                    ease: "easeOut",
                                }}
                                className="relative z-10 flex items-center justify-between w-full px-6"
                            >
                                <span className="text-sm font-medium text-muted-foreground">Playback Speed</span>
                                <span className="font-bold text-lg text-primary">{currentSpeedLabel}</span>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </Button>

                {/* Enhanced glow effect when expanded */}
                <AnimatePresence>
                    {isExpanded && (
                        <motion.div
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            transition={{ duration: 0.3 }}
                            className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 blur-xl -z-10"
                        />
                    )}
                </AnimatePresence>
            </motion.div>
        </div>
    )
}
