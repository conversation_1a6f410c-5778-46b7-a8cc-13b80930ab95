"use client"

import type React from "react"

import { useState, useEffect, useRef, useCallback } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"

const SPEED_VALUES = [0.25, 0.5, 1.0, 1.5, 2.0, 4.0]
const SPEED_LABELS = ["x0.25", "x0.5", "x1.0", "x1.5", "x2.0", "x4.0"]

interface PlaybackSpeedButtonProps {
    className?: string
    startSpeed?: number
    onSpeedChange?: (speed: number) => void
    showTooltip?: boolean
}

export default function PlaybackSpeedButton({
    className = "",
    startSpeed = 1.0,
    onSpeedChange,
    showTooltip = false,
}: PlaybackSpeedButtonProps) {
    const [isExpanded, setIsExpanded] = useState(false)
    const [currentSpeed, setCurrentSpeed] = useState(startSpeed)
    const [fillPercentage, setFillPercentage] = useState(() => {
        const speedIndex = SPEED_VALUES.indexOf(startSpeed)
        return speedIndex >= 0
            ? (speedIndex / (SPEED_VALUES.length - 1)) * 100
            : 20 // fallback to x1.0 (20%)
    })
    const [isHolding, setIsHolding] = useState(false)

    // Internal state for dragging - only visual, doesn't trigger global updates
    const [dragSpeed, setDragSpeed] = useState(startSpeed)
    const [dragFillPercentage, setDragFillPercentage] = useState(() => {
        const speedIndex = SPEED_VALUES.indexOf(startSpeed)
        return speedIndex >= 0
            ? (speedIndex / (SPEED_VALUES.length - 1)) * 100
            : 20
    })

    const buttonRef = useRef<HTMLButtonElement>(null)
    const timeoutRef = useRef<NodeJS.Timeout>(null)

    const calculateSpeedFromPosition = useCallback((percentage: number) => {
        const clampedPercentage = Math.max(0, Math.min(100, percentage))
        const speedIndex = Math.round((clampedPercentage / 100) * (SPEED_VALUES.length - 1))
        return SPEED_VALUES[speedIndex]
    }, [])

    const calculatePercentageFromSpeed = useCallback((speed: number) => {
        const speedIndex = SPEED_VALUES.indexOf(speed)
        return (speedIndex / (SPEED_VALUES.length - 1)) * 100
    }, [])

    const handleClick = useCallback((e: React.MouseEvent) => {
        e.preventDefault()

        // If we were holding and expanded, don't process as click
        if (isExpanded) return

        // Get current speed index
        const currentIndex = SPEED_VALUES.indexOf(currentSpeed)
        let nextIndex

        if (e.ctrlKey || e.metaKey) {
            // Ctrl+click: go to previous speed
            nextIndex = currentIndex > 0 ? currentIndex - 1 : SPEED_VALUES.length - 1
        } else {
            // Regular click: go to next speed
            nextIndex = currentIndex < SPEED_VALUES.length - 1 ? currentIndex + 1 : 0
        }

        const newSpeed = SPEED_VALUES[nextIndex]
        const newPercentage = calculatePercentageFromSpeed(newSpeed)

        // Update all states
        setCurrentSpeed(newSpeed)
        setFillPercentage(newPercentage)
        setDragSpeed(newSpeed)
        setDragFillPercentage(newPercentage)

        // Trigger global callback
        if (onSpeedChange) onSpeedChange(newSpeed)
    }, [isExpanded, currentSpeed, calculatePercentageFromSpeed, onSpeedChange])

    const handleMouseDown = useCallback((e: React.MouseEvent) => {
        e.preventDefault()
        setIsHolding(true)
        timeoutRef.current = setTimeout(() => {
            setIsExpanded(true)
        }, 150)
    }, [timeoutRef])

    const handleMouseMove = useCallback(
        (e: MouseEvent) => {
            if (!isExpanded || !buttonRef.current || !isHolding) return

            const rect = buttonRef.current.getBoundingClientRect()
            const x = e.clientX - rect.left
            const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))

            // Update internal drag state only - no global state updates during drag
            setDragFillPercentage(percentage)
            const newSpeed = calculateSpeedFromPosition(percentage)
            setDragSpeed(newSpeed)
        },
        [isExpanded, isHolding, calculateSpeedFromPosition],
    )

    const handleMouseUp = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
        }

        const wasExpanded = isExpanded
        setIsHolding(false)
        setIsExpanded(false)

        // Only update global state if we were dragging (expanded)
        if (wasExpanded) {
            // Snap to nearest speed value after drag
            const finalPercentage = dragFillPercentage
            const nearestSpeed = calculateSpeedFromPosition(finalPercentage)
            const snappedPercentage = calculatePercentageFromSpeed(nearestSpeed)

            // Update both internal and global state to final values
            setCurrentSpeed(nearestSpeed)
            setFillPercentage(snappedPercentage)
            setDragSpeed(nearestSpeed)
            setDragFillPercentage(snappedPercentage)

            // Only trigger global callback after drag
            if (onSpeedChange) onSpeedChange(nearestSpeed)
        }
        // If not expanded, it was just a click - handleClick will handle it
    }, [isExpanded, dragFillPercentage, calculateSpeedFromPosition, calculatePercentageFromSpeed, onSpeedChange])

    const handleTouchStart = useCallback((e: React.TouchEvent) => {
        e.preventDefault()
        setIsHolding(true)
        timeoutRef.current = setTimeout(() => {
            setIsExpanded(true)
        }, 150)
    }, [])

    const handleTouchEnd = useCallback(() => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
        }

        const wasExpanded = isExpanded
        const wasHolding = isHolding
        setIsHolding(false)
        setIsExpanded(false)

        // If we weren't expanded, treat as a tap (like click)
        if (!wasExpanded && wasHolding) {
            // Get current speed index
            const currentIndex = SPEED_VALUES.indexOf(currentSpeed)
            // For touch, just go to next (no Ctrl key support)
            const nextIndex = currentIndex < SPEED_VALUES.length - 1 ? currentIndex + 1 : 0

            const newSpeed = SPEED_VALUES[nextIndex]
            const newPercentage = calculatePercentageFromSpeed(newSpeed)

            // Update all states
            setCurrentSpeed(newSpeed)
            setFillPercentage(newPercentage)
            setDragSpeed(newSpeed)
            setDragFillPercentage(newPercentage)

            // Trigger global callback
            if (onSpeedChange) onSpeedChange(newSpeed)
        } else if (wasExpanded) {
            // Handle drag end
            const finalPercentage = dragFillPercentage
            const nearestSpeed = calculateSpeedFromPosition(finalPercentage)
            const snappedPercentage = calculatePercentageFromSpeed(nearestSpeed)

            // Update both internal and global state to final values
            setCurrentSpeed(nearestSpeed)
            setFillPercentage(snappedPercentage)
            setDragSpeed(nearestSpeed)
            setDragFillPercentage(snappedPercentage)

            // Only trigger global callback after drag
            if (onSpeedChange) onSpeedChange(nearestSpeed)
        }
    }, [isExpanded, isHolding, currentSpeed, dragFillPercentage, calculatePercentageFromSpeed, calculateSpeedFromPosition, onSpeedChange])

    const handleTouchMove = useCallback(
        (e: TouchEvent) => {
            if (!isExpanded || !buttonRef.current || !isHolding) return

            const touch = e.touches[0]
            const rect = buttonRef.current.getBoundingClientRect()
            const x = touch.clientX - rect.left
            const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100))

            // Update internal drag state only - no global state updates during drag
            setDragFillPercentage(percentage)
            const newSpeed = calculateSpeedFromPosition(percentage)
            setDragSpeed(newSpeed)
        },
        [isExpanded, isHolding, calculateSpeedFromPosition],
    )

    useEffect(() => {
        // Only add global listeners when actually holding/interacting
        if (isHolding) {
            if (isExpanded) {
                document.addEventListener("mousemove", handleMouseMove)
                document.addEventListener("touchmove", handleTouchMove)
            }
            document.addEventListener("mouseup", handleMouseUp)
            document.addEventListener("touchend", handleTouchEnd)
        }

        return () => {
            document.removeEventListener("mousemove", handleMouseMove)
            document.removeEventListener("touchmove", handleTouchMove)
            document.removeEventListener("mouseup", handleMouseUp)
            document.removeEventListener("touchend", handleTouchEnd)
        }
    }, [isExpanded, isHolding, handleMouseMove, handleTouchMove, handleMouseUp, handleTouchEnd])

    useEffect(() => {
        setCurrentSpeed(startSpeed)
        setDragSpeed(startSpeed)
        const speedIndex = SPEED_VALUES.indexOf(startSpeed)
        const percentage = speedIndex >= 0
            ? (speedIndex / (SPEED_VALUES.length - 1)) * 100
            : 20
        setFillPercentage(percentage)
        setDragFillPercentage(percentage)
    }, [startSpeed])

    // Use drag values when dragging, otherwise use current values
    const displaySpeed = isHolding ? dragSpeed : currentSpeed
    const displayFillPercentage = isHolding ? dragFillPercentage : fillPercentage
    const currentSpeedLabel = SPEED_LABELS[SPEED_VALUES.indexOf(displaySpeed)]

    return (
        <div className={`flex items-center justify-center bg-background ${className}`}>
            <motion.div
                layout
                className="relative"
                initial={false}
                animate={{
                    width: isExpanded ? 320 : 70,
                }}
                transition={{
                    type: "spring",
                    stiffness: 400,
                    damping: 35,
                }}
            >
                <Button
                    ref={buttonRef}
                    variant="outline"
                    className={`relative overflow-hidden transition-all duration-300 select-none ${
                        isExpanded
                            ? "w-full h-8 rounded-full border-primary/30 cursor-grab active:cursor-grabbing"
                            : "h-8 px-6 hover:bg-accent cursor-pointer"
                    }`}
                    onClick={handleClick}
                    onMouseDown={handleMouseDown}
                    onTouchStart={handleTouchStart}
                    title={showTooltip ? "Click to cycle speeds, Ctrl+click for previous, hold to drag" : undefined}
                    style={{ userSelect: "none" }}
                >
                    {/* Background fill effect */}
                    <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-primary/20 via-primary/15 to-primary/10 rounded-full"
                        initial={{ scaleX: 0, originX: 0 }}
                        animate={{
                            scaleX: isExpanded ? displayFillPercentage / 100 : 0,
                            originX: 0,
                        }}
                        transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 30,
                        }}
                    />

                    {/* Speed markers when expanded */}
                    {isExpanded && (
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 0.4 }}
                            exit={{ opacity: 0 }}
                            className="absolute inset-0 flex items-center justify-between px-8"
                        >
                            {SPEED_VALUES.map((_, index) => (
                                <div key={index} className="w-0.5 h-6 bg-muted-foreground/30 rounded-full" />
                            ))}
                        </motion.div>
                    )}

                    <AnimatePresence mode="wait">
                        {!isExpanded ? (
                            <motion.div
                                key="button-content"
                                initial={{ opacity: 0, scale: 0.9 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0.9 }}
                                transition={{
                                    duration: 0.25,
                                    ease: "easeOut",
                                }}
                                className="relative z-10"
                            >
                                <span className="font-semibold text-foreground">{currentSpeedLabel}</span>
                            </motion.div>
                        ) : (
                            <motion.div
                                key="expanded-content"
                                initial={{ opacity: 0, y: 5 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -5 }}
                                transition={{
                                    duration: 0.3,
                                    delay: 0.1,
                                    ease: "easeOut",
                                }}
                                className="relative z-10 flex items-center justify-between w-full px-6"
                            >
                                <span className="text-sm font-medium text-muted-foreground">Playback Speed</span>
                                <span className="font-bold text-lg text-primary">{currentSpeedLabel}</span>
                            </motion.div>
                        )}
                    </AnimatePresence>
                </Button>

                {/* Enhanced glow effect when expanded */}
                <AnimatePresence>
                    {isExpanded && (
                        <motion.div
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.8 }}
                            transition={{ duration: 0.3 }}
                            className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 blur-xl -z-10"
                        />
                    )}
                </AnimatePresence>
            </motion.div>
        </div>
    )
}
