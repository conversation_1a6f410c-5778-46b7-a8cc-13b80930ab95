'use client'; // This directive is essential for a Client Component

import { useEffect } from 'react';

/**
 * A client component that blocks the browser's native zoom functionality
 * (Ctrl + Mouse Wheel, Ctrl + '+', Ctrl + '-').
 * It renders nothing to the DOM. Place it in your root layout.
 */
export default function BlockZoom() {
  useEffect(() => {
    // Handler for keyboard events
    const handleKeydown = (e: KeyboardEvent) => {
      // Check for Ctrl key and the '+', '-', or '=' keys
      if (e.ctrlKey && ['=', '-', '+'].includes(e.key)) {
        e.preventDefault();
      }
    };

    // Handler for the wheel event
    const handleWheel = (e: WheelEvent) => {
      // Check for Ctrl key to prevent zoom via mouse wheel
      if (e.ctrlKey) {
        e.preventDefault();
      }
    };

    // Add event listeners to the window
    window.addEventListener('keydown', handleKeydown);
    // The `passive: false` option is crucial for `preventDefault` to work on wheel events
    window.addEventListener('wheel', handleWheel, { passive: false });

    // Cleanup function to remove event listeners when the component unmounts
    return () => {
      window.removeEventListener('keydown', handleKeydown);
      window.removeEventListener('wheel', handleWheel);
    };
  }, []); // Empty dependency array ensures this effect runs only once

  // This component does not render any UI
  return null;
}