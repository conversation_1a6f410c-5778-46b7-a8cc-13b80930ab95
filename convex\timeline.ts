import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { ModalityConfigs } from "./schema";
import { asyncMap } from "convex-helpers";

export const getProjectModalities = query({
  args: { projectId: v.id("projects") },
  returns: v.array(v.object({
    ...ModalityConfigs.doc.fields,
    isVisible: v.boolean()
  })),
  handler: async (ctx, args) => {
    const project = await ctx.db.get(args.projectId);
    if (!project) return [];

    // Get all enabled modalities for this project
    const modalities = await Promise.all(
      project.enabledModalities.map(id => ctx.db.get(id))
    );

    // Filter out null values and add visibility from project settings
    const visibleModalityIds = project.visibleModalities || project.enabledModalities;

    const enrichedmodalities = await asyncMap(modalities, async (modality) => {
      
      return {
        ...modality!,
        isVisible: visibleModalityIds.includes(modality!._id)
      };
    });

    return enrichedmodalities
  },
});

export const getProjectEvents = query({
  args: {
    projectId: v.id("projects"),
    visibleModalitiesOnly: v.optional(v.boolean()),
    //visibleModalityIds: v.optional(v.array(v.id("modalityConfigs"))),
    sessionId: v.optional(v.id("streamSessions"))
  },
  handler: async (ctx, args) => {
    const project = await ctx.db.get(args.projectId);
        if (!project) return [];

    const visibleModalityIds = project.visibleModalities || project.enabledModalities;

    let eventsQuery = ctx.db
      .query("monitoringEvents")
      .withIndex("by_project", (q) => q.eq("projectId", args.projectId));

    if (args.sessionId) {
      eventsQuery = eventsQuery.filter((q) => q.eq(q.field("sessionId"), args.sessionId));
    }

    const events = await eventsQuery.collect();

  
    const eventWithModalityDetails = (await asyncMap(events, async (event) => {
      const modality = await ctx.db.get(event.modalityId);
      if(args.visibleModalitiesOnly && (!modality || !visibleModalityIds.includes(modality._id))) {
        return null;
      }
      return {
        ...event,
        modalityName: modality?.name || 'Unknown',
        modalityColor: modality?.colorCode || '#6b7280',
      };
    })).filter((result) => result !== null);
 

    return eventWithModalityDetails;
  },
});

export const updateProjectVisibleModalities = mutation({
  args: {
    projectId: v.id("projects"),
    visibleModalityIds: v.array(v.id("modalityConfigs"))
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.projectId, {
      visibleModalities: args.visibleModalityIds,
      updatedAt: Date.now()
    });
    
    return { success: true };
  },
});

export const createMonitoringEvent = mutation({
  args: {
    projectId: v.id("projects"),
    sessionId: v.id("streamSessions"),
    startTime: v.number(),
    endTime: v.optional(v.number()),
    modalityId: v.id("modalityConfigs"),
    eventType: v.string(),
    severity: v.union(v.literal("normal"), v.literal("warning"), v.literal("critical")),
    title: v.string(),
    description: v.string(),
    createdBy: v.id("users")
  },
  handler: async (ctx, args) => {
    // Create monitoring event with proper defaults
    const eventId = await ctx.db.insert("monitoringEvents", {
      ...args,
      timestamp: args.startTime, // For backward compatibility
      location: undefined,
      screenshots: [],
      videoClip: undefined,
      reviewerId: undefined,
      reviewStatus: "unreviewed",
      reviewNotes: undefined,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    return eventId;
  },
});

export const deleteMonitoringEvent = mutation({
  args: { eventId: v.id("monitoringEvents") },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.eventId);
    return { success: true };
  },
});
