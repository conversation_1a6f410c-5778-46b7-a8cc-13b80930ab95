"use client"

import { useCallback, useEffect, useRef } from 'react'
import { useVideoTimeline } from '@/components/contexts/VideoTimelineContext'

/**
 * Configuration options for video-timeline synchronization
 */
interface VideoTimelineSyncConfig {
  // Synchronization thresholds
  timeSyncThreshold?: number // Minimum time difference to trigger sync (seconds)
  seekDebounceMs?: number // Debounce time for seek operations (milliseconds)
  
  // Behavior flags
  enableBidirectionalSync?: boolean // Allow both video->timeline and timeline->video sync
  pauseOnSeek?: boolean // Pause playback during seeking
  resumeAfterSeek?: boolean // Resume playback after seeking if it was playing before
  
  // Performance options
  syncUpdateInterval?: number // Interval for sync updates (milliseconds)
  enableSmoothing?: boolean // Enable smooth transitions during sync
}

const defaultConfig: Required<VideoTimelineSyncConfig> = {
  timeSyncThreshold: 0.1, // 100ms threshold
  seekDebounceMs: 100,
  enableBidirectionalSync: true,
  pauseOnSeek: false,
  resumeAfterSeek: false,
  syncUpdateInterval: 100,
  enableSmoothing: true,
}

/**
 * Enhanced hook for video-timeline synchronization
 * Provides optimized sync logic with debouncing and smooth transitions
 */
export function useVideoTimelineSync(config: VideoTimelineSyncConfig = {}) {
  const finalConfig = { ...defaultConfig, ...config }
  const videoTimeline = useVideoTimeline()
  
  // Refs for tracking state and preventing stale closures
  const lastSyncTimeRef = useRef<number>(0)
  const seekTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const wasPlayingBeforeSeekRef = useRef<boolean>(false)
  const lastVideoTimeRef = useRef<number>(0)
  const lastTimelineTimeRef = useRef<number>(0)

  /**
   * Debounced seek function to prevent excessive seek operations
   */
  const debouncedSeek = useCallback((time: number, source: 'video' | 'timeline') => {
    if (seekTimeoutRef.current) {
      clearTimeout(seekTimeoutRef.current)
    }

    seekTimeoutRef.current = setTimeout(() => {
      videoTimeline.seekTo(time, source)
      seekTimeoutRef.current = null
    }, finalConfig.seekDebounceMs)
  }, [videoTimeline, finalConfig.seekDebounceMs])

  /**
   * Handle video time updates with intelligent synchronization
   */
  const handleVideoTimeUpdate = useCallback((time: number) => {
    const now = Date.now()
    const timeDiff = Math.abs(time - videoTimeline.currentTime)
    
    // Only sync if the time difference exceeds threshold and enough time has passed
    if (
      timeDiff > finalConfig.timeSyncThreshold &&
      now - lastSyncTimeRef.current > finalConfig.syncUpdateInterval &&
      !videoTimeline.isSeeking
    ) {
      videoTimeline.updateCurrentTime(time, 'video')
      lastSyncTimeRef.current = now
      lastVideoTimeRef.current = time
    }
  }, [videoTimeline, finalConfig.timeSyncThreshold, finalConfig.syncUpdateInterval])

  /**
   * Handle timeline time updates with intelligent synchronization
   */
  const handleTimelineTimeUpdate = useCallback((time: number) => {
    const now = Date.now()
    const timeDiff = Math.abs(time - videoTimeline.currentTime)
    
    // Only sync if bidirectional sync is enabled and conditions are met
    if (
      finalConfig.enableBidirectionalSync &&
      timeDiff > finalConfig.timeSyncThreshold &&
      now - lastSyncTimeRef.current > finalConfig.syncUpdateInterval &&
      !videoTimeline.isSeeking
    ) {
      videoTimeline.updateCurrentTime(time, 'timeline')
      lastSyncTimeRef.current = now
      lastTimelineTimeRef.current = time
    }
  }, [videoTimeline, finalConfig.enableBidirectionalSync, finalConfig.timeSyncThreshold, finalConfig.syncUpdateInterval])

  /**
   * Handle timeline cursor drag start
   */
  const handleTimelineDragStart = useCallback((time: number) => {
    if (finalConfig.pauseOnSeek && videoTimeline.isPlaying) {
      wasPlayingBeforeSeekRef.current = true
      videoTimeline.pause()
    }
    
    videoTimeline.startSeeking(time, 'timeline')
  }, [videoTimeline, finalConfig.pauseOnSeek])

  /**
   * Handle timeline cursor drag update
   */
  const handleTimelineDragUpdate = useCallback((time: number) => {
    videoTimeline.updateSeeking(time)
  }, [videoTimeline])

  /**
   * Handle timeline cursor drag end
   */
  const handleTimelineDragEnd = useCallback(() => {
    videoTimeline.endSeeking()
    
    if (finalConfig.resumeAfterSeek && wasPlayingBeforeSeekRef.current) {
      videoTimeline.play()
      wasPlayingBeforeSeekRef.current = false
    }
  }, [videoTimeline, finalConfig.resumeAfterSeek])

  /**
   * Handle timeline click-to-seek
   */
  const handleTimelineClick = useCallback((time: number) => {
    videoTimeline.seekTo(time, 'timeline')
  }, [videoTimeline])

  /**
   * Handle video seek operations
   */
  const handleVideoSeek = useCallback((time: number) => {
    if (finalConfig.enableBidirectionalSync) {
      debouncedSeek(time, 'video')
    }
  }, [debouncedSeek, finalConfig.enableBidirectionalSync])

  /**
   * Get current sync status
   */
  const getSyncStatus = useCallback(() => {
    const videoTime = lastVideoTimeRef.current
    const timelineTime = lastTimelineTimeRef.current
    const currentTime = videoTimeline.currentTime
    
    return {
      isInSync: Math.abs(videoTime - timelineTime) <= finalConfig.timeSyncThreshold,
      videoTime,
      timelineTime,
      currentTime,
      isSeeking: videoTimeline.isSeeking,
      seekingSource: videoTimeline.seekingSource,
      timeDifference: Math.abs(videoTime - timelineTime),
    }
  }, [videoTimeline, finalConfig.timeSyncThreshold])

  /**
   * Force synchronization between video and timeline
   */
  const forcSync = useCallback((source: 'video' | 'timeline' = 'video') => {
    if (source === 'video') {
      videoTimeline.updateCurrentTime(lastVideoTimeRef.current, 'video')
    } else {
      videoTimeline.updateCurrentTime(lastTimelineTimeRef.current, 'timeline')
    }
    lastSyncTimeRef.current = Date.now()
  }, [videoTimeline])

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (seekTimeoutRef.current) {
        clearTimeout(seekTimeoutRef.current)
      }
    }
  }, [])

  return {
    // Video handlers
    handleVideoTimeUpdate,
    handleVideoSeek,
    
    // Timeline handlers
    handleTimelineTimeUpdate,
    handleTimelineClick,
    handleTimelineDragStart,
    handleTimelineDragUpdate,
    handleTimelineDragEnd,
    
    // Utility functions
    getSyncStatus,
    forcSync,
    
    // State access
    ...videoTimeline,
    
    // Configuration
    config: finalConfig,
  }
}

/**
 * Simplified hook for basic video-timeline synchronization
 * Provides essential sync functionality with minimal configuration
 */
export function useBasicVideoTimelineSync() {
  return useVideoTimelineSync({
    timeSyncThreshold: 0.2,
    seekDebounceMs: 150,
    enableBidirectionalSync: true,
    pauseOnSeek: false,
    resumeAfterSeek: false,
  })
}

/**
 * Hook optimized for performance-critical scenarios
 * Reduces sync frequency and enables smoothing
 */
export function usePerformantVideoTimelineSync() {
  return useVideoTimelineSync({
    timeSyncThreshold: 0.5,
    seekDebounceMs: 200,
    syncUpdateInterval: 200,
    enableSmoothing: true,
    enableBidirectionalSync: true,
  })
}
