title: TODO
---

### Function expansion

- [ ] Zoom the timeline (can be achieved by controlling scale, need to provide external wheel callback?)

- [x] Pass time as a parameter when clicking

- [x] Migrate style rendering: from effects => props, provide more powerful style expansion capabilities

- [x] Allow custom time area rendering capabilities

### Design expansion

- [ ] Consider how to be compatible with the following common frame animation editor interaction modes, and whether it is necessary to be compatible (the following is a preliminary consideration of compatible implementation solutions)

- [ ] Changes in action data structure

- [ ] Solution 1: Convert previous start and end to points collection

- [ ] Solution 2: Add the concept of group to group actions

- [ ] Runner multi-time unit adaptation capability (s/frame)

![aim](/assets/aim.png)