# Timeline Component Implementation Guide
## Advanced Event Timeline for Neuromonitoring

### 🎯 Overview

The timeline component is the core of the NFM system, providing real-time event visualization, annotation, and review capabilities. It must handle:
- Multiple modality tracks (EMG, MEP, SSEP, etc.)
- Real-time event synchronization
- Video timestamp coordination
- Event filtering and search
- Collaborative annotations

---

### 📦 Timeline Component Architecture

```typescript
// components/timeline/TimelineContainer.tsx
import React, { useState, useEffect, useRef } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { TimelineEditor } from './TimelineEditor';
import { EventMarker } from './EventMarker';
import { ModalityTrack } from './ModalityTrack';

interface TimelineContainerProps {
  projectId: string;
  sessionId: string;
  currentTime: number;
  duration: number;
  onTimeSeek: (time: number) => void;
  onEventCreate: (event: Partial<MonitoringEvent>) => void;
}

export function TimelineContainer({
  projectId,
  sessionId,
  currentTime,
  duration,
  onTimeSeek,
  onEventCreate
}: TimelineContainerProps) {
  const [selectedModalities, setSelectedModalities] = useState<string[]>([]);
  const [timeScale, setTimeScale] = useState(1); // pixels per second
  const [expandedView, setExpandedView] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<string | null>(null);

  // Real-time data from Convex
  const events = useQuery(api.monitoring.getEventsBySession, {
    sessionId,
  });
  
  const modalities = useQuery(api.configuration.getActiveModalities, {});
  
  const createEvent = useMutation(api.monitoring.createEvent);

  // Timeline viewport management
  const [viewportStart, setViewportStart] = useState(0);
  const [viewportEnd, setViewportEnd] = useState(duration);

  const handleEventClick = (eventId: string) => {
    setSelectedEvent(eventId);
    const event = events?.find(e => e._id === eventId);
    if (event) {
      onTimeSeek(event.timestamp / 1000); // Convert to seconds
    }
  };

  const handleEventCreate = async (modalityId: string, timestamp: number) => {
    const newEvent = {
      projectId,
      sessionId,
      timestamp: timestamp * 1000, // Convert to milliseconds
      modalityId,
      eventType: 'manual_annotation',
      severity: 'normal' as const,
      description: '',
      screenshots: [],
      reviewed: false,
      createdBy: 'current-user-id', // Get from auth context
    };

    await createEvent(newEvent);
    onEventCreate(newEvent);
  };

  return (
    <div className="timeline-container w-full">
      {/* Timeline Header */}
      <TimelineHeader
        modalities={modalities || []}
        selectedModalities={selectedModalities}
        onModalityToggle={setSelectedModalities}
        expandedView={expandedView}
        onExpandToggle={() => setExpandedView(!expandedView)}
        timeScale={timeScale}
        onTimeScaleChange={setTimeScale}
      />

      {/* Main Timeline */}
      <div className="timeline-viewport relative overflow-x-auto border rounded-lg">
        <TimelineRuler
          duration={duration}
          timeScale={timeScale}
          currentTime={currentTime}
          viewportStart={viewportStart}
          viewportEnd={viewportEnd}
        />

        {/* Modality Tracks */}
        <div className="modality-tracks">
          {(modalities || [])
            .filter(m => selectedModalities.length === 0 || selectedModalities.includes(m._id))
            .map(modality => (
              <ModalityTrack
                key={modality._id}
                modality={modality}
                events={events?.filter(e => e.modalityId === modality._id) || []}
                duration={duration}
                timeScale={timeScale}
                currentTime={currentTime}
                expandedView={expandedView}
                onEventClick={handleEventClick}
                onEventCreate={(timestamp) => handleEventCreate(modality._id, timestamp)}
                selectedEventId={selectedEvent}
              />
            ))}
        </div>

        {/* Current Time Indicator */}
        <CurrentTimeIndicator
          currentTime={currentTime}
          timeScale={timeScale}
          duration={duration}
        />
      </div>

      {/* Timeline Controls */}
      <TimelineControls
        currentTime={currentTime}
        duration={duration}
        onTimeSeek={onTimeSeek}
        onZoomIn={() => setTimeScale(timeScale * 1.5)}
        onZoomOut={() => setTimeScale(timeScale / 1.5)}
        onFitToView={() => {
          const containerWidth = 1200; // Get actual container width
          setTimeScale(containerWidth / duration);
        }}
      />
    </div>
  );
}
```

### 🎵 Modality Track Component

```typescript
// components/timeline/ModalityTrack.tsx
import React, { useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { EventMarker } from './EventMarker';

interface ModalityTrackProps {
  modality: ModalityConfig;
  events: MonitoringEvent[];
  duration: number;
  timeScale: number;
  currentTime: number;
  expandedView: boolean;
  onEventClick: (eventId: string) => void;
  onEventCreate: (timestamp: number) => void;
  selectedEventId: string | null;
}

export function ModalityTrack({
  modality,
  events,
  duration,
  timeScale,
  currentTime,
  expandedView,
  onEventClick,
  onEventCreate,
  selectedEventId
}: ModalityTrackProps) {
  const trackRef = useRef<HTMLDivElement>(null);
  const [hoveredTime, setHoveredTime] = useState<number | null>(null);

  const trackWidth = duration * timeScale;
  const trackHeight = expandedView ? 80 : 40;

  const handleTrackClick = (e: React.MouseEvent) => {
    if (trackRef.current) {
      const rect = trackRef.current.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const timestamp = clickX / timeScale;
      onEventCreate(timestamp);
    }
  };

  const handleTrackMouseMove = (e: React.MouseEvent) => {
    if (trackRef.current) {
      const rect = trackRef.current.getBoundingClientRect();
      const mouseX = e.clientX - rect.left;
      const timestamp = mouseX / timeScale;
      setHoveredTime(timestamp);
    }
  };

  return (
    <div className="modality-track border-b border-gray-200">
      {/* Track Label */}
      <div className="track-header flex items-center justify-between p-2 bg-gray-50 min-w-[200px]">
        <div className="flex items-center gap-2">
          <div
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: modality.colorCode }}
          />
          <Badge variant="outline" style={{ borderColor: modality.colorCode }}>
            {modality.displayName}
          </Badge>
        </div>
        <div className="text-sm text-gray-500">
          {events.length} events
        </div>
      </div>

      {/* Track Canvas */}
      <div
        ref={trackRef}
        className="track-canvas relative cursor-crosshair bg-white hover:bg-gray-50/50"
        style={{ 
          height: trackHeight,
          minWidth: trackWidth,
        }}
        onClick={handleTrackClick}
        onMouseMove={handleTrackMouseMove}
        onMouseLeave={() => setHoveredTime(null)}
      >
        {/* Background Grid */}
        <div className="absolute inset-0 opacity-10">
          {Array.from({ length: Math.ceil(duration / 10) }).map((_, i) => (
            <div
              key={i}
              className="absolute top-0 bottom-0 border-l border-gray-300"
              style={{ left: i * 10 * timeScale }}
            />
          ))}
        </div>

        {/* Event Markers */}
        {events.map(event => (
          <EventMarker
            key={event._id}
            event={event}
            timeScale={timeScale}
            trackHeight={trackHeight}
            isSelected={selectedEventId === event._id}
            onClick={() => onEventClick(event._id)}
            expandedView={expandedView}
          />
        ))}

        {/* Hover Time Indicator */}
        {hoveredTime !== null && (
          <div
            className="absolute top-0 bottom-0 w-px bg-blue-400 pointer-events-none"
            style={{ left: hoveredTime * timeScale }}
          >
            <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-blue-400 text-white px-2 py-1 rounded text-xs whitespace-nowrap">
              {formatTime(hoveredTime)}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  const ms = Math.floor((seconds % 1) * 1000);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`;
}
```
