# Dynamic Stream Configuration

## Overview

The NFM system now supports dynamic stream source configuration through the web interface. This allows hospital staff to:

1. Add new RTSP stream sources without restarting MediaMTX
2. Configure streams for different equipment (Inomed, microscopes, cameras)
3. Monitor stream status and viewer count
4. Remove unused stream sources

## How It Works

### 1. Web Interface
Navigate to **Settings → Stream Sources** in the NFM application to manage streams.

### 2. Adding a Stream
Click "Add Stream Source" and provide:
- **Path Name**: Unique identifier (e.g., `or1_main`, `microscope_1`)
- **RTSP URL**: Source URL (e.g., `rtsp://*************:8554/main/av`)
- **Stream Type**: Equipment type (Inomed, Microscope, Camera, Screen)
- **Description**: Optional description

### 3. Behind the Scenes
When you add a stream:
1. The app calls MediaMTX API to configure the path
2. Configuration is stored in Convex database
3. Stream becomes available immediately at:
   - RTSP: `rtsp://localhost:8554/{path_name}`
   - WebRTC: `http://localhost:8889/{path_name}`

### 4. Authentication
- Publishing requires admin credentials
- Viewing is allowed without authentication
- Each stream inherits the global auth settings

## Example Configurations

### Inomed System
```
Path: or1_main
URL: rtsp://*************:8554/main/av
Type: Inomed System
Description: Operating Room 1 - Main Feed
```

### Surgical Microscope
```
Path: microscope_or2
URL: rtsp://************:554/live
Type: Microscope
Description: OR2 Zeiss Microscope
```

### Room Camera
```
Path: camera_or1_overview
URL: rtsp://************:554/ch01
Type: Camera  
Description: OR1 Overview Camera
```

## API Endpoints

The system uses MediaMTX API v3:
- Add path: `POST /v3/config/paths/add/{name}`
- Remove path: `DELETE /v3/config/paths/delete/{name}`
- List paths: `GET /v3/paths/list`

## Security Notes

1. Stream sources are configured server-side only
2. Authentication prevents unauthorized publishing
3. All configurations are logged in the database
4. Only authenticated users can modify stream sources

## Troubleshooting

1. **Stream not connecting**: Verify RTSP URL is accessible
2. **Path already exists**: Use a different path name
3. **API errors**: Check MediaMTX is running and API is enabled
4. **Auth failures**: Verify source device credentials