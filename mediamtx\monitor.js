const http = require('http');
const https = require('https');

// Configuration
const API_URL = 'http://localhost:9997';
const METRICS_URL = 'http://localhost:9998';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  cyan: '\x1b[36m',
};

function log(message, color = colors.reset) {
  console.log(`${color}${new Date().toLocaleTimeString()} - ${message}${colors.reset}`);
}

function checkMediaMTX() {
  // Check API
  http.get(`${API_URL}/v3/paths/list`, (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      try {
        const paths = JSON.parse(data);
        const streamCount = Object.keys(paths.items).length;
        
        log(`MediaMTX Status: ONLINE`, colors.green);
        log(`Active streams: ${streamCount}`, colors.cyan);
        
        Object.entries(paths.items).forEach(([name, info]) => {
          const readers = info.readers?.length || 0;
          const source = info.source ? 'ACTIVE' : 'WAITING';
          log(`  - ${name}: ${source}, ${readers} viewers`, colors.yellow);
        });
      } catch (err) {
        log(`Failed to parse API response: ${err.message}`, colors.red);
      }
    });
  }).on('error', (err) => {
    log(`MediaMTX API not accessible: ${err.message}`, colors.red);
  });
}

// Check every 5 seconds
log('Starting MediaMTX Monitor...', colors.cyan);
log(`Monitoring API at: ${API_URL}`, colors.cyan);
setInterval(checkMediaMTX, 5000);
checkMediaMTX();