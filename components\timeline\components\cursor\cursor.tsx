import React, { FC, useEffect, useRef } from 'react';
import { ScrollSync } from 'react-virtualized';
import { CommonProp } from '../../interface/common_prop';
import { prefix } from '../../utils/deal_class_prefix';
import { parserPixelToTime, parserTimeToPixel } from '../../utils/deal_data';
import { RowDnd } from '../row_rnd/row_rnd';
import { RowRndApi } from '../row_rnd/row_rnd_interface';
import './cursor.css';

/** Animation Timeline Component Parameters */
export type CursorProps = CommonProp & {
  /** Scroll distance to the left */
  scrollLeft: number;
  /** Set cursor position */
  setCursor: (param: { left?: number; time?: number }) => boolean;
  /** Timeline area dom ref */
  areaRef: React.RefObject<HTMLDivElement>;
  /** Setup scroll left */
  deltaScrollLeft: (delta: number) => void;
  /** Scroll sync ref（TODO: This data is used to temporarily solve the out-of-sync problem when scrollLeft is dragged) */
  scrollSync: React.RefObject<ScrollSync>;
};

export const Cursor: FC<CursorProps> = ({
  disableDrag,
  cursorTime,
  setCursor,
  startLeft,
  timelineWidth,
  scaleWidth,
  scale,
  scrollLeft,
  scrollSync,
  areaRef,
  maxScaleCount,
  deltaScrollLeft,
  onCursorDragStart,
  onCursorDrag,
  onCursorDragEnd,
}) => {
  const rowRnd = useRef<RowRndApi | null>(null);
  const draggingLeft = useRef<number | undefined>(undefined);

  useEffect(() => {
    if (typeof draggingLeft.current === 'undefined' && startLeft !== undefined && scaleWidth !== undefined && scale !== undefined) {
      // When not dragging, update the cursor scale according to the parameter
      rowRnd?.current?.updateLeft(parserTimeToPixel(cursorTime, { startLeft, scaleWidth, scale }) - scrollLeft);
    }
  }, [cursorTime, startLeft, scaleWidth, scale, scrollLeft]);

  return (
    <RowDnd
      start={startLeft}
      ref={rowRnd}
      parentRef={areaRef}
      bounds={{
        left: 0,
        right: Math.min(timelineWidth, maxScaleCount! * scaleWidth! + startLeft! - scrollLeft),
      }}
      deltaScrollLeft={deltaScrollLeft}
      enableDragging={!disableDrag}
      enableResizing={false}
      onDragStart={() => {
        if (onCursorDragStart) {
          onCursorDragStart(cursorTime);
        }
        if (startLeft !== undefined && scaleWidth !== undefined && scale !== undefined) {
          draggingLeft.current = parserTimeToPixel(cursorTime, { startLeft, scaleWidth, scale }) - scrollLeft;
          rowRnd?.current?.updateLeft(draggingLeft.current);
        }
      }}
      onDragEnd={() => {
        if (typeof draggingLeft.current != 'undefined' && startLeft !== undefined && scaleWidth !== undefined && scale !== undefined) {
          const time = parserPixelToTime(draggingLeft.current + scrollLeft, { startLeft, scale, scaleWidth });
          setCursor({ time });
          if(onCursorDragEnd) {
            onCursorDragEnd(time);
          }
        }
        draggingLeft.current = undefined;
      }}
      onDrag={({ left }, scroll = 0) => {
        const scrollLeft = scrollSync.current.state.scrollLeft;

        if (!scroll || scrollLeft === 0) {
          // When dragging, if the current left < left min, set the value to left min
          if (left < startLeft! - scrollLeft) draggingLeft.current = startLeft! - scrollLeft;
          else draggingLeft.current = left;
        } else {
          // During auto-scroll, if current left < left min, set value to left min
          if (draggingLeft.current !== undefined && startLeft !== undefined && draggingLeft.current < startLeft - scrollLeft - scroll) {
            draggingLeft.current = startLeft - scrollLeft - scroll;
          }
        }
        rowRnd?.current?.updateLeft(draggingLeft!.current!);
        if (typeof draggingLeft.current != 'undefined' && startLeft !== undefined && scaleWidth !== undefined && scale !== undefined) {
          const time = parserPixelToTime(draggingLeft.current + scrollLeft, { startLeft, scale, scaleWidth });

          // Clamp time to valid bounds (0 to maxScaleCount * scale)
          const maxTime = maxScaleCount! * scale!;
          const clampedTime = Math.max(0, Math.min(time, maxTime));

          setCursor({ time: clampedTime });
          if(onCursorDrag) {
            onCursorDrag(clampedTime);
          }
        }
        return false;
      }}
    >
      <div className={prefix('cursor')}>
        <svg className={prefix('cursor-top')} width="8" height="12" viewBox="0 0 8 12" fill="none">
          <path
            d="M0 1C0 0.447715 0.447715 0 1 0H7C7.55228 0 8 0.447715 8 1V9.38197C8 9.76074 7.786 10.107 7.44721 10.2764L4.44721 11.7764C4.16569 11.9172 3.83431 11.9172 3.55279 11.7764L0.552786 10.2764C0.214002 10.107 0 9.76074 0 9.38197V1Z"
            fill="#5297FF"
          />
        </svg>
        <div className={prefix('cursor-area')} />
      </div>
    </RowDnd>
  );
};
