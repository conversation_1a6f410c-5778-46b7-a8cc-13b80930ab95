"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  FileText,
  User,
  Play,
  BarChart3,
  Edit,
  Mail
} from "lucide-react"

interface ProjectActionModalProps {
  isOpen: boolean
  onClose: () => void
  onAction: (action: string) => void
  project?: {
    _id: string
    patient?: { firstName: string; lastName: string }
    surgeryType: string
    status: string
  }
}

export function ProjectActionModal({ 
  isOpen, 
  onClose, 
  onAction, 
  project 
}: ProjectActionModalProps) {
  const handleAction = (action: string) => {
    onAction(action)
    onClose()
  }

  if (!project) return null

  const getActions = () => {
    const actions = [
      { id: "details", label: "Open Project Details", icon: FileText },
      { id: "patient", label: "View Patient Information", icon: User }
    ]

    if (project.status === "scheduled") {
      actions.push({ id: "start", label: "Start Live Monitoring", icon: Play })
    }

    if (project.status === "in-progress") {
      actions.push({ id: "monitor", label: "Open Evaluation Mode", icon: BarChart3 })
    }

    actions.push(
      { id: "report", label: "Generate Report", icon: FileText },
      { id: "edit", label: "Edit Project", icon: Edit },
      { id: "share", label: "Send to Colleagues", icon: Mail }
    )

    return actions
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Project Actions</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-2">
          {getActions().map((action) => {
            const Icon = action.icon
            return (
              <Button
                key={action.id}
                variant="ghost"
                className="w-full justify-start h-auto p-4"
                onClick={() => handleAction(action.id)}
              >
                <Icon className="mr-3 h-5 w-5" />
                <span>{action.label}</span>
              </Button>
            )
          })}
        </div>
        
        <div className="flex gap-3 pt-4 border-t">
          <Button variant="outline" onClick={onClose} className="flex-1">
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}