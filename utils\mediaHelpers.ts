/**
 * Media helper utilities for video streaming and capture
 */

/**
 * Format duration from seconds to HH:MM:SS
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

/**
 * Format timestamp for screenshots and events
 */
export function formatTimestamp(timestamp: number = Date.now()): string {
  return new Date(timestamp).toISOString().replace(/[:.]/g, '-').slice(0, -5);
}

/**
 * Get video quality based on resolution
 */
export function getVideoQuality(width: number, height: number): string {
  if (width >= 1920 && height >= 1080) return "HD";
  if (width >= 1280 && height >= 720) return "Standard";
  return "Basic";
}

/**
 * Check if browser supports required features
 */
export function checkBrowserSupport(): {
  webrtc: boolean;
  pictureInPicture: boolean;
  fullscreen: boolean;
  canvasCapture: boolean;
} {
  return {
    webrtc: !!(window.RTCPeerConnection && navigator.mediaDevices),
    pictureInPicture: !!document.pictureInPictureEnabled,
    fullscreen: !!document.fullscreenEnabled,
    canvasCapture: !!HTMLCanvasElement.prototype.toBlob
  };
}

/**
 * Generate screenshot filename
 */
export function generateScreenshotFilename(
  streamPath: string, 
  timestamp?: number
): string {
  const time = formatTimestamp(timestamp);
  return `nfm-screenshot-${streamPath}-${time}.png`;
}

/**
 * Download blob as file
 */
export function downloadBlob(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

/**
 * Convert video element to canvas and capture frame
 */
export function captureVideoFrame(
  video: HTMLVideoElement
): Promise<Blob | null> {
  return new Promise((resolve) => {
    if (!video || video.videoWidth === 0) {
      resolve(null);
      return;
    }

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    
    if (!ctx) {
      resolve(null);
      return;
    }

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0);

    canvas.toBlob((blob) => resolve(blob), "image/png");
  });
}
