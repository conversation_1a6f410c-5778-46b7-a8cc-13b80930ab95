"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Menu } from "lucide-react"
import { Sidebar } from "./sidebar"

interface MobileNavProps {
  activeSessionsCount?: number
}

export function MobileNav({ activeSessionsCount = 0 }: MobileNavProps) {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline" size="icon" className="md:hidden">
          <Menu className="h-4 w-4" />
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="p-0 w-full max-w-xs h-full">
        <div className="h-full">
          <Sidebar activeSessionsCount={activeSessionsCount} className="h-full w-full" />
        </div>
      </SheetContent>
    </Sheet>
  )
}