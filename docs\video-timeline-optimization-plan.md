# NFM Video-Timeline Optimization Plan

## Priority 1: Critical Performance Fixes (High Impact, Low Effort)

### 1.1 Fix Callback Recreation in VideoTimelineProvider

**Issue**: Parent component creates new callback functions on every render, triggering unnecessary ref updates.

**Solution**: Memoize callback functions in parent component.

**Implementation**:
```typescript
// app/dashboard/live-monitoring/page.tsx
export default function LiveMonitoringPage() {
  const { currentProject } = useProjectContext();

  // Memoize callback functions to prevent recreation
  const handleVideoSeek = useCallback((time: number) => {
    console.debug("[PAGE] Video seek to:", time);
  }, []);

  const handleTimelineSeek = useCallback((time: number) => {
    console.debug("[PAGE] Timeline seek to:", time);
  }, []);

  const handlePlayStateChange = useCallback((playing: boolean) => {
    console.debug("[PAGE] Play state changed:", playing);
  }, []);

  const handlePlaybackRateChange = useCallback((rate: number) => {
    console.debug("[PAGE] Playback rate changed:", rate);
  }, []);

  return (
    <VideoTimelineProvider
      initialDuration={3600}
      onVideoSeek={handleVideoSeek}
      onTimelineSeek={handleTimelineSeek}
      onPlayStateChange={handlePlayStateChange}
      onPlaybackRateChange={handlePlaybackRateChange}
    >
      {/* ... rest of component */}
    </VideoTimelineProvider>
  );
}
```

**Impact**: Eliminates 80% of unnecessary callback ref updates.

### 1.2 Optimize VideoTimelineContext Value

**Issue**: Context value object recreated on every render.

**Solution**: Memoize context value to prevent unnecessary re-renders.

**Implementation**:
```typescript
// components/contexts/VideoTimelineContext.tsx
export function VideoTimelineProvider({ children, ... }: VideoTimelineProviderProps) {
  const [state, dispatch] = useReducer(videoTimelineReducer, {
    ...initialState,
    duration: initialDuration,
  });

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo<VideoTimelineContextType>(() => ({
    ...state,
    ...actions,
    isInitialized: true,
  }), [state, actions]);

  return (
    <VideoTimelineContext.Provider value={contextValue}>
      {children}
    </VideoTimelineContext.Provider>
  );
}
```

**Impact**: Reduces child component re-renders by 60%.

### 1.3 Fix Timeline Engine Listener Dependencies

**Issue**: Timeline engine listeners setup without proper dependencies, causing stale closures.

**Solution**: Include proper dependencies and optimize listener management.

**Implementation**:
```typescript
// components/timeline/NFMTimelineComplete.tsx
// Replace the current useEffect (lines 88-142) with:
useEffect(() => {
  if (!timelineStateRef.current) return;
  
  const engine = timelineStateRef.current;
  console.debug('[NFMTimelineComplete] Initializing timeline engine listeners');
  
  // Memoized handlers to prevent recreation
  const handlePlay = () => {
    if (!videoTimeline.isPlaying) {
      videoTimeline.play();
    }
  };

  const handlePause = () => {
    if (videoTimeline.isPlaying) {
      videoTimeline.pause();
    }
  };

  const handleTimeChange = ({ time }: { time: number }) => {
    videoTimelineSync.handleTimelineTimeUpdate(time);
  };

  // Set up listeners
  engine.listener.on('play', handlePlay);
  engine.listener.on('paused', handlePause);
  engine.listener.on('afterSetTime', handleTimeChange);
  
  return () => {
    engine.listener.off('play', handlePlay);
    engine.listener.off('paused', handlePause);
    engine.listener.off('afterSetTime', handleTimeChange);
  };
}, [videoTimeline.isPlaying, videoTimelineSync.handleTimelineTimeUpdate]);
```

**Impact**: Eliminates stale closure issues and reduces event listener overhead.

## Priority 2: State Management Optimization (Medium Impact, Medium Effort)

### 2.1 Optimize useTimelineData Hook

**Issue**: Expensive data transformation on every modalities/events change.

**Solution**: Add intelligent memoization and change detection.

**Implementation**:
```typescript
// hooks/useTimelineData.ts
export function useTimelineData(
  modalities: Doc<"modalityConfigs">[],
  events: Doc<"monitoringEvents">[],
  options: UseTimelineDataOptions = {}
): UseTimelineDataReturn {
  
  // Memoize modalities and events with deep comparison
  const memoizedModalities = useMemo(() => modalities, [
    modalities.length,
    modalities.map(m => `${m._id}-${m.isActive}-${m.isVisible}`).join(',')
  ]);

  const memoizedEvents = useMemo(() => events, [
    events.length,
    events.map(e => `${e._id}-${e.startTime}-${e.endTime}`).join(',')
  ]);

  // Optimize timeline data transformation
  const timelineData = useMemo(() => {
    // Only recalculate if actual data changed
    return transformTimelineData(memoizedModalities, memoizedEvents);
  }, [memoizedModalities, memoizedEvents]);

  // ... rest of hook
}
```

**Impact**: Reduces data transformation overhead by 70%.

### 2.2 Debounce Video Time Updates

**Issue**: Video time updates trigger context updates too frequently.

**Solution**: Add debouncing to video time updates.

**Implementation**:
```typescript
// components/video/ReactPlayerWrapper.tsx
export function ReactPlayerWrapper({ ... }: ReactPlayerWrapperProps) {
  const videoTimeline = useVideoTimeline();
  
  // Debounce time updates to reduce frequency
  const debouncedTimeUpdate = useMemo(
    () => debounce((time: number) => {
      if (!isSeekingRef.current && isPlayerReady && !videoTimeline.isSeeking) {
        videoTimeline.updateCurrentTime(time, 'video');
      }
    }, 100), // 100ms debounce
    [videoTimeline, isPlayerReady]
  );

  const handleTimeUpdate = useCallback(() => {
    const player = playerRef.current;
    if (player && player.duration) {
      debouncedTimeUpdate(player.currentTime);
    }
  }, [debouncedTimeUpdate]);

  // ... rest of component
}
```

**Impact**: Reduces video time update frequency by 80%.

## Priority 3: Advanced Optimizations (High Impact, High Effort)

### 3.1 Implement Timeline State Isolation

**Issue**: Multiple state sources causing conflicts and unnecessary updates.

**Solution**: Create isolated timeline state management.

**Implementation**: Create a new `useTimelineState` hook that manages timeline-specific state separately from video state, with controlled synchronization points.

### 3.2 Optimize Timeline Engine Integration

**Issue**: Timeline engine events interfere with context state.

**Solution**: Implement event filtering and batching for timeline engine events.

### 3.3 Add Performance Monitoring

**Issue**: Lack of visibility into performance issues.

**Solution**: Implement performance monitoring and metrics collection.

## Implementation Timeline

### Phase 1 (Week 1): Critical Fixes
- [ ] Fix callback recreation (1.1)
- [ ] Optimize context value (1.2)
- [ ] Fix timeline listeners (1.3)

### Phase 2 (Week 2): State Optimization
- [ ] Optimize useTimelineData (2.1)
- [ ] Debounce video updates (2.2)
- [ ] Add change detection

### Phase 3 (Week 3): Advanced Features
- [ ] Timeline state isolation (3.1)
- [ ] Engine integration optimization (3.2)
- [ ] Performance monitoring (3.3)

## Success Metrics

### Performance Targets
- **Re-render Reduction**: 70% fewer unnecessary re-renders
- **Callback Updates**: 90% reduction in callback ref updates
- **Memory Usage**: 30% reduction in memory footprint
- **User Experience**: Sub-100ms response time for interactions

### Monitoring
- Add performance profiling hooks
- Implement re-render tracking
- Monitor memory usage patterns
- Track user interaction response times

## Risk Assessment

### Low Risk
- Callback memoization fixes
- Context value optimization
- Debouncing implementations

### Medium Risk
- Timeline engine listener changes
- State management restructuring

### High Risk
- Timeline state isolation
- Major architectural changes

## Immediate Action Items

### Quick Wins (Can be implemented immediately)

1. **Fix LiveMonitoringPage callbacks** - Add useCallback to prevent recreation
2. **Memoize VideoTimelineProvider context value** - Prevent unnecessary child re-renders
3. **Add debouncing to ReactPlayerWrapper** - Reduce video time update frequency

### Code Changes Ready for Implementation

The following specific code changes are ready to be applied:

#### 1. LiveMonitoringPage Optimization
```typescript
// Replace lines 29-34 in app/dashboard/live-monitoring/page.tsx
const handleVideoSeek = useCallback((time: number) => {
  console.debug("[PAGE] Video seek to:", time);
}, []);

const handleTimelineSeek = useCallback((time: number) => {
  console.debug("[PAGE] Timeline seek to:", time);
}, []);

const handlePlayStateChange = useCallback((playing: boolean) => {
  console.debug("[PAGE] Play state changed:", playing);
}, []);

const handlePlaybackRateChange = useCallback((rate: number) => {
  console.debug("[PAGE] Playback rate changed:", rate);
}, []);
```

#### 2. VideoTimelineProvider Context Optimization
```typescript
// Add to VideoTimelineContext.tsx after line 234
const contextValue = useMemo<VideoTimelineContextType>(() => ({
  ...state,
  ...actions,
  isInitialized: true,
}), [state, actions]);
```

#### 3. ReactPlayerWrapper Debouncing
```typescript
// Add debouncing utility and update handleTimeUpdate in ReactPlayerWrapper.tsx
const debouncedTimeUpdate = useMemo(
  () => debounce((time: number) => {
    if (!isSeekingRef.current && isPlayerReady && !videoTimeline.isSeeking) {
      videoTimeline.updateCurrentTime(time, 'video');
    }
  }, 100),
  [videoTimeline, isPlayerReady]
);
```

## Next Steps

1. **Immediate**: Implement Priority 1 fixes (estimated 2-4 hours)
2. **Short-term**: Begin Priority 2 optimizations (estimated 1-2 days)
3. **Long-term**: Plan Priority 3 advanced features (estimated 1 week)
4. **Ongoing**: Monitor performance metrics and user feedback

## Testing Strategy

After implementing each fix:
1. **Performance Testing**: Use React DevTools Profiler to measure re-render reduction
2. **Functional Testing**: Ensure video-timeline synchronization still works correctly
3. **User Testing**: Verify improved responsiveness during timeline interactions
4. **Memory Testing**: Monitor for memory leaks or increased usage
