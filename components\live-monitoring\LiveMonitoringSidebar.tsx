"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import {
  Clock,
  Users,
  Mic,
  MonitorSpeaker,
  Play,
  Square,
  ChevronLeft,
  ChevronRight,
  Activity,
  Volume2
} from "lucide-react"

interface LiveMonitoringSidebarProps {
  isRecordingSession: boolean
  sessionDuration: string
  currentProject: any
  onStartRecording: () => void
  onStopRecording: () => void
  className?: string
}

export function LiveMonitoringSidebar({
  isRecordingSession,
  sessionDuration,
  currentProject,
  onStartRecording,
  onStopRecording,
  className
}: LiveMonitoringSidebarProps) {
  const [isMinimized, setIsMinimized] = useState(false)

  if (isMinimized) {
    return (
      <div className={cn("flex flex-col gap-2 w-16", className)}>
        {/* Expand Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsMinimized(false)}
          className="h-12 w-12 p-0 flex flex-col gap-1"
          title="Expand sidebar"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {/* Quick Session Status */}
        <Button
          variant={isRecordingSession ? "destructive" : "outline"}
          size="sm"
          onClick={isRecordingSession ? onStopRecording : onStartRecording}
          className="h-12 w-12 p-0 flex flex-col gap-1"
          title={isRecordingSession ? "Stop Recording" : "Start Recording"}
        >
          {isRecordingSession ? (
            <>
              <Square className="h-4 w-4" />
              <div className="text-xs">REC</div>
            </>
          ) : (
            <>
              <Play className="h-4 w-4" />
              <div className="text-xs">START</div>
            </>
          )}
        </Button>

        {/* Quick Audio Controls */}
        <Button
          variant="outline"
          size="sm"
          className="h-12 w-12 p-0 flex flex-col gap-1"
          title="Audio Controls"
        >
          <Volume2 className="h-4 w-4" />
          <div className="text-xs">AUDIO</div>
        </Button>

        {/* Quick Team Info */}
        <Button
          variant="outline"
          size="sm"
          className="h-12 w-12 p-0 flex flex-col gap-1"
          title="Team Information"
        >
          <Users className="h-4 w-4" />
          <div className="text-xs">TEAM</div>
        </Button>
      </div>
    )
  }

  return (
    <div className={cn("space-y-4 w-80", className)}>
      {/* Header with Minimize Button */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Session Control</h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsMinimized(true)}
          className="h-8 w-8 p-0"
          title="Minimize sidebar"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Session Controls */}
      <Card className="h-fit">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Clock className="h-4 w-4" />
            Session Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {isRecordingSession ? (
            <>
              <div className="flex items-center justify-center">
                <Badge variant="destructive" className="animate-pulse text-xs">
                  🔴 RECORDING
                </Badge>
              </div>
              <div className="text-center">
                <div className="text-lg font-mono font-bold">{sessionDuration}</div>
                <div className="text-xs text-muted-foreground">Duration</div>
              </div>
              <Button
                onClick={onStopRecording}
                variant="destructive"
                size="sm"
                className="w-full"
              >
                <Square className="h-3 w-3 mr-2" />
                Stop Session
              </Button>
            </>
          ) : (
            <Button
              onClick={onStartRecording}
              variant="default"
              size="sm"
              className="w-full"
            >
              <Play className="h-3 w-3 mr-2" />
              Start Session
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Team Information */}
      <Card className="h-fit">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Users className="h-4 w-4" />
            Team Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-xs">
          <div>
            <div className="font-medium text-muted-foreground uppercase tracking-wide">Surgery</div>
            <div className="text-sm">{currentProject.surgeryType}</div>
          </div>
          <div>
            <div className="font-medium text-muted-foreground uppercase tracking-wide">Patient</div>
            <div className="text-sm">Test Patient</div>
          </div>
          <div>
            <div className="font-medium text-muted-foreground uppercase tracking-wide">Status</div>
            <Badge variant={currentProject.status === "in-progress" ? "default" : "secondary"} className="text-xs">
              {currentProject.status}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Audio Controls */}
      <Card className="h-fit">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Mic className="h-4 w-4" />
            Audio Controls
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-xs">OR Audio</span>
            <Button variant="outline" size="sm" className="h-6 w-6 p-0">
              <MonitorSpeaker className="h-3 w-3" />
            </Button>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs">Team Comm</span>
            <Button variant="outline" size="sm" className="h-6 w-6 p-0">
              <Mic className="h-3 w-3" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <Card className="h-fit">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Activity className="h-4 w-4" />
            Session Stats
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-xs">
          <div className="flex justify-between">
            <span>Events:</span>
            <span className="font-medium">0</span>
          </div>
          <div className="flex justify-between">
            <span>Alerts:</span>
            <span className="font-medium text-orange-600">0</span>
          </div>
          <div className="flex justify-between">
            <span>Critical:</span>
            <span className="font-medium text-red-600">0</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
