# NFM Timeline Editor Usage Guide

## Quick Start

The NFM Timeline Editor is now fully integrated with **direct data integration** - no conversion layer overhead! Here's how to get started:

### Basic Usage

```tsx
import { NFMTimelineEditor, NFMTimelineControls } from '@/components/timeline';
import { useProjectContext } from '@/components/contexts/ProjectContext';

function MyTimelineComponent() {
  const { modalities, events } = useProjectContext();
  const [currentTime, setCurrentTime] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);

  return (
    <div>
      {/* Timeline Controls */}
      <NFMTimelineControls
        currentTime={currentTime}
        duration={3600}
        isPlaying={isPlaying}
        modalities={modalities}
        events={events}
        onSeek={setCurrentTime}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
      />
      
      {/* Timeline Editor */}
      <NFMTimelineEditor
        modalities={modalities}
        events={events}
        currentTime={currentTime}
        duration={3600}
        isPlaying={isPlaying}
        height={300}
        onTimeChange={setCurrentTime}
        onActionCreate={(action, rowId) => {
          console.log('Create event:', action, 'in modality:', rowId);
        }}
        onActionDelete={(actionId) => {
          console.log('Delete event:', actionId);
        }}
      />
    </div>
  );
}
```

### Advanced Configuration

```tsx
<NFMTimelineEditor
  modalities={modalities}
  events={events}
  currentTime={currentTime}
  duration={duration}
  config={{
    // Grid settings
    snapToGrid: true,
    gridInterval: 5, // 5 second intervals
    
    // Interaction settings
    allowCreate: true,
    allowDelete: true,
    allowDrag: true,
    allowResize: true,
    
    // Visual settings
    rowHeight: 40,
    cursorColor: '#ef4444',
    selectionColor: '#3b82f6',
    
    // Features
    enableKeyboardShortcuts: true,
    enableContextMenu: true,
    enableTooltips: true
  }}
  onError={(error) => {
    toast.error('Timeline error: ' + error.message);
  }}
  onValidationError={(errors) => {
    console.warn('Validation errors:', errors);
  }}
/>
```

## Key Features

### 1. Event Creation
- **Click on timeline**: Creates a new event at the clicked time
- **Default duration**: 1 second (configurable)
- **Automatic modality assignment**: Based on the row clicked

### 2. Event Editing
- **Drag to move**: Click and drag events to new positions
- **Resize**: Drag the edges to change event duration
- **Selection**: Click to select, Ctrl+click for multi-select
- **Delete**: Press Delete or Backspace key

### 3. Navigation
- **Zoom**: Mouse wheel + Ctrl/Cmd to zoom in/out
- **Scroll**: Mouse wheel to scroll horizontally
- **Keyboard**: Arrow keys to seek, Space to play/pause

### 4. Modality Controls
- **Visibility**: Toggle modality visibility with multi-select
- **Color coding**: Each modality has its own color
- **Event counts**: Shows number of events per modality

## Event Handlers

### Data Change Handlers
```tsx
onDataChange={(newData) => {
  // Handle timeline data changes
  console.log('Timeline data updated:', newData);
}}

onActionCreate={(action, rowId) => {
  // Handle new event creation
  createEvent({
    startTime: action.start,
    endTime: action.end,
    modalityId: rowId,
    // ... other properties
  });
}}

onActionUpdate={(action) => {
  // Handle event updates (move, resize)
  updateEvent(action.id, {
    startTime: action.start,
    endTime: action.end,
  });
}}

onActionDelete={(actionId) => {
  // Handle event deletion
  deleteEvent(actionId);
}}
```

### Interaction Handlers
```tsx
onActionClick={(action, event) => {
  // Handle event clicks
  console.log('Event clicked:', action);
}}

onActionDoubleClick={(action, event) => {
  // Handle event double-clicks (e.g., open edit dialog)
  openEventEditDialog(action);
}}

onActionContextMenu={(action, event) => {
  // Handle right-click context menu
  showContextMenu(action, event.clientX, event.clientY);
}}

onTimelineClick={(time, rowId) => {
  // Handle timeline background clicks
  if (rowId) {
    createEventAtTime(time, rowId);
  }
}}
```

### Playback Handlers
```tsx
onTimeChange={(time) => {
  // Sync with video player
  videoPlayer.seekTo(time);
}}

onPlayStateChange={(isPlaying) => {
  // Sync playback state
  if (isPlaying) {
    videoPlayer.play();
  } else {
    videoPlayer.pause();
  }
}}
```

## Keyboard Shortcuts

| Key | Action |
|-----|--------|
| `Space` | Play/Pause |
| `←` `→` | Seek backward/forward (5s) |
| `Ctrl/Cmd + ←` `→` | Seek backward/forward (30s) |
| `Ctrl/Cmd + Wheel` | Zoom in/out |
| `Delete` / `Backspace` | Delete selected events |
| `Escape` | Clear selection |
| `Ctrl/Cmd + A` | Select all events |
| `Ctrl/Cmd + Z` | Undo (if enabled) |
| `Ctrl/Cmd + Y` | Redo (if enabled) |

## Styling Customization

### CSS Custom Properties
```css
.timeline-editor-nfm {
  --cursor-color: #ef4444;
  --selection-color: #3b82f6;
  --row-height: 40px;
  --grid-color: #e5e7eb;
}
```

### Custom Event Renderers
```tsx
// Create custom renderer for specific event types
function CustomEventRenderer({ action, row, isSelected, scale, height }) {
  return (
    <div 
      className="custom-event"
      style={{
        width: (action.end - action.start) * scale,
        height: height - 4,
        backgroundColor: action.color || '#3b82f6'
      }}
    >
      {action.title}
    </div>
  );
}

// Use in timeline editor
<NFMTimelineEditor
  getActionRender={(action, row) => {
    if (action.eventType === 'custom') {
      return <CustomEventRenderer action={action} row={row} />;
    }
    // Return null to use default renderer
    return null;
  }}
/>
```

## Performance Tips

### Large Datasets
- Enable virtualization for many events: `config.virtualizeRows = true`
- Limit visible rows: `config.maxVisibleRows = 50`
- Use debounced updates: Built into `useTimelineData` hook

### Memory Management
- The timeline automatically cleans up event listeners
- Undo/redo stack is limited to 50 steps by default
- Use `React.memo` for custom renderers if needed

### Smooth Animations
- CSS animations are GPU-accelerated
- Transitions are optimized for 60fps
- Large datasets automatically reduce animation complexity

## Troubleshooting

### Common Issues

1. **Events not appearing**: Check that modalities are visible and events have valid time ranges
2. **Performance issues**: Reduce the number of visible events or enable virtualization
3. **Styling conflicts**: Ensure timeline CSS is loaded after other stylesheets
4. **Type errors**: Make sure all event data matches the expected TypeScript interfaces

### Debug Mode
```tsx
<NFMTimelineEditor
  onError={(error) => {
    console.error('Timeline error:', error);
    // Send to error reporting service
  }}
  onValidationError={(errors) => {
    console.warn('Validation errors:', errors);
    // Show user-friendly warnings
  }}
/>
```

### Performance Monitoring
```tsx
const { 
  editorData, 
  isValid, 
  errors, 
  changeCount 
} = useTimelineData(modalities, events, {
  validateOnChange: true,
  enableChangeTracking: true
});

// Monitor performance
console.log('Timeline stats:', {
  eventCount: editorData.flatMap(row => row.actions).length,
  isValid,
  errors,
  changeCount
});
```

## Migration from Old Timeline

The new timeline editor maintains API compatibility with the old implementation:

```tsx
// Old usage (still works)
<TimelineContainer
  currentTime={currentTime}
  duration={duration}
  events={events}
  modalities={modalities}
  onSeek={handleSeek}
  // ... other props
/>

// New usage (enhanced features)
<>
  <NFMTimelineControls {...controlProps} />
  <NFMTimelineEditor {...editorProps} />
</>
```

Key differences:
- Split into separate controls and editor components
- Enhanced keyboard shortcuts and interactions
- Better performance with large datasets
- More customization options
- Improved accessibility
