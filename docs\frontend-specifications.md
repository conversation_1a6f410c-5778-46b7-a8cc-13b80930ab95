# Frontend Component Specifications
## NFM System - Detailed UI/UX Implementation Guide

### 🎯 Overview

This document provides component-level specifications for implementing the NFM user interface, based on the design.pdf mockups and detailed requirements. Each section includes layout specifications, component behavior, styling requirements, and interaction patterns that can be directly implemented by developers.

---

## 🗂️ Application Layout Structure

### Root Layout Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    NFM System Header                        │
│ ┌─ Hospital Logo ──┬── Page Title ──┬── User Profile ─┐    │
│ │  [Logo Image]    │  "Live View"   │  Dr. Smith ▼   │    │
│ └─────────────────┴────────────────┴─────────────────┘    │
├─────────────────────────────────────────────────────────────┤
│ ┌─ Sidebar ─┐ ┌──────── Main Content Area ─────────────────┐ │
│ │           │ │                                            │ │
│ │ [Status]  │ │                                            │ │
│ │ [Nav]     │ │            Page Content                    │ │
│ │ [Menu]    │ │                                            │ │
│ │           │ │                                            │ │
│ │           │ │                                            │ │
│ └───────────┘ └────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

**Header Component Specifications:**
- **Height**: 64px fixed
- **Background**: White with bottom border (border-gray-200)
- **Hospital Logo**: Left-aligned, 40px height, 150px max width
- **Page Title**: Center-aligned, text-xl font-semibold
- **User Profile**: Right-aligned dropdown with avatar, name, role badge
- **Live Status Indicator**: Red/green dot next to title when on live pages

---

## 🔄 Dual Sidebar Architecture

### Overview

The NFM system implements a dual sidebar architecture to maximize screen real estate and provide contextual information based on the current page. This system consists of:

1. **Left Sidebar (Primary Navigation)**: Always visible, contains main navigation, user controls, and system status
2. **Right Sidebar (Contextual Content)**: Page-specific content that can be collapsed/expanded

### Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                Header                                               │
│                          [Right Sidebar Toggle]                                    │
├─────────────────────────────────────────────────────────────────────────────────────┤
│ ┌─ Left ─┐ ┌──────────── Main Content ────────────┐ ┌─ Right Sidebar ─┐           │
│ │ Sidebar│ │                                      │ │ (Contextual)    │           │
│ │        │ │                                      │ │                 │           │
│ │ [Nav]  │ │         Page Content                 │ │ • Session Ctrl  │           │
│ │ [User] │ │                                      │ │ • Event Log     │           │
│ │ [Sys]  │ │                                      │ │ • Page Tools    │           │
│ │        │ │                                      │ │                 │           │
│ └────────┘ └──────────────────────────────────────┘ └─────────────────┘           │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### Implementation Guide

#### 1. Setting Up Dual Sidebar Layout

```typescript
// app/dashboard/layout.tsx
import { SidebarProvider } from "@/components/ui/sidebar"
import { SidebarLeft } from "@/components/layout/sidebar-left"
import { SidebarRight } from "@/components/layout/sidebar-right"

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const [isRightSidebarCollapsed, setIsRightSidebarCollapsed] = useState(true)

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        {/* Left Sidebar - Always visible */}
        <SidebarLeft />

        {/* Main Content Area */}
        <SidebarInset className="flex flex-1 flex-col">
          <Header
            onToggleRightSidebar={() => setIsRightSidebarCollapsed(!isRightSidebarCollapsed)}
            isRightSidebarCollapsed={isRightSidebarCollapsed}
            showRightSidebarTrigger={true}
          />

          <div className="flex flex-1 overflow-hidden">
            <main className="flex-1 overflow-y-auto">
              {children}
            </main>

            {/* Right Sidebar - Contextual */}
            <SidebarRight
              isCollapsed={isRightSidebarCollapsed}
              currentPage="live-monitoring" // Determined by route
            />
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  )
}
```

#### 2. Creating Page-Specific Right Sidebar Content

```typescript
// components/right-sidebar-content/live-monitoring-content.tsx
export function LiveMonitoringContent({ isCollapsed }: { isCollapsed: boolean }) {
  if (isCollapsed) {
    return (
      <div className="flex flex-col items-center gap-4 p-2">
        {/* Minimized icons */}
        <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary">
          <Activity className="size-4" />
        </div>
      </div>
    )
  }

  return (
    <>
      {/* Header */}
      <div className="h-16 border-b p-3">
        <h3 className="font-semibold">Session Control</h3>
      </div>

      {/* Scrollable Content */}
      <ScrollArea className="flex-1">
        <div className="space-y-4 p-3">
          {/* Session Controls */}
          <SessionControlCard />

          {/* Event Log */}
          <EventLogSection />

          {/* Page-specific tools */}
        </div>
      </ScrollArea>
    </>
  )
}
```

#### 3. Adding New Pages to Right Sidebar System

To add right sidebar content for a new page:

1. **Create content component**:
   ```typescript
   // components/right-sidebar-content/[page-name]-content.tsx
   export function PageNameContent({ isCollapsed }: { isCollapsed: boolean }) {
     // Implementation
   }
   ```

2. **Register in right sidebar router**:
   ```typescript
   // components/layout/sidebar-right.tsx
   const renderContent = () => {
     switch (currentPage.toLowerCase()) {
       case "live-monitoring":
         return <LiveMonitoringContent isCollapsed={isCollapsed} />
       case "evaluation":
         return <EvaluationContent isCollapsed={isCollapsed} />
       case "your-new-page":
         return <YourNewPageContent isCollapsed={isCollapsed} />
       default:
         return null
     }
   }
   ```

3. **Update pages list**:
   ```typescript
   const pagesWithRightSidebar = ["live-monitoring", "evaluation", "your-new-page"]
   ```

### Right Sidebar Content Guidelines

#### Session Control Section
- **Purpose**: Control active monitoring sessions
- **Components**: Start/stop buttons, session timer, status indicators
- **Visibility**: Live monitoring and evaluation pages

#### Event Log Section
- **Purpose**: Display and manage timeline events
- **Components**: Event list, filters, quick actions
- **Features**:
  - Real-time event updates
  - Severity-based color coding
  - Quick review actions
  - Export functionality

#### Page-Specific Tools
- **Live Monitoring**: Audio controls, team information, session stats
- **Evaluation**: Playback controls, annotation tools, comparison views
- **Reports**: Export options, template selection, sharing controls

### Responsive Behavior

#### Desktop (≥1024px)
- Left sidebar: Always visible, collapsible to icons
- Right sidebar: Toggleable, 320px width when expanded
- Main content: Dynamically resizes based on sidebar states

#### Tablet (768px - 1023px)
- Left sidebar: Collapsible overlay
- Right sidebar: Hidden by default, overlay when opened
- Main content: Full width when sidebars closed

#### Mobile (<768px)
- Left sidebar: Sheet overlay from left
- Right sidebar: Sheet overlay from right
- Main content: Full screen
- Header: Contains both sidebar triggers

---

## 📊 Left Sidebar Component (Primary Navigation)

### Sidebar Layout Specification

```typescript
interface SidebarProps {
  currentPath: string;
  liveSession?: LiveSession | null;
  isCollapsed?: boolean;
}

interface LiveSession {
  id: string;
  location: string; // "OR 12"
  status: 'online' | 'offline' | 'recording';
  startTime: number;
  currentDuration: string; // "02:45:30"
}
```

**Sidebar Structure (Top to Bottom):**

### 1. Live Session Status Card
```
┌─────────────────────────────────┐
│ 🔴 LIVE SESSION                │
│ OR 12 - Spinal Surgery         │
│ ⏱️ 02:45:30                     │
│ 👥 Dr. Smith, Dr. Johnson       │
│ [STOP] [PAUSE]                  │
└─────────────────────────────────┘
```

**Specifications:**
- **Position**: Fixed at top of sidebar
- **Background**: Green gradient (live) or gray (offline)
- **Padding**: 16px
- **Border Radius**: 8px
- **Margin**: 16px from top/sides
- **Status Indicator**: Animated red dot when recording
- **Duration**: Updates every second, format HH:MM:SS
- **Team Display**: Max 2 names, "& X more" if exceeds
- **Action Buttons**: Only show when user has permission

### 2. Navigation Menu
```
┌─────────────────────────────────┐
│ 📊 Projects                     │
│ 🔴 Live Monitoring              │
│ 📝 Evaluation                   │
│ 👥 Patients                     │
│ ⚙️ Settings                     │
│ 📋 Reports                      │
│ 📊 Analytics                    │
└─────────────────────────────────┘
```

**Navigation Item Specifications:**
- **Height**: 48px each
- **Padding**: 12px 16px
- **Hover State**: bg-gray-100, cursor pointer
- **Active State**: bg-blue-50, blue left border (4px)
- **Icon**: 20px Lucide React icons
- **Text**: text-sm font-medium
- **Badge Support**: For counts (e.g., "3 Active")

### 3. Quick Actions (Bottom)
```
┌─────────────────────────────────┐
│ 🆘 Emergency Stop               │
│ 📞 Call Neurophysiologist       │
│ 📋 Export Current Session       │
└─────────────────────────────────┘
```

**Component Implementation:**
```typescript
// components/layout/Sidebar.tsx
export function Sidebar({ currentPath, liveSession, isCollapsed }: SidebarProps) {
  return (
    <div className="w-64 bg-white border-r border-gray-200 h-full flex flex-col">
      {/* Live Session Status */}
      {liveSession && (
        <LiveSessionCard session={liveSession} />
      )}
      
      {/* Navigation Menu */}
      <nav className="flex-1 px-4 py-6">
        <NavigationItems currentPath={currentPath} />
      </nav>
      
      {/* Quick Actions */}
      <div className="border-t border-gray-200 p-4">
        <QuickActions />
      </div>
    </div>
  );
}
```

---

## 📋 Projects Overview Page

### Projects Table Component

**Based on shadcn-table implementation with medical-specific columns:**

```typescript
interface ProjectTableProps {
  projects: Project[];
  onProjectSelect: (project: Project) => void;
  onProjectAction: (projectId: string, action: ProjectAction) => void;
}

interface Project {
  id: string;
  patientName: string;
  patientId: string;
  surgeon: string;
  surgeryType: string;
  operatingRoom: string;
  scheduledStart: Date;
  status: 'scheduled' | 'pre-op' | 'live' | 'post-op' | 'completed';
  duration?: string;
  complications?: number;
}
```

### Table Layout Specification

**Header Row:**
```
┌─ Status ─┬─ Patient ─────┬─ Surgeon ─────┬─ Surgery Type ─┬─ OR ─┬─ Scheduled ─┬─ Actions ─┐
│   🟢     │ Smith, John   │ Dr. Johnson   │ Spinal Fusion  │ 12  │ 14:30      │  ⋮       │
│          │ #P2024-001    │ Neurosurgery  │                │     │ 2h 45m     │           │
└─────────┴───────────────┴───────────────┴────────────────┴─────┴────────────┴───────────┘
```

**Column Specifications:**

1. **Status Column** (80px):
   - Color-coded dots: 🔴 Live, 🟡 Pre-op, 🟢 Completed, ⚪ Scheduled
   - Tooltip on hover with full status

2. **Patient Column** (200px):
   - **Primary**: Patient name (font-medium)
   - **Secondary**: Patient ID in gray (text-sm)
   - Clickable to open patient details

3. **Surgeon Column** (180px):
   - **Primary**: Surgeon name
   - **Secondary**: Department/specialization

4. **Surgery Type Column** (160px):
   - **Primary**: Surgery type
   - **Badge**: Color-coded by complexity

5. **Operating Room Column** (60px):
   - **Large text**: Room number
   - **Background**: Color coded by room

6. **Scheduled Column** (120px):
   - **Primary**: Time (HH:MM)
   - **Secondary**: Duration estimate

7. **Actions Column** (100px):
   - **Dropdown menu** with context actions

### Search and Filter Bar

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🔍 Search patients, surgeons...     [Status ▼] [Room ▼] [Date ▼] [+] New   │
└─────────────────────────────────────────────────────────────────────────────┘
```

**Filter Specifications:**
- **Search Input**: Full-width, debounced search (300ms)
- **Status Filter**: Multi-select dropdown
- **Room Filter**: OR room selector
- **Date Filter**: Date range picker
- **New Button**: Primary button, opens project creation modal

### Project Action Modal

**Triggered when clicking table row or actions dropdown:**

```
┌──────────────────────────────────────────────────────────┐
│                    Project Actions                       │
├──────────────────────────────────────────────────────────┤
│  📂 Open Project Details                                │
│  👤 View Patient Information                            │
│  🔴 Start Live Monitoring                               │
│  📊 Open Evaluation Mode                                │
│  📋 Generate Report                                     │
│  ✏️ Edit Project                                        │
│  📧 Send to Colleagues                                  │
├──────────────────────────────────────────────────────────┤
│                    [Cancel] [Select]                    │
└──────────────────────────────────────────────────────────┘
```

**Modal Specifications:**
- **Width**: 400px
- **Position**: Center screen
- **Animation**: Fade in with scale
- **Actions**: Icon + text, hover states
- **Keyboard**: Arrow navigation, Enter to select, Esc to close

---

## 🔴 LIVE VIEW PAGE - Core Application Interface

### Overall Layout Structure (Dual Sidebar Architecture)

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ Live View - OR 12 - Spinal Fusion Surgery                    [Right Sidebar Toggle] │
├─────────────────────────────────────────────────────────────────────────────────────┤
│ ┌─ Left ─┐ ┌──────────── Main Content ────────────┐ ┌─ Right Sidebar ─┐           │
│ │ Sidebar│ │                                      │ │ Session Control │           │
│ │        │ │  ┌─── VIDEO STREAM ─────────────────┐ │ │ 🔴 RECORDING    │           │
│ │ [Nav]  │ │  │                                  │ │ │ ⏱️ 02:45:30     │           │
│ │ [User] │ │  │      Inomed Interface            │ │ │ [STOP]          │           │
│ │ [Sys]  │ │  │                                  │ │ ├─────────────────┤           │
│ │        │ │  │ 📸 Screenshot  🔍 Zoom  ⛶ Full  │ │ │ Event Log       │           │
│ │        │ │  └──────────────────────────────────┘ │ │ 🟡 EMG Alert    │           │
│ │        │ │                                      │ │ │ 14:32:15       │           │
│ │        │ │  ┌─── TIMELINE ──────────────────────┐ │ │ [Review]       │           │
│ │        │ │  │ ⏮️ ▶️ ⏭️ │ ➖ ➕ │ [x1.0 Speed] │ │ ├─────────────────┤           │
│ │        │ │  ├────────────────────────────────────┤ │ │ 🔴 MEP Loss     │           │
│ │        │ │  │ EMG  ████████████████████████████ │ │ │ 14:28:45       │           │
│ │        │ │  │ MEP  ████████████████████████████ │ │ │ ⚠️ Critical     │           │
│ │        │ │  │ SSEP ████████████████████████████ │ │ │ [Review]       │           │
│ │        │ │  │      🔴   🟡  🟢    🔴           │ │ ├─────────────────┤           │
│ │        │ │  │      ↑    ↑   ↑     ↑            │ │ │ [Filter][Export]│           │
│ │        │ │  │   MEP   EMG Normal Current        │ │ │                 │           │
│ │        │ │  └────────────────────────────────────┘ │ └─────────────────┘           │
│ │        │ │                                      │ │                               │
│ └────────┘ └──────────────────────────────────────┘ └───────────────────────────────┘
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### Key Layout Changes

#### Optimized Screen Usage
- **Video Stream**: Now takes up ~45% of viewport height for better visibility
- **Timeline**: Occupies remaining ~55% of viewport height for detailed event analysis
- **Right Sidebar**: Contains session controls and event log (320px width when expanded)
- **No Horizontal Waste**: Eliminates unused space, maximizes content area

#### Responsive Behavior
- **Desktop**: Dual sidebar layout with collapsible right sidebar
- **Tablet**: Right sidebar becomes overlay, left sidebar collapsible
- **Mobile**: Both sidebars become sheet overlays, content takes full screen

### Video Stream Component

**Component Specifications:**
```typescript
interface LiveVideoPlayerProps {
  streamUrl: string;
  isRecording: boolean;
  onScreenshot: (timestamp: number) => void;
  onTimeUpdate: (currentTime: number) => void;
  isPaused?: boolean;
  className?: string;
}
```

**Video Container Layout:**
```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                    VIDEO STREAM                             │
│                 16:9 Aspect Ratio                           │
│                                                             │
│                                                             │
│  ┌─── Controls Overlay (on hover) ──────────────────────┐   │
│  │ ⏸️  🔊  📸  🔍  ⛶    🔴 LIVE  •  Connection: Good   │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

**Video Player Specifications:**
- **Aspect Ratio**: 16:9 (maintains Inomed interface proportions)
- **Background**: Black (#000000)
- **Controls**: Hidden by default, show on hover
- **Screenshot**: Captures full frame at current timestamp
- **Status Indicators**: Connection quality, recording status, live indicator
- **Fullscreen**: Supports browser fullscreen API
- **Loading State**: Spinner with "Connecting to stream..." message

**Control Buttons:**
- **Play/Pause**: Only for evaluation mode (hidden during live)
- **Volume**: Mute/unmute with slider
- **Screenshot**: 📸 icon, saves with timestamp
- **Zoom**: Picture-in-picture mode
- **Fullscreen**: ⛶ icon, enters browser fullscreen

### Timeline Component (Core Feature)

**Timeline Header Controls:**
```
┌─ Timeline Controls ─────────────────────────────────────────────────┐
│ [🔍-] [🔍+] [⟲ Fit] [→| Next] [📈 Expand] ⏰ 02:45:30 / 03:00:00   │
└─────────────────────────────────────────────────────────────────────┘
```

**Control Specifications:**
- **Zoom Out** [🔍-]: Decrease time scale (show more time)
- **Zoom In** [🔍+]: Increase time scale (show less time, more detail)
- **Fit to View** [⟲]: Auto-scale to show all events
- **Next Event** [→|]: Jump to next event on timeline
- **Expand View** [📈]: Toggle single vs multi-row modality view
- **Time Display**: Current time / total duration

**Timeline Track Layout (Zoomed In - Duration View):**
```
┌─ Timeline Tracks (Duration Bars) ──────────────────────────────────────────────┐
│ EMG  ████████████████████████████████████████████████████████████████████████ │
│      ╔══════╗        ╔════════════╗                                           │
│      14:32   14:34    14:48        14:52                                      │
│                                                                               │
│ MEP  ████████████████████████████████████████████████████████████████████████ │
│      ╔═══════════╗   ╔══════╗                                                │
│      14:28  14:35     14:42  14:44                                            │
│                                                                               │
│ SSEP ████████████████████████████████████████████████████████████████████████ │
│      ╔═══╗                   ╔═══════════════╗                               │
│      14:35 14:36              14:55           15:02                           │
└────────────────────────────────────────────────────────────────────────────────┘
```

**Event Duration Specifications:**
- **Duration Bars**: Show when zoomed in (< 60s per 100px scale)
- **Start/End Times**: Events have both startTime and endTime
- **Bar Height**: 50% of track height, centered vertically
- **Bar Colors**: Match modality colors with transparency for readability
- **Duration Labels**: Show startTime and endTime when space permits
- **Transition**: Smooth animation between dot view and duration bar view

**Timeline Track Layout (Compact View):**
```
┌─ Timeline Tracks ──────────────────────────────────────────────────────────────┐
│ All  ████████████████████████████████████████████████████████████████████████ │
│      🔴    🟡      🟢    🔴       🟡               🟢                         │
│      14:28 14:32   14:35 14:42    14:48           14:55                      │
└────────────────────────────────────────────────────────────────────────────────┘
```

**Timeline Track Layout (Expanded View):**
```
┌─ Timeline Tracks (Expanded) ───────────────────────────────────────────────────┐
│ EMG  ████████████████████████████████████████████████████████████████████████ │
│      🟡              🟡                                                       │
│      14:32           14:48                                                    │
│                                                                               │
│ MEP  ████████████████████████████████████████████████████████████████████████ │
│      🔴              🔴                                                       │
│      14:28           14:42                                                    │
│                                                                               │
│ SSEP ████████████████████████████████████████████████████████████████████████ │
│      🟢                          🟢                                          │
│      14:35                       14:55                                       │
└────────────────────────────────────────────────────────────────────────────────┘
```

**Timeline Specifications:**

1. **Track Height**: 
   - Compact: 60px total
   - Expanded: 40px per modality

2. **Time Scale**: 
   - Default: 60 seconds per 100px
   - Zoom levels: 15s, 30s, 60s, 120s, 300s per 100px

3. **Event Markers**:
   - **Size**: 12px diameter circles (when zoomed out)
   - **Duration Bars**: When zoomed in (< 60s per 100px), show as colored bars with startTime to endTime
   - **Colors**: Configurable per modality (EMG: yellow, MEP: red, SSEP: green)
   - **Hover Effect**: Scale to 16px, show tooltip with duration
   - **Click Action**: Open event review popup
   - **Right-click**: Show context menu (View, Edit, Delete)
   - **Duration**: Events have startTime and endTime for medical analysis

4. **Current Time Indicator**:
   - **Style**: Red vertical line (2px width)
   - **Height**: Full timeline height
   - **Animation**: Smooth movement during playback
   - **Dragging**: Real-time position updates while dragging
   - **Auto-scroll**: Timeline scrolls to keep indicator visible
   - **Interaction**: Click and drag to seek, smooth following

5. **Grid Lines**:
   - **Major**: Every minute (thick line, opacity 0.3)
   - **Minor**: Every 15 seconds (thin line, opacity 0.1)
   - **Labels**: Time stamps every major grid line

6. **Interaction**:
   - **Click**: Seek to time position
   - **Drag Indicator**: Real-time time seeking with smooth following
   - **Scroll**: Horizontal scroll when zoomed (mouse wheel)
   - **Keyboard**: Ctrl+scroll for zoom, arrow keys for seek, space for play/pause
   - **Double-click**: Create new event at position
   - **Right-click Event**: Context menu with View/Edit/Delete options
   - **Auto-scroll**: Timeline follows current time indicator and next event navigation
   - **Scroll Buttons**: Manual scroll controls at timeline edges
   - **Modality Navigation**: Hover modality labels to show next/previous event arrows

### Event Creation Buttons

**Quick Action Bar:**
```
┌─ Event Creation ───────────────────────────────────────────────────────────────┐
│ [🔴 MEP Event] [🟡 EMG Alert] [🟢 SSEP Check] [⚪ DES Stimulation] [+ Custom]  │
│     Critical      Warning       Normal        Procedure          New Event    │
└────────────────────────────────────────────────────────────────────────────────┘
```

**Button Specifications:**
- **Height**: 48px
- **Padding**: 12px 20px
- **Border Radius**: 6px
- **Font**: text-sm font-medium
- **Icon**: 16px colored circle matching modality
- **Hover**: Slight scale (1.02) and shadow increase
- **Active**: Pressed state with scale (0.98)
- **Configuration**: Buttons based on surgery type template

**Event Creation Flow:**
1. User clicks event button
2. Event immediately created on timeline at current time
3. Event review popup opens automatically
4. User can edit details or close to save basic event

### Event Review Popup (Critical Component)

**Large Popup Mode (Full Screen):**
```
┌──────────────────────────────────────────────────────────────────────────────────┐
│ Event Review - MEP Loss Event                                           [✕]     │
├──────────────────────────────────────────────────────────────────────────────────┤
│                                                                                  │
│  ┌─── Event Video Player ─────────────────┐  ┌─── Event Details ──────────────┐ │
│  │                                        │  │ Event Type: MEP Loss           │ │
│  │        Video Clip                      │  │ Severity: ⚠️ Critical          │ │
│  │     [Current Frame]                    │  │ Time: 14:28:45                 │ │
│  │                                        │  │ Duration: 10s before/after     │ │
│  │  ⏮️ [-5s] [-1s] ⏸️ [+1s] [+5s] ⏭️      │  │                                │ │
│  │  📸 Screenshot     🎬 Export Clip      │  │ Modality: MEP                  │ │
│  │                                        │  │ Location: L5 Nerve Root        │ │
│  └────────────────────────────────────────┘  │                                │ │
│                                              │ Error Type:                    │ │
│  ┌─── Screenshots ────────────────────────┐  │ [Dropdown ▼] Signal Loss       │ │
│  │ [📸] [📸] [📸] [📸] [+]                │  │                                │ │
│  │  :45  :50  :55  :00   New              │  │ Description:                   │ │
│  └────────────────────────────────────────┘  │ ┌─────────────────────────────┐ │
│                                              │ │ Sudden loss of MEP signal   │ │
│                                              │ │ in left tibialis anterior   │ │
│                                              │ │ during decompression...     │ │
│                                              │ │                             │ │
│                                              │ └─────────────────────────────┘ │
│                                              │                                │ │
│                                              │ Reviewed by: Dr. Johnson       │ │
│                                              │ Status: ⏳ Under Review        │ │
│                                              └────────────────────────────────┘ │
│                                                                                  │
│ [🗑️ Delete] [📤 Share] [💾 Save & Close] [✅ Mark Reviewed] [➡️ Next Event]    │
└──────────────────────────────────────────────────────────────────────────────────┘
```

**Small Popup Mode (Side Panel):**
```
┌─ Event Review ────────┐
│ MEP Loss - 14:28:45   │
├───────────────────────┤
│ [📸] Video frame      │
│                       │
│ Severity: Critical ▼  │
│ Type: Signal Loss ▼   │
│                       │
│ Notes:                │
│ ┌───────────────────┐ │
│ │ Brief description │ │
│ └───────────────────┘ │
│                       │
│ [Save] [Review]       │
└───────────────────────┘
```

**Event Review Specifications:**

1. **Popup Modes**:
   - **Large**: 80% screen width/height, pauses live stream
   - **Small**: 300px width, right sidebar, stream continues
   - **Mode Setting**: User preference in settings

2. **Video Player Controls**:
   - **Time Range**: Default 10s before/after event
   - **Extend Controls**: [-5s] [-1s] [+1s] [+5s] buttons
   - **Frame Navigation**: Arrow keys for frame-by-frame
   - **Screenshot**: Capture current frame with timestamp

3. **Event Details Form**:
   - **Event Type**: Dropdown with modality-specific options
   - **Severity**: Critical/Warning/Normal with color coding
   - **Location**: Muscle/nerve/area affected
   - **Description**: Auto-growing textarea
   - **Auto-save**: Saves on every change after 1s delay

4. **Screenshots Gallery**:
   - **Thumbnail Grid**: 4 screenshots per row
   - **Timestamp Labels**: Below each thumbnail
   - **Click to Enlarge**: Modal view of full screenshot
   - **Add New**: Camera button to capture current frame

5. **Action Buttons**:
   - **Delete**: Confirmation required
   - **Share**: Send to team member with comment
   - **Save & Close**: Return to live view
   - **Mark Reviewed**: Changes status, adds reviewer
   - **Next Event**: Navigate to next unreviewed event

### Right Sidebar - Live Monitoring Content

#### Session Control Section

**Session Status Card:**
```
┌─ Session Control ───────────┐
│ 🔴 RECORDING                │
│ ⏱️ 02:45:30                 │
│ [STOP SESSION]              │
├─────────────────────────────┤
│ Surgery: Spinal Fusion      │
│ Patient: Test Patient       │
│ Status: in-progress         │
└─────────────────────────────┘
```

**Audio Controls:**
```
┌─ Audio Controls ────────────┐
│ OR Audio     [🔊]           │
│ Team Comm    [🎤]           │
└─────────────────────────────┘
```

**Session Statistics:**
```
┌─ Session Stats ─────────────┐
│ Events: 12                  │
│ Alerts: 3                   │
│ Critical: 2                 │
└─────────────────────────────┘
```

#### Event Log Section

**Event Log Layout:**
```
┌─ Event Log ─────────────────┐
│ [🔍 Filter] [📤 Export]     │
├─────────────────────────────┤
│ 🔴 MEP Loss        14:28:45 │
│ L5 nerve root      ⚠️ Critical │
│ Dr. Johnson        [Review] │
├─────────────────────────────┤
│ 🟡 EMG Alert       14:32:15 │
│ Tibialis anterior  ⚠️ Warning │
│ Unreviewed         [Review] │
├─────────────────────────────┤
│ 🟢 SSEP Normal     14:35:30 │
│ Routine check      ✅ Normal │
│ Dr. Smith          [View]   │
├─────────────────────────────┤
│ 🔴 MEP Loss        14:42:10 │
│ L5 nerve root      ⚠️ Critical │
│ Under review       [Review] │
├─────────────────────────────┤
│                             │
│     [Load More...]          │
└─────────────────────────────┘
```

**Event Log Item Specifications:**

1. **Event Card Layout**:
   - **Height**: 80px per event
   - **Padding**: 12px
   - **Border**: Bottom border between events
   - **Hover**: Light gray background

2. **Event Information**:
   - **Line 1**: Modality icon + event type + timestamp
   - **Line 2**: Location/muscle + severity badge
   - **Line 3**: Reviewer + action button

3. **Severity Badges**:
   - **Critical**: Red background, white text
   - **Warning**: Yellow background, dark text
   - **Normal**: Green background, white text

4. **Interaction**:
   - **Click Event**: Highlight on timeline + scroll into view
   - **Timeline Sync**: Hovering event highlights timeline marker
   - **Review Button**: Opens event review popup
   - **Auto-scroll**: New events auto-scroll to top

5. **Filtering Options**:
   - **By Modality**: EMG, MEP, SSEP checkboxes
   - **By Severity**: Critical, Warning, Normal
   - **By Status**: Reviewed, Unreviewed, Under Review
   - **By Time Range**: Last hour, session, custom range

#### Collapsed State

When right sidebar is collapsed (16px width), show only:
- Session status icon (recording indicator)
- Team icon
- Audio icon
- Event log icon

Each icon should have tooltip showing current status.

---

## 🎛️ Enhanced Timeline Controls Specification

### Timeline Controls Layout (Phase 2.3.2 Implementation)

**Enhanced Control Bar:**
```
┌─────────────────────────────────────────────────────────────────┐
│  ⏮️  ▶️  ⏭️  │  ➖  ➕  │  [x1.0 Playback Speed]                │
│ Prev Play Next │ Zoom Out In │   (Interactive Speed Control)      │
└─────────────────────────────────────────────────────────────────┘
```

### Control Component Specifications

#### **1. Event Navigation Controls**
```typescript
interface EventNavigationProps {
  currentTime: number;
  events: MonitoringEvent[];
  onPrevEvent: () => void;
  onNextEvent: () => void;
  onScrollToTime: (time: number) => void;
}
```

**Previous Event Button:**
- **Icon**: ⏮️ (SkipBack)
- **Behavior**: Finds previous event chronologically before current time
- **Auto-scroll**: Centers target event in timeline viewport
- **State**: Disabled when no previous events exist
- **Tooltip**: "Previous Event" / "No previous events"

**Next Event Button:**
- **Icon**: ⏭️ (SkipForward)
- **Behavior**: Finds next event chronologically after current time
- **Auto-scroll**: Centers target event in timeline viewport
- **State**: Disabled when no upcoming events exist
- **Tooltip**: "Next Event" / "No upcoming events"

#### **2. Zoom Controls**
```typescript
interface ZoomControlsProps {
  scale: number;
  onZoomIn: () => void;
  onZoomOut: () => void;
  minScale?: number;
  maxScale?: number;
}
```

**Zoom Out Button (-):**
- **Icon**: ➖ (Minus)
- **Behavior**: Decreases scale (shows more time, less detail)
- **Scale Factor**: Divides current scale by 1.2
- **Limits**: Minimum scale of 10
- **Tooltip**: "Zoom Out (-)"

**Zoom In Button (+):**
- **Icon**: ➕ (Plus)
- **Behavior**: Increases scale (shows less time, more detail)
- **Scale Factor**: Multiplies current scale by 1.2
- **Limits**: Maximum scale of 500
- **Tooltip**: "Zoom In (+)"

#### **3. Playback Speed Control**
```typescript
interface PlaybackSpeedProps {
  currentSpeed: number;
  onSpeedChange: (speed: number) => void;
  availableSpeeds?: number[];
}
```

**Speed Button:**
- **Display**: Current speed (e.g., "x1.0", "x2.0", "x0.5")
- **Interaction**: Dropdown menu with speed options
- **Available Speeds**: [0.25x, 0.5x, 1x, 1.5x, 2x, 4x]
- **Default**: 1x (normal speed)
- **Behavior**: Affects timeline playback rate and video synchronization

### Enhanced Timeline Header

**Header Layout with Options:**
```
┌─────────────────────────────────────────────────────────────────┐
│ 🔄 Neuromonitoring Timeline    Events: 14  Modalities: 7  [Options ▼] │
└─────────────────────────────────────────────────────────────────┘
```

**Options Dropdown Menu:**
```
Options ▼
├── 🎹 Show Keyboard Info
├── ─────────────────────
├── 🔄 Enable/Disable Editing
├── ─────────────────────
├── 📥 Export Timeline
├── 📤 Import Timeline
├── ─────────────────────
└── 💾 Save Session
```

### Auto-Scroll Implementation

#### **Scroll-to-Event Functionality**
```typescript
const handleScrollToTime = (time: number) => {
  // Calculate pixel position based on timeline scale
  const pixelsPerSecond = 100 / timelineScale;
  const targetPixelX = time * pixelsPerSecond;

  // Center the target time in viewport
  const viewportWidth = 800; // Configurable
  const centeredScrollLeft = targetPixelX - viewportWidth / 2;
  const clampedScrollLeft = Math.max(0, centeredScrollLeft);

  // Programmatically scroll timeline
  timelineRef.current?.setScrollLeft(clampedScrollLeft);
};
```

**Auto-scroll Behavior:**
- **Event Navigation**: Automatically centers target events in viewport
- **Smooth Scrolling**: Uses timeline's native scroll methods
- **Boundary Handling**: Prevents scrolling beyond timeline limits
- **Visual Feedback**: Timeline cursor moves to target time

### Timeline Scrolling Specifications

#### **CSS Overflow Settings**
```css
.timeline-editor-edit-area {
  overflow-x: auto !important; /* Enable horizontal scrolling */
  overflow-y: hidden !important; /* Disable vertical scrolling */
}

.timeline-editor-edit-area .ReactVirtualized__Grid {
  overflow-x: auto !important; /* Enable timeline navigation */
  overflow-y: hidden !important; /* Disable vertical scrolling */
}

.timeline-editor-edit-area .ReactVirtualized__Grid::-webkit-scrollbar {
  width: 0px; /* Hide vertical scrollbar */
  height: 8px; /* Show horizontal scrollbar for timeline scrubbing */
}
```

**Scrolling Behavior:**
- **Horizontal Scrolling**: Enabled for timeline navigation
- **Vertical Scrolling**: Disabled (timeline sized to content)
- **Mouse Wheel**: Horizontal scrolling support
- **Touch Scrolling**: Mobile-friendly horizontal scrolling
- **Edge Dragging**: Cursor dragging near edges auto-scrolls

### Control States and Feedback

#### **Button States**
```typescript
interface ControlButtonState {
  enabled: boolean;
  loading?: boolean;
  active?: boolean;
  tooltip: string;
}
```

**State Specifications:**
- **Enabled**: Full opacity, interactive cursor
- **Disabled**: 50% opacity, not-allowed cursor
- **Active**: Highlighted background (play/pause button)
- **Loading**: Spinner overlay for async operations

#### **Visual Feedback**
- **Hover States**: Light background highlight
- **Click Feedback**: Brief scale animation
- **Keyboard Focus**: Blue outline ring
- **Tooltips**: Contextual help text on hover

### Keyboard Shortcuts Integration

**Supported Shortcuts:**
- **Space**: Toggle play/pause
- **Left Arrow**: Previous event
- **Right Arrow**: Next event
- **Plus (+)**: Zoom in
- **Minus (-)**: Zoom out
- **1-6**: Set playback speed (1=0.25x, 2=0.5x, 3=1x, 4=1.5x, 5=2x, 6=4x)

### Mobile Responsiveness

#### **Mobile Layout Adjustments**
```
┌─────────────────────────────────────────┐
│  ⏮️ ▶️ ⏭️                               │
│  ➖ ➕                                   │
│  [x1.0 Speed]                          │
└─────────────────────────────────────────┘
```

**Mobile Specifications:**
- **Vertical Stacking**: Controls stack vertically on narrow screens
- **Larger Touch Targets**: 44px minimum touch target size
- **Simplified Layout**: Reduced spacing and padding
- **Essential Controls Only**: Hide advanced features on small screens

This completes the detailed frontend specifications for the enhanced timeline controls. The next section covers patient detail pages and settings interfaces.

---

## 👤 Patient Detail Page

### Patient Information Layout

```
┌────────────────────────────────────────────────────────────────────────────────────┐
│ ← Back to Projects                    Patient: Smith, John (#P2024-001)            │
├────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                    │
│ ┌─ Patient Overview ──────────────────┐ ┌─ Current Project ──────────────────────┐ │
│ │ 👤 John Smith                       │ │ 🏥 Spinal Fusion Surgery              │ │
│ │ 📅 DOB: 1985-03-15 (39 years)       │ │ 👨‍⚕️ Dr. Johnson (Primary)             │ │
│ │ 🆔 Hospital ID: P2024-001           │ │ 🏠 OR 12                               │ │
│ │ ⚧ Male                              │ │ 📅 Today, 14:30 (Scheduled)           │ │
│ │ ☎️ (555) 123-4567                   │ │ ⏱️ Est. Duration: 3-4 hours           │ │
│ │ 🏠 123 Main St, City                │ │ 📋 Status: Pre-operative              │ │
│ │                                     │ │                                        │ │
│ │ 🩺 Diagnosis:                       │ │ [📊 View Live] [📝 Edit] [📋 Report]   │ │
│ │ L4-L5 disc herniation with          │ └────────────────────────────────────────┘ │
│ │ radiculopathy                       │                                          │ │
│ │                                     │ ┌─ Quick Actions ────────────────────────┐ │
│ │ 🚨 Allergies: Penicillin, Latex     │ │ [📋 New Pre-op Exam]                  │ │
│ │ 💊 Medications: Gabapentin 300mg    │ │ [📊 View Previous Surgeries]           │ │
│ └─────────────────────────────────────┘ │ [📄 Upload Documents]                  │ │
│                                        │ [📞 Contact Emergency Contact]          │ │
│                                        └────────────────────────────────────────┘ │
│                                                                                    │
│ ┌─ Examination History ──────────────────────────────────────────────────────────┐ │
│ │                                                                                │ │
│ │ Tabs: [📋 All Exams] [🔍 Pre-op] [📊 Post-op] [📁 Documents] [📈 Trends]     │ │
│ │                                                                                │ │
│ │ ┌─ Pre-operative Examination - March 15, 2024 ──────────────────────────────┐ │ │
│ │ │ Examiner: Dr. Smith                                            ⭐ Baseline │ │ │
│ │ │                                                                            │ │ │
│ │ │ 🧠 Cranial Nerves:                     💪 Motor Function:                  │ │ │
│ │ │ CN I-XII: All normal                   Right leg: 5/5 all groups          │ │ │
│ │ │ ✅ No deficits noted                   Left leg: 4/5 dorsiflexion          │ │ │
│ │ │                                        🚨 Weakness noted                   │ │ │
│ │ │ 👋 Sensory Function:                   🔧 Reflexes:                        │ │ │
│ │ │ L4: Decreased sensation                Patellar: 2+ bilateral              │ │ │
│ │ │ L5: Normal                             Achilles: 1+ left, 2+ right        │ │ │
│ │ │ S1: Normal                             🚨 Asymmetric                       │ │ │
│ │ │                                                                            │ │ │
│ │ │ 📝 Notes: Patient reports increasing pain and weakness over 6 months...   │ │ │
│ │ │                                                                            │ │ │
│ │ │ [📊 View Details] [📄 Print] [✏️ Edit] [📋 Copy to New Exam]             │ │ │
│ │ └────────────────────────────────────────────────────────────────────────────┘ │ │
│ │                                                                                │ │
│ │ ┌─ Post-operative Examination - March 15, 2024 (Expected) ──────────────────┐ │ │
│ │ │ Status: ⏳ Pending (Surgery in progress)                                   │ │ │
│ │ │ Expected completion: ~18:30                                                │ │ │
│ │ │ [📋 Schedule Post-op Exam] [⏰ Set Reminder]                              │ │ │
│ │ └────────────────────────────────────────────────────────────────────────────┘ │ │
│ │                                                                                │ │
│ └────────────────────────────────────────────────────────────────────────────────┘ │
└────────────────────────────────────────────────────────────────────────────────────┘
```

### Clinical Examination Interface

**Pre/Post-op Examination Form:**

```
┌─ Clinical Examination ─────────────────────────────────────────────────────────────┐
│ Patient: John Smith (#P2024-001)                   Type: [Pre-op ▼] Post-op       │
│ Examiner: Dr. Johnson                               Date: March 15, 2024 14:15     │
├────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                    │
│ ┌─ Cranial Nerve Examination ───────────────────────────────────────────────────┐ │
│ │                                                                                │ │
│ │ 🧠 Click on nerve to assess:                                                  │ │
│ │                                                                                │ │
│ │     👁️ CN II (Optic)         [✅ Normal] [⚠️ Abnormal] [⚪ Not Tested]       │ │
│ │     👁️ CN III (Oculomotor)   [✅ Normal] [⚠️ Abnormal] [⚪ Not Tested]       │ │
│ │     👁️ CN IV (Trochlear)     [✅ Normal] [⚠️ Abnormal] [⚪ Not Tested]       │ │
│ │     👁️ CN V (Trigeminal)     [✅ Normal] [⚠️ Abnormal] [⚪ Not Tested]       │ │
│ │     👁️ CN VI (Abducens)      [✅ Normal] [⚠️ Abnormal] [⚪ Not Tested]       │ │
│ │     👂 CN VII (Facial)       [✅ Normal] [⚠️ Abnormal] [⚪ Not Tested]       │ │
│ │     👂 CN VIII (Acoustic)    [✅ Normal] [⚠️ Abnormal] [⚪ Not Tested]       │ │
│ │     👅 CN IX (Glossopharyngeal) [✅ Normal] [⚠️ Abnormal] [⚪ Not Tested]    │ │
│ │     🗣️ CN X (Vagus)          [✅ Normal] [⚠️ Abnormal] [⚪ Not Tested]       │ │
│ │     🤷 CN XI (Accessory)     [✅ Normal] [⚠️ Abnormal] [⚪ Not Tested]       │ │
│ │     👅 CN XII (Hypoglossal)  [✅ Normal] [⚠️ Abnormal] [⚪ Not Tested]       │ │
│ │                                                                                │ │
│ │ 📝 CN Notes: ____________________________________________________________     │ │
│ └────────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                    │
│ ┌─ Motor Function Assessment ────────────────────────────────────────────────────┐ │
│ │                                                                                │ │
│ │ 💪 Click on muscle group to assess (MRC Scale 0-5):                           │ │
│ │                                                                                │ │
│ │ Right Side:                          Left Side:                               │ │
│ │ Hip Flexors:     [5▼] Normal        Hip Flexors:     [4▼] Good                │ │
│ │ Knee Extension:  [5▼] Normal        Knee Extension:  [5▼] Normal              │ │
│ │ Dorsiflexion:    [5▼] Normal        Dorsiflexion:    [3▼] Fair ⚠️             │ │
│ │ Plantarflexion:  [5▼] Normal        Plantarflexion:  [4▼] Good                │ │
│ │ EHL:             [5▼] Normal        EHL:             [2▼] Poor ⚠️              │ │
│ │                                                                                │ │
│ │ 📊 Motor Score: Right 25/25 (100%)  Left 18/25 (72%) ⚠️                      │ │
│ │                                                                                │ │
│ │ 📝 Motor Notes: Left foot weakness, difficulty with toe extension...          │ │
│ └────────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                    │
│ ┌─ Sensory Function Assessment ──────────────────────────────────────────────────┐ │
│ │                                                                                │ │
│ │ 👋 Click on dermatome to assess:                                              │ │
│ │                                                                                │ │
│ │ [Human Body Diagram - Interactive SVG]                                        │ │
│ │                                                                                │ │
│ │ L2: [✅ Normal] [⚠️ Decreased] [🚫 Absent] [⚡ Hyperesthetic]                  │ │
│ │ L3: [✅ Normal] [⚠️ Decreased] [🚫 Absent] [⚡ Hyperesthetic]                  │ │
│ │ L4: [⚠️ Decreased] [⚠️ Decreased] [🚫 Absent] [⚡ Hyperesthetic] ⚠️           │ │
│ │ L5: [✅ Normal] [⚠️ Decreased] [🚫 Absent] [⚡ Hyperesthetic]                  │ │
│ │ S1: [✅ Normal] [⚠️ Decreased] [🚫 Absent] [⚡ Hyperesthetic]                  │ │
│ │                                                                                │ │
│ │ 📝 Sensory Notes: L4 dermatome decreased sensation both sides...               │ │
│ └────────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                    │
│ ┌─ Overall Assessment ───────────────────────────────────────────────────────────┐ │
│ │                                                                                │ │
│ │ 📊 Overall Status: [⚠️ Abnormal ▼] Normal / Abnormal / Critical                │ │
│ │                                                                                │ │
│ │ 📝 Summary Notes:                                                              │ │
│ │ ┌──────────────────────────────────────────────────────────────────────────┐   │ │
│ │ │ Patient shows baseline neurological deficits consistent with L4-L5       │   │ │
│ │ │ disc herniation. Left lower extremity weakness in EHL and dorsiflexion  │   │ │
│ │ │ noted. Sensory deficits in L4 distribution bilaterally...               │   │ │
│ │ └──────────────────────────────────────────────────────────────────────────┘   │ │
│ │                                                                                │ │
│ │ 📋 Recommendations:                                                            │ │
│ │ ☐ Monitor motor function closely during surgery                                │ │
│ │ ☐ Baseline MEP responses may be abnormal                                       │ │
│ │ ☐ Post-op comparison to current baseline                                       │ │
│ │                                                                                │ │
│ │ [💾 Save Draft] [✅ Complete & Save] [📄 Print] [📧 Share with Team]          │ │
│ └────────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                    │
└────────────────────────────────────────────────────────────────────────────────────┘
```

**Interactive Elements Specifications:**

1. **MRC Scale Dropdown**:
   - 0: No movement
   - 1: Flicker of movement
   - 2: Movement with gravity eliminated
   - 3: Movement against gravity
   - 4: Movement against some resistance
   - 5: Normal strength
   - Color coding: 0-2 (red), 3-4 (yellow), 5 (green)

2. **Dermatome Body Diagram**:
   - Interactive SVG with clickable regions
   - Color coding for sensation levels
   - Bilateral assessment capability
   - Zoom functionality for detailed areas

3. **Cranial Nerve Quick Assessment**:
   - One-click normal/abnormal/not tested
   - Visual icons for each nerve function
   - Bulk actions: "Mark all normal" button
   - Abnormal findings trigger note requirement

4. **Auto-calculations**:
   - Motor scores with percentage
   - Overall assessment suggestions
   - Deficit highlighting and warnings
   - Comparison to previous exams

---

## ⚙️ Settings & Configuration Pages

### Settings Navigation

```
┌─ Settings ──────────────────────────────────────────────────────────────────────┐
│                                                                                 │
│ ┌─ Categories ─┐ ┌─ Configuration Content ──────────────────────────────────────┐ │
│ │              │ │                                                             │ │
│ │ 🏥 General   │ │                Current Settings Page                         │ │
│ │ 👥 Users     │ │                                                             │ │
│ │ 🔧 Modalities│ │                                                             │ │
│ │ 🏠 OR Rooms  │ │                                                             │ │
│ │ 📊 Surgery   │ │                                                             │ │
│ │ 🎨 Interface │ │                                                             │ │
│ │ 📡 Streaming │ │                                                             │ │
│ │ 🔒 Security  │ │                                                             │ │
│ │ 📋 Reports   │ │                                                             │ │
│ │ 🔔 Alerts    │ │                                                             │ │
│ │              │ │                                                             │ │
│ └──────────────┘ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### Modality Configuration Page

```
┌─ Modality Configuration ────────────────────────────────────────────────────────┐
│                                                                                 │
│ Manage monitoring modalities and their event types                              │
│                                                                                 │
│ ┌─ Active Modalities ─────────────────────────────────────────────────────────┐ │
│ │                                                                             │ │
│ │ 🔴 MEP (Motor Evoked Potentials)                              [✏️] [🗑️]   │ │
│ │ Color: Red (#FF0000)    Event Types: 5 configured                          │ │
│ │ ├─ Signal Loss (Critical)                                                  │ │
│ │ ├─ Amplitude Decrease (Warning)                                            │ │
│ │ ├─ Threshold Change (Warning)                                              │ │
│ │ ├─ Stimulation Artifact (Normal)                                           │ │
│ │ └─ Baseline Recording (Normal)                                             │ │
│ │                                                                             │ │
│ │ 🟡 EMG (Electromyography)                                     [✏️] [🗑️]   │ │
│ │ Color: Yellow (#FFFF00)  Event Types: 4 configured                         │ │
│ │ ├─ Spontaneous Activity (Critical)                                         │ │
│ │ ├─ Recruitment Changes (Warning)                                           │ │
│ │ ├─ Stimulation Response (Normal)                                           │ │
│ │ └─ Quiet Period (Normal)                                                   │ │
│ │                                                                             │ │
│ │ 🟢 SSEP (Somatosensory Evoked Potentials)                     [✏️] [🗑️]   │ │
│ │ Color: Green (#00FF00)   Event Types: 3 configured                         │ │
│ │ ├─ Signal Loss (Critical)                                                  │ │
│ │ ├─ Latency Increase (Warning)                                              │ │
│ │ └─ Normal Response (Normal)                                                │ │
│ │                                                                             │ │
│ │                                                [+ Add New Modality]        │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
│ ┌─ Surgery Type Templates ────────────────────────────────────────────────────┐ │
│ │                                                                             │ │
│ │ Templates define which modalities are enabled by default for surgery types │ │
│ │                                                                             │ │
│ │ Spinal Fusion                                                [✏️] [🗑️]   │ │
│ │ ✅ MEP  ✅ EMG  ✅ SSEP  ❌ BAEP  ❌ VEP                                      │ │
│ │                                                                             │ │
│ │ Brain Tumor Resection                                        [✏️] [🗑️]   │ │
│ │ ✅ MEP  ✅ EMG  ❌ SSEP  ✅ BAEP  ❌ VEP                                      │ │
│ │                                                                             │ │
│ │ Cranial Base Surgery                                         [✏️] [🗑️]   │ │
│ │ ❌ MEP  ✅ EMG  ❌ SSEP  ✅ BAEP  ✅ VEP                                      │ │
│ │                                                                             │ │
│ │                                           [+ Add Surgery Template]         │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
│                                          [Save Changes] [Reset] [Export Config] │
└─────────────────────────────────────────────────────────────────────────────────┘
```

This completes the comprehensive frontend specifications for the NFM system. An LLM should now have enough detail to implement all the core interfaces, including layout specifications, component behaviors, styling requirements, and interaction patterns.
