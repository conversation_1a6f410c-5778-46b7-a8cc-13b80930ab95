  pc.ontrack = (event) => {
    console.log('Received track:', event.track.kind);
    const video = document.createElement('video');
    video.srcObject = event.streams[0];
    video.autoplay = true;
    document.body.appendChild(video);
  };

  pc.addTransceiver('video', { direction: 'recvonly' });
  pc.addTransceiver('audio', { direction: 'recvonly' });

  const offer = await pc.createOffer();
  await pc.setLocalDescription(offer);

  const response = await fetch('http://localhost:8889/test_stream/whep', {
    method: 'POST',
    headers: { 'Content-Type': 'application/sdp' },
    body: offer.sdp,
  });

  const answer = await response.text();
  await pc.setRemoteDescription({ type: 'answer', sdp: answer });
}

testWebRTC();
```

### 3. RTSP Stream Testing

```bash
# Test with ffplay
ffplay rtsp://localhost:8554/test_stream

# Test with VLC
vlc rtsp://localhost:8554/test_stream

# Test with GStreamer
gst-launch-1.0 rtspsrc location=rtsp://localhost:8554/test_stream ! decodebin ! autovideosink
```

---

## 🚨 Common Development Issues

### Issue 1: Port Already in Use

**Solution**:
```bash
# Windows - Find process using port
netstat -ano | findstr :8554
taskkill /PID <PID> /F

# Unix - Find and kill process
lsof -i :8554
kill -9 <PID>
```

### Issue 2: WebRTC Connection Fails

**Checklist**:
1. Check MediaMTX is running: `curl http://localhost:9997`
2. Verify stream exists: `curl http://localhost:9997/v3/paths/list`
3. Check browser console for errors
4. Ensure no firewall blocking ports
5. Try disabling browser extensions

### Issue 3: RTSP Source Not Connecting

**Debug Steps**:
1. Test network connectivity: `ping *************`
2. Check RTSP with VLC first
3. Verify credentials if required
4. Try TCP instead of UDP protocol
5. Check MediaMTX logs for errors

### Issue 4: High CPU Usage

**Optimization**:
```yaml
# In mediamtx.yml, adjust encoding settings
paths:
  test_stream:
    runOnInit: >
      ffmpeg -f lavfi -i testsrc2=size=1280x720:rate=15
      -c:v libx264 -preset fast -b:v 1000k
      -f rtsp rtsp://localhost:8554/test_stream
```

---

## 📊 Performance Monitoring

### Development Metrics Script

Create `mediamtx/monitor.js`:

```javascript
const http = require('http');

function checkMediaMTX() {
  // Check API
  http.get('http://localhost:9997/v3/paths/list', (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      const paths = JSON.parse(data);
      console.log(`Active streams: ${Object.keys(paths.items).length}`);
      
      Object.entries(paths.items).forEach(([name, info]) => {
        console.log(`- ${name}: ${info.readers?.length || 0} viewers`);
      });
    });
  }).on('error', (err) => {
    console.error('MediaMTX API not accessible:', err.message);
  });

  // Check Metrics
  http.get('http://localhost:9998/metrics', (res) => {
    let data = '';
    res.on('data', chunk => data += chunk);
    res.on('end', () => {
      const lines = data.split('\n');
      const rtspConns = lines.find(l => l.includes('rtsp_conns'));
      const webrtcConns = lines.find(l => l.includes('webrtc_conns'));
      
      if (rtspConns) console.log(rtspConns);
      if (webrtcConns) console.log(webrtcConns);
    });
  });
}

// Check every 5 seconds
setInterval(checkMediaMTX, 5000);
checkMediaMTX();
```

---

## 🔄 Next Steps

After MediaMTX is running:

1. **Test WebRTC streaming**:
   - Start MediaMTX
   - Open http://localhost:8889/test_stream in browser
   - Verify video plays

2. **Integrate with Next.js**:
   - Create WebRTC player component
   - Add stream management to Convex
   - Build video controls UI

3. **Configure for Inomed**:
   - Get actual RTSP URLs from hospital
   - Set up authentication if required
   - Test with real equipment

---

**Development Setup Version**: 1.0  
**Compatible With**: MediaMTX 1.6.0+, Windows/macOS/Linux  
**Last Updated**: May 2025