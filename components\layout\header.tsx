import { Separator } from "../ui/separator"
import { SidebarTrigger } from "../ui/sidebar"
import { MobileNav } from "./mobile-nav"
import { RightSidebarTrigger } from "./right-sidebar-trigger"

interface HeaderProps {
  activeSessionsCount?: number
  pageTitle?: string
  pageSubtitle?: string
  isLive?: boolean
  onToggleRightSidebar?: () => void
  isRightSidebarOpen?: boolean
  showRightSidebarTrigger?: boolean
}

export function Header({
  activeSessionsCount = 0,
  pageTitle,
  pageSubtitle,
  isLive = false,
  onToggleRightSidebar,
  isRightSidebarOpen = true,
  showRightSidebarTrigger = false
}: HeaderProps) {
  const hospitalName = "Central Hospital"

  return (
    <header className="sticky top-0 flex shrink-0 z-10 items-center gap-2 border-b bg-background backdrop-blur-sm shadow-sm">
       {/*<div className="flex h-16 items-center justify-between px-4 lg:px-6">*/}
        {/* Left: Mobile Navigation + Hospital Branding OR Page Context */}
        <div className="flex flex-1 items-center gap-2 px-3">
          <MobileNav activeSessionsCount={activeSessionsCount} />
          <SidebarTrigger />
          <Separator orientation="vertical" className="mr-2 h-4" />
          {pageTitle ? (
            // Page context mode
            <div className="flex items-center space-x-3 ">
            
              <div className=" border-gray-200 pl-3">
                <div className="flex items-center gap-2">
                  <h1 className="font-semibold text-lg">{pageTitle}</h1>
                  {isLive && (
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-xs font-medium text-red-600 uppercase tracking-wide">LIVE</span>
                    </div>
                  )}
                </div>
                {pageSubtitle && (
                  <p className="text-sm text-muted-foreground">{pageSubtitle}</p>
                )}
              </div>
            </div>
          ) : (
            // Default hospital branding mode
            <div className="flex items-center space-x-3">
              <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-sm">NH</span>
              </div>
              <div>
                <h1 className="font-semibold text-lg text-gray-900">{hospitalName}</h1>
                <p className="text-sm text-muted-foreground">Neural Function Monitor</p>
              </div>
            </div>
          )}
        </div>

        {/* Right Sidebar Toggle */}
       
          {showRightSidebarTrigger && onToggleRightSidebar && (
            <RightSidebarTrigger
              onToggleCollapse={onToggleRightSidebar}
              isOpen={isRightSidebarOpen}
            />
          )}
      
    </header>
  )
}