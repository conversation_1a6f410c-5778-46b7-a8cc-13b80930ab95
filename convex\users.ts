import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { requireAuth } from "./auth";
import { Users, userRoleValidator } from "./schema";
import { omit, pick } from "convex-helpers";

// Type-safe field selectors
const userCreateFields = omit(Users.withoutSystemFields, ["lastLogin"]);
const userUpdateFields = pick(Users.withoutSystemFields, [
  "name", "role", "specialization", "license", "credentials", "department", "phone", "isActive"
]);

// Get all users
export const getUsers = query({
  args: {
    role: userRoleValidator,
    isActive: v.optional(v.boolean()),
  },
  returns: v.array(Users.doc),
  handler: async (ctx, args) => {
    await requireAuth(ctx, ["admin", "surgeon", "neurophysiologist"]);
    
    let users;
    
    if (args.role && args.isActive !== undefined) {
      users = await ctx.db.query("users")
        .withIndex("by_role", (q) => q.eq("role", args.role))
        .filter((q) => q.eq(q.field("isActive"), args.isActive))
        .collect();
    } else if (args.role) {
      users = await ctx.db
        .query("users")
        .withIndex("by_role", (q) => q.eq("role", args.role))
        .collect();
    } else if (args.isActive !== undefined) {
      users = await ctx.db
        .query("users")
        .withIndex("by_active", (q) => q.eq("isActive", args.isActive))
        .collect();
    } else {
      users = await ctx.db.query("users").collect();
    }
    
    return users;
  },
});

// Get a single user by ID
export const getUser = query({
  args: { userId: v.id("users") },
  returns: v.union(Users.doc, v.null()),
  handler: async (ctx, args) => {
    // Don't require auth for getting user data - this is needed for context
    const user = await ctx.db.get(args.userId);
    return user || null;
  },
});


// Get users by role for team assignment
export const getUsersByRole = query({
  args: { roles: v.array(userRoleValidator) },
  returns: v.array(Users.doc),
  handler: async (ctx, args) => {
    await requireAuth(ctx);
    
    const users = await ctx.db
      .query("users")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .collect();
    //Convex queries do not natively support filtering by "any of" a set
    return users.filter(user => args.roles.includes(user!.role!));
  },
});

// Update user information (admin OR the user themselves)
export const updateUser = mutation({
  args: {
    userId: v.id("users"),
    updates: v.object(userUpdateFields),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const currentUser = await requireAuth(ctx);
    
    // Allow if user is admin OR updating their own profile
    if (currentUser.role !== "admin" && currentUser._id !== args.userId) {
      throw new Error("Not authorized to update this user");
    }
    
    // If non-admin user is updating themselves, restrict what they can change
    if (currentUser.role !== "admin" && currentUser._id === args.userId) {
      // Users can only update certain fields about themselves
      const allowedSelfUpdateFields = ["name", "specialization", "license", "department", "phone"];
      const updateKeys = Object.keys(args.updates);
      const restrictedFields = updateKeys.filter(key => !allowedSelfUpdateFields.includes(key));
      
      if (restrictedFields.length > 0) {
        throw new Error(`Users cannot update these fields: ${restrictedFields.join(", ")}`);
      }
    }
    
    await ctx.db.patch(args.userId, args.updates);
    
    return { success: true };
  },
});

// Update user preferences (users can update their own preferences)
export const updateUserPreferences = mutation({
  args: {
    preferences: v.object({
      defaultModalities: v.optional(v.array(v.string())),
      notificationSettings: v.optional(v.object({
        email: v.boolean(),
        inApp: v.boolean(),
        criticalEvents: v.boolean(),
      })),
      uiPreferences: v.optional(v.object({
        theme: v.union(v.literal("light"), v.literal("dark")),
        timelineView: v.union(v.literal("compact"), v.literal("expanded")),
        defaultTimeScale: v.number(),
      })),
    }),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    const user = await requireAuth(ctx);
    
    // Merge new preferences with existing ones
    const currentPreferences = user.preferences;
    const updatedPreferences = {
      defaultModalities: args.preferences.defaultModalities ?? currentPreferences!.defaultModalities,
      notificationSettings: args.preferences.notificationSettings ?? currentPreferences!.notificationSettings,
      uiPreferences: args.preferences.uiPreferences ?? currentPreferences!.uiPreferences,
    };
    
    await ctx.db.patch(user._id, {
      preferences: updatedPreferences,
    });
    
    return { success: true };
  },
});

// Create a new user (admin only)
export const createUser = mutation({
  args: userCreateFields,
  returns: v.object({ userId: v.id("users") }),
  handler: async (ctx, args) => {
    await requireAuth(ctx, ["admin"]);
    
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email!))
      .first();
      
    if (existingUser) {
      throw new Error("User with this email already exists");
    }
    
    const userId = await ctx.db.insert("users", {
      ...args,
      preferences: {
        defaultModalities: [],
        notificationSettings: {
          email: true,
          inApp: true,
          criticalEvents: true,
        },
        uiPreferences: {
          theme: "light",
          timelineView: "expanded",
          defaultTimeScale: 60,
        },
      },
      isActive: true,
      createdAt: Date.now(),
    });
    
    return { userId };
  },
});

// Deactivate user (soft delete)
export const deactivateUser = mutation({
  args: { userId: v.id("users") },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    await requireAuth(ctx, ["admin"]);
    
    await ctx.db.patch(args.userId, {
      isActive: false,
    });
    
    return { success: true };
  },
});

// Reactivate user
export const reactivateUser = mutation({
  args: { userId: v.id("users") },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, args) => {
    await requireAuth(ctx, ["admin"]);
    
    await ctx.db.patch(args.userId, {
      isActive: true,
    });
    
    return { success: true };
  },
});
