"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import {
  Clock,
  Users,
  Mic,
  MonitorSpeaker,
  Play,
  Square,
  Activity,
  Volume2,
  Filter,
  Download,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  ChevronDown
} from "lucide-react"
import { useIsMobile } from "@/hooks/use-mobile"
import { Carousel, CarouselContent, CarouselItem } from "../ui/carousel"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "../ui/accordion"

interface LiveMonitoringContentProps {
  isCollapsed: boolean
}

// Mock data for demonstration
const mockEvents = [
  {
    id: "1",
    type: "MEP Loss",
    modality: "MEP",
    timestamp: "14:28:45",
    location: "L5 nerve root",
    severity: "critical" as const,
    reviewer: "<PERSON><PERSON> <PERSON>",
    status: "unreviewed" as const,
    icon: "🔴"
  },
  {
    id: "2",
    type: "EMG Alert",
    modality: "EMG",
    timestamp: "14:32:15",
    location: "Tibialis anterior",
    severity: "warning" as const,
    reviewer: "Unreviewed",
    status: "unreviewed" as const,
    icon: "🟡"
  },
  {
    id: "3",
    type: "SSEP Normal",
    modality: "SSEP",
    timestamp: "14:35:30",
    location: "Routine check",
    severity: "normal" as const,
    reviewer: "Dr. Smith",
    status: "reviewed" as const,
    icon: "🟢"
  }
]

export function LiveMonitoringContent({ isCollapsed }: LiveMonitoringContentProps) {
  const [isRecordingSession] = useState(true)
  const [sessionDuration] = useState("02:45:30")
  const isMobile = useIsMobile()

  if (isMobile || !isCollapsed) {
    return (

      
        <div className="mb-4 px-2">
          <Accordion type="multiple" defaultValue={["session-control"]}>
            <AccordionItem value="session-control">
              <AccordionTrigger >
                Session Control
              </AccordionTrigger>
              <AccordionContent>
                <Carousel orientation="vertical" className="">
                  <CarouselContent className="flex flex-col gap-4">
                    {/* Session Controls */}
                    <CarouselItem>
                      <Card className="border-sidebar-border bg-sidebar-accent/50">
                        <CardHeader className="pb-2">
                          <CardTitle className="flex items-center gap-2 text-sm">
                            <Clock className="h-4 w-4" />
                            Session Status
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          {isRecordingSession ? (
                            <>
                              <div className="flex items-center justify-center">
                                <Badge variant="destructive" className="animate-pulse text-xs">
                                  🔴 RECORDING
                                </Badge>
                              </div>
                              <div className="text-center">
                                <div className="text-lg font-mono font-bold">{sessionDuration}</div>
                                <div className="text-xs text-muted-foreground">Duration</div>
                              </div>
                              <Button variant="destructive" size="sm" className="w-full">
                                <Square className="h-3 w-3 mr-2" />
                                Stop Session
                              </Button>
                            </>
                          ) : (
                            <Button variant="default" size="sm" className="w-full">
                              <Play className="h-3 w-3 mr-2" />
                              Start Session
                            </Button>
                          )}
                        </CardContent>
                      </Card>
                    </CarouselItem>
                    {/* Team Information */}
                    <CarouselItem>
                      <Card className="border-sidebar-border bg-sidebar-accent/50">
                        <CardHeader className="pb-2">
                          <CardTitle className="flex items-center gap-2 text-sm">
                            <Users className="h-4 w-4" />
                            Team Information
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2 text-xs">
                          <div>
                            <div className="font-medium text-muted-foreground uppercase tracking-wide">Surgery</div>
                            <div className="text-sm">Spinal Fusion Surgery</div>
                          </div>
                          <div>
                            <div className="font-medium text-muted-foreground uppercase tracking-wide">Patient</div>
                            <div className="text-sm">Test Patient</div>
                          </div>
                          <div>
                            <div className="font-medium text-muted-foreground uppercase tracking-wide">Status</div>
                            <Badge variant="default" className="text-xs">in-progress</Badge>
                          </div>
                        </CardContent>
                      </Card>
                    </CarouselItem>
                    {/* Audio Controls */}
                    <CarouselItem>
                      <Card className="border-sidebar-border bg-sidebar-accent/50">
                        <CardHeader className="pb-2">
                          <CardTitle className="flex items-center gap-2 text-sm">
                            <Mic className="h-4 w-4" />
                            Audio Controls
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-xs">OR Audio</span>
                            <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                              <MonitorSpeaker className="h-3 w-3" />
                            </Button>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-xs">Team Comm</span>
                            <Button variant="outline" size="sm" className="h-6 w-6 p-0">
                              <Mic className="h-3 w-3" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    </CarouselItem>
                    {/* Session Stats */}
                    <CarouselItem>
                      <Card className="border-sidebar-border bg-sidebar-accent/50">
                        <CardHeader className="pb-2">
                          <CardTitle className="flex items-center gap-2 text-sm">
                            <Activity className="h-4 w-4" />
                            Session Stats
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-2 text-xs">
                          <div className="flex justify-between">
                            <span>Events:</span>
                            <span className="font-medium">{mockEvents.length}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Alerts:</span>
                            <span className="font-medium text-orange-600">
                              {mockEvents.filter(e => e.severity === 'warning').length}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Critical:</span>
                            <span className="font-medium text-red-600">
                              {mockEvents.filter(e => e.severity === 'critical').length}
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    </CarouselItem>
                  </CarouselContent>
                </Carousel>
              </AccordionContent>
            </AccordionItem>
            <AccordionItem value="event-log">
              <AccordionTrigger>
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-sm">Event Log</h4>
                 
                </div>
              </AccordionTrigger>
              <AccordionContent>
                < div className="border-t border-sidebar-border" >
                  <div className="p-2">


                    <ScrollArea className="">
                      <div className="space-y-2">
                        {mockEvents.map((event, index) => (
                          <div key={event.id}>
                            <div className="p-2 rounded-md hover:bg-sidebar-accent/50 cursor-pointer transition-colors">
                              <div className="flex items-center justify-between text-xs">
                                <span className="flex items-center gap-1">
                                  <span>{event.icon}</span>
                                  <span className="font-medium">{event.type}</span>
                                </span>
                                <span className="text-muted-foreground">{event.timestamp}</span>
                              </div>
                              <div className="flex items-center justify-between mt-1">
                                <span className="text-xs text-muted-foreground">{event.location}</span>
                                <Badge
                                  variant={
                                    event.severity === 'critical' ? 'destructive' :
                                      event.severity === 'warning' ? 'secondary' : 'default'
                                  }
                                  className="text-xs h-4"
                                >
                                  {event.severity === 'critical' && <AlertTriangle className="h-2 w-2 mr-1" />}
                                  {event.severity === 'warning' && <XCircle className="h-2 w-2 mr-1" />}
                                  {event.severity === 'normal' && <CheckCircle className="h-2 w-2 mr-1" />}
                                  {event.severity}
                                </Badge>
                              </div>
                              <div className="flex items-center justify-between mt-1">
                                <span className="text-xs text-muted-foreground">{event.reviewer}</span>
                                <Button variant="outline" size="sm" className="h-5 text-xs px-2">
                                  <Eye className="h-2 w-2 mr-1" />
                                  Review
                                </Button>
                              </div>
                            </div>
                            {index < mockEvents.length - 1 && <Separator className="my-1" />}
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </div >
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>


    )
  }

  return (
    <div className="flex flex-col items-center gap-4 p-2">
      {/* Session Status Icon */}
      <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
        {isRecordingSession ? (
          <Square className="size-4 text-red-500" />
        ) : (
          <Play className="size-4" />
        )}
      </div>

      {/* Team Icon */}
      <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-accent text-sidebar-accent-foreground">
        <Users className="size-4" />
      </div>

      {/* Audio Icon */}
      <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-accent text-sidebar-accent-foreground">
        <Volume2 className="size-4" />
      </div>

      {/* Event Log Icon */}
      <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-accent text-sidebar-accent-foreground">
        <Activity className="size-4" />
      </div>
    </div>
  )
}

