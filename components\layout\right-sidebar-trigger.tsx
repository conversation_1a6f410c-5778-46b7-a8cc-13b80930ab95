"use client"

import { PanelRight, PanelRightClose } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useIsMobile } from "@/hooks/use-mobile"

interface RightSidebarTriggerProps {
  onToggleCollapse: () => void
  isOpen: boolean
}

export function RightSidebarTrigger({
  onToggleCollapse,
  isOpen,
}: RightSidebarTriggerProps) {
  const isMobile = useIsMobile()

  if (isMobile) {
    return (
      <Button
        variant="ghost"
        size="icon"
        className="h-7 w-7"
        onClick={onToggleCollapse}
        title={isMobile && isOpen ? "Close Right Sidebar" : "Open Right Sidebar"}
      >
        <PanelRight />
        <span className="sr-only">Toggle Right Sidebar</span>
      </Button>
    )
  }
  return (
    <Button
      variant="ghost"
      size="icon"
      className="h-7 w-7 pr-2"
      onClick={onToggleCollapse}
      title={isOpen ? "Collapse Right Sidebar" : "Expand Right Sidebar"}
    >
      {isOpen ? <PanelRightClose /> : <PanelRight />}
      <span className="sr-only">Toggle Right Sidebar Collapse</span>
    </Button>
  )
}
